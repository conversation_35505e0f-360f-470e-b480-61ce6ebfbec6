"use client";;
import { useAppPathname } from '@hooks/router-hooks';
import NestedMenuSection from '@components/nested-menu-section/nested-menu-section';
import useNavigationMenu from '@providers/navigation-menu/navigation-menu-provider';
import Page from '@components/layout/containers/page';

import type { JSX } from "react";

export default function LevelTwoNav(): JSX.Element {
  const pathname = useAppPathname();
  const { permissibleMenus, getPermissableMenu } = useNavigationMenu();

  if (!getPermissableMenu || !permissibleMenus) return <></>;

  const menu = getPermissableMenu(pathname, permissibleMenus, "equals");

  return <Page>{!!menu && <NestedMenuSection menus={[menu]} />}</Page>;
}
