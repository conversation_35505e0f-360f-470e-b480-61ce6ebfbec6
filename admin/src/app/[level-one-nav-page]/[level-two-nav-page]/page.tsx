import { AuthorizeServer } from '@app/authorize-server';
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import LevelTwoNav from "./components";

import type { JSX } from "react";

/**
 * Level Two Navigation Page
 *
 * This is a server component that renders the level two navigation page
 */
export default function LevelTwoNavPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <LevelTwoNav />
    </AuthorizeServer>
  );
}
