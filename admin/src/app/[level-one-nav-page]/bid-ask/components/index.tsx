"use client";;
import Page from "@components/layout/containers/page";
import BidAskComponent from "./bid-ask";
import { ResponseType } from "../types/response-type";

import type { JSX } from "react";

const BidAsk = ({ marketOffers }: { marketOffers: ResponseType[] }): JSX.Element => {
  return (
    <Page>
      <BidAskComponent marketOffers={marketOffers} />
    </Page>
  );
};

export default BidAsk;
