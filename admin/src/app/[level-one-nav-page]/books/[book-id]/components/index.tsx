"use client";;
import Page from "@components/layout/containers/page";
import { AdminBookResponse } from "@rubiconcarbon/shared-types";
import BookDetailComponent from "./book-detail";

import type { JSX } from "react";

const BookDetail = ({ books }: { books: AdminBookResponse[] }): JSX.Element => {
  return (
    <Page>
      <BookDetailComponent books={books} />
    </Page>
  );
};

export default BookDetail;
