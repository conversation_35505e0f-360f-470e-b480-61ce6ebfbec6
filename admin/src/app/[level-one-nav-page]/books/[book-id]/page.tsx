import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookQueryResponse,
  AdminBookResponse,
  BookRelations,
  PermissionEnum,
  uuid,
} from "@rubiconcarbon/shared-types";
import BookDetail from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * Book Detail Page
 *
 * This is a server component that renders the Book Detail page
 */
export default async function BookDetailPage({
  params,
}: {
  params: Promise<{ "book-id": string }>;
}): Promise<JSX.Element> {
  const { "book-id": compositeId } = await params;

  const booksResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        includeTotalCount: true,
        ids: compositeId?.split("%2C") as uuid[], // server component route segments encode commas in the url as %2C
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(booksResponse)) return booksResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_READ]}>
      <BookDetail books={(booksResponse as AdminBookQueryResponse).data as AdminBookResponse[]} />
    </AuthorizeServer>
  );
}
