import { CheckRounded, CancelRounded } from "@mui/icons-material";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, I<PERSON>Button, Divider, Box } from "@mui/material";
import { isNothing, Maybe, px, toDecimal } from "@rubiconcarbon/frontend-shared";
import { MouseEvent, useMemo, type JSX } from "react";
import useNavigation from "@hooks/use-navigation";
import useAuth from "@providers/auth-provider";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import useGenericTableRowActions from "@components/ui/generic-table/hooks/use-generic-table-row-actions";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { BookParentGrouping } from "../../../../types/book";
import SearchBookIcon from "@components/icons/search-book-icon";

type ActionsCellProps = {
  row: GenericTableRowModel<BookParentGrouping>;
};

const ActionsCell = ({ row }: ActionsCellProps): JSX.Element => {
  const { user } = useAuth();
  const { pushToPath } = useNavigation();

  const { toggleEdit } = useGenericTableRowActions<BookParentGrouping>();
  const { creating, editing, dirty, submitting, disabled } = useGenericTableRowState<BookParentGrouping>(row);

  const permissions = useMemo(() => user?.permissions, [user?.permissions]);

  const hasPermissionToViewBook = permissions?.includes(PermissionEnum.BOOKS_READ);

  return (
    <Stack direction="row">
      <Maybe condition={disabled || (!creating && !editing)}>
        <Tooltip title={!hasPermissionToViewBook ? "Insufficient permissions" : ""}>
          <Box>
            <IconButton
              color="primary"
              disabled={!hasPermissionToViewBook || disabled || submitting || editing}
              onClick={() => pushToPath(row?.compositeId)}
            >
              <SearchBookIcon
                {...px(
                  {
                    color: (!hasPermissionToViewBook || disabled || submitting || editing) && "rgba(0, 0, 0, 0.26)",
                  },
                  [false],
                )}
              />
            </IconButton>
          </Box>
        </Tooltip>
      </Maybe>
      <Maybe condition={!disabled && (creating || editing)}>
        <IconButton
          color="primary"
          onClick={(event: MouseEvent<HTMLButtonElement>) => event.stopPropagation()}
          type="submit"
          disabled={submitting || (editing && !dirty)}
        >
          <CheckRounded />
        </IconButton>
        <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
        <IconButton color="error" onClick={() => toggleEdit(row)}>
          <CancelRounded />
        </IconButton>
      </Maybe>
    </Stack>
  );
};

export const BOOKS_COLUMNS: GenericTableColumn<BookParentGrouping>[] = [
  {
    field: "name",
    label: "Book",
    editable: false,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "limit.holdingPriceMax",
    label: "Limit",
    type: "money",
    autofocus: true,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    transformDataValue: (value: string) => (!isNothing(value) && !toDecimal(value).isZero() ? value : "No Limit"),
    missingDataValue: "No Limit",
  },
  {
    field: "ownerAllocationsByAssetType.0.groupedPrices.totalPriceAllocated",
    label: "Value",
    type: "money",
    editable: false,
    headerTooltipContent: <>Total value of credits (settled)</>,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "ownerAllocationsByAssetType.0.groupedPrices.totalPricePendingBuy",
    label: "Pending Buy",
    type: "money",
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "ownerAllocationsByAssetType.0.groupedPrices.totalPricePendingSell",
    label: "Pending Sell",
    type: "money",
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "total",
    label: "Total",
    type: "money",
    editable: false,
    headerTooltipContent: <>Value (settled) + (Pending Buy - Pending Sell)(unsettled)</>,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "" as any,
    label: "Details",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
