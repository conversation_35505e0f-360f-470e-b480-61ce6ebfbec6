import { BookTypeToLabel } from "@constants/book-type-to-label";
import { NoNumberFormat } from "@utils/formatters/no-number-format";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { WarningRounded, ErrorOutlineRounded } from "@mui/icons-material";
import {
  Stack,
  Typography,
  TextField,
  MenuItem,
  Autocomplete,
  FormControl,
  FormControlLabel,
  Checkbox,
  Box,
} from "@mui/material";
import {
  isNothing,
  currencyFormat,
  numberFormat,
  Maybe,
  px,
  isEmptyRecord,
  calculator,
  toDecimal,
} from "@rubiconcarbon/frontend-shared";
import {
  uuid,
  BookType,
  AdminAllocationResponse,
  AssetType,
  AdminBookResponse,
  AdminAssetResponse,
} from "@rubiconcarbon/shared-types";
import { useMemo, useEffect, useState, type JSX } from "react";
import { Control, FieldErrors, UseFormSetValue, UseFormWatch, Controller } from "react-hook-form";
import { NumericFormat, NumberFormatValues } from "react-number-format";
import { getAssetTypePrices } from "@utils/helpers/portfolio/book-transformations";
import { TransferAssetsModel } from "../models/transfer-assets";
import { MISSING_DATA } from "@constants/constants";

import classes from "../styles/transfer-asset-item.module.scss";

type TransferAssetItemProps = {
  index: number;
  isSubmitSuccessful: boolean;
  isSubmitting: boolean;
  control: Control<TransferAssetsModel>;
  errors: FieldErrors<TransferAssetsModel>;
  books: AdminBookResponse[];
  bookToVintageAllocations: Record<uuid, AdminAllocationResponse[]>;
  transferableBookTypes: string[];
  bookOptions: UseAutoCompleteOptionsReturnEntry<{ id: uuid; type: BookType }>[];
  setValue: UseFormSetValue<TransferAssetsModel>;
  watch: UseFormWatch<TransferAssetsModel>;
};

const TransferAssetItem = (props: TransferAssetItemProps): JSX.Element => {
  const {
    index,
    isSubmitting,
    isSubmitSuccessful,
    control,
    errors,
    books,
    bookToVintageAllocations,
    transferableBookTypes,
    bookOptions,
    setValue,
    watch,
  } = props;

  const [canHighlightApproval, setCanHighlightApproval] = useState<boolean>(false);
  const [highlightApproval, setHighlightApproval] = useState<boolean>(false);

  const rootAssetItem = watch(`assets.${index}`);

  const selectedSourceBook = useMemo(() => rootAssetItem?.source, [rootAssetItem?.source]);
  const selectedDestinationBook = useMemo(() => rootAssetItem?.destination, [rootAssetItem?.destination]);
  const selectedAsset = useMemo(() => rootAssetItem?.asset, [rootAssetItem?.asset]);
  // const selectedDetailedAsset = selectedAsset?.detailedAsset as TrimmedProjectVintageResponse;
  const selectedSourceType = selectedSourceBook?.type;
  const selectedDestinationType = selectedDestinationBook?.type;
  const selectedAssetId = useMemo(() => rootAssetItem?.assetId, [rootAssetItem?.assetId]);
  const value = useMemo(() => rootAssetItem?.value, [rootAssetItem?.value]);
  const bookLimitBreached = useMemo(() => {
    if (selectedDestinationBook) {
      const { registry_vintage: prices } = getAssetTypePrices(selectedDestinationBook, [AssetType.REGISTRY_VINTAGE]);
      const limit = selectedDestinationBook?.limit?.holdingPriceMax;
      const limited = !isNothing(limit) && !toDecimal(limit).isZero();

      if (limited && !!value) {
        const currentLimit = calculator(
          calculator(prices?.totalPriceAllocated)
            .add(prices?.totalPricePendingBuy)
            .subtract(prices?.totalPricePendingSell)
            .calculate(),
          { parserBlacklist: ["$", ","] },
        )
          .add(value)
          .calculate()
          .toNumber();

        return calculator(currentLimit, { treatNothingAsNaN: true }).isGreaterThan(limit);
      }
    }

    return false;
  }, [selectedDestinationBook, value]);
  const promptForComplainceApproval = useMemo(
    () =>
      BookType.COMPLIANCE_DEFAULT === selectedSourceBook?.type ||
      BookType.COMPLIANCE_DEFAULT === selectedDestinationBook?.type,
    [selectedDestinationBook?.type, selectedSourceBook?.type],
  );
  const promptForRehabApproval = useMemo(
    () => BookType.REHABILITATION_DEFAULT === selectedDestinationBook?.type,
    [selectedDestinationBook?.type],
  );
  const assetIsNotRCTApproved = useMemo(
    () =>
      !!selectedAsset
        ? selectedDestinationBook?.type === BookType.PORTFOLIO_DEFAULT && !selectedAsset?.asset.isRctStandard
        : false,
    [selectedAsset, selectedDestinationBook?.type],
  );
  const assetIsNotBufferApproved = useMemo(
    () =>
      !!selectedAsset && !isNothing(selectedAsset?.asset.riskBufferPercentage)
        ? selectedDestinationBook?.type === BookType.PORTFOLIO_DEFAULT && +selectedAsset?.asset.riskBufferPercentage > 1
        : false,
    [selectedAsset, selectedDestinationBook?.type],
  );
  const hasAssetWarning = useMemo(
    () => assetIsNotRCTApproved || assetIsNotBufferApproved,
    [assetIsNotBufferApproved, assetIsNotRCTApproved],
  );
  const hasOnlyApprovalErrors = useMemo(() => {
    const assetItemErrors = errors?.assets?.[index] || {};
    return (
      !isEmptyRecord(assetItemErrors) &&
      Object.keys(assetItemErrors).every((entry) => ["complainceApproved", "rehabApproved"].includes(entry))
    );
  }, [errors?.assets, index]);

  const vintageOptions = useAutoCompleteOptions<AdminAllocationResponse, uuid>({
    data: bookToVintageAllocations?.[selectedSourceType ?? ("" as any)] || [],
    keys: ["amountAvailable", "currentPrice", "asset"],
    preTransform: (data) => data?.filter((vintage) => vintage?.amountAvailable > 0),
    label: (entry: Partial<AdminAllocationResponse>) => {
      const projectVintage =
        entry?.asset?.type === AssetType.REGISTRY_VINTAGE ? (entry.asset as AdminAssetResponse) : undefined;
      return `${projectVintage?.registryProjectId} - ${projectVintage?.name}  (${projectVintage?.projectVintageName})`;
    },
    displayLabel: (entry) => {
      const projectVintage =
        entry?.asset?.type === AssetType.REGISTRY_VINTAGE ? (entry.asset as AdminAssetResponse) : undefined;
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <Stack>
            <span>
              {projectVintage?.registryProjectId} - {projectVintage?.name}
            </span>
            <Typography variant="body2" color="GrayText">
              {projectVintage?.projectVintageName}
            </Typography>
          </Stack>
          <Typography variant="caption" color="GrayText">
            Price: {currencyFormat(entry?.currentPrice ? +entry?.currentPrice : 0)} | Value:{" "}
            {currencyFormat(calculator(entry?.currentPrice).multiply(entry?.amountAvailable).calculate().toString())} |
            Available: {numberFormat(entry?.amountAvailable ?? "", { fallback: MISSING_DATA })} credits
          </Typography>
        </Stack>
      );
    },
    value: (entry) => {
      return entry?.asset?.id;
    },
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  useEffect(() => {
    const subscription = watch((value = {}, { name }) => {
      switch (name) {
        case `assets.${index}.sourceId`: {
          const sourceId = value?.assets?.at(index)?.sourceId;

          if (sourceId) {
            const destinationId = value?.assets?.at(index)?.destinationId;

            if (destinationId === sourceId) {
              setValue(`assets.${index}.destinationId`, "" as uuid);
            }

            const sourceBook = books?.find(({ id }) => sourceId === id) ?? null;
            setValue(`assets.${index}.source`, sourceBook);
          } else {
            setValue(`assets.${index}.source`, null);
          }

          setValue(`assets.${index}.assetId`, "" as uuid);
          setValue(`assets.${index}.asset`, null);
          break;
        }

        case `assets.${index}.destinationId`: {
          const destinationId = value?.assets?.at(index)?.destinationId;

          if (destinationId) {
            const destinationBook = books?.find(({ id }) => destinationId === id) ?? null;
            setValue(`assets.${index}.destination`, destinationBook);
          } else {
            setValue(`assets.${index}.destination`, null);
          }
          break;
        }

        case `assets.${index}.assetId`: {
          const assetId = value?.assets?.at(index)?.assetId;

          if (assetId && selectedSourceType) {
            const asset = bookToVintageAllocations[selectedSourceType as keyof typeof bookToVintageAllocations]?.find(({ asset }) => asset.id === assetId);
            const amount = value?.assets?.at(index)?.amount;

            setValue(`assets.${index}.asset`, asset || null);

            if (!!asset && !!amount) {
              const price = asset?.currentPrice ? +asset?.currentPrice : 0;

              if (Number(NoNumberFormat(amount)) <= (asset?.amountAvailable || 0)) {
                setValue(
                  `assets.${index}.value`,
                  amount !== ""
                    ? numberFormat(
                        calculator(price, { parserBlacklist: ["$", ","] })
                          .multiply(amount)
                          .calculate()
                          .toString(),
                        {
                          decimalPlaces: 2,
                        },
                      )
                    : "",
                );
              } else {
                setValue(`assets.${index}.amount`, "" as any);
              }
            } else {
              // Clear amount if asset changed but no amount yet
              setValue(`assets.${index}.amount`, "" as any);
            }
          } else {
            setValue(`assets.${index}.asset`, null);
            setValue(`assets.${index}.amount`, "" as any);
          }
          break;
        }

        case `assets.${index}.amount`: {
          const asset = value?.assets?.at(index)?.asset;
          const amount = value?.assets?.at(index)?.amount;

          if (!!asset && !isNothing(amount)) {
            const price = asset?.currentPrice ? +asset?.currentPrice : 0;

            setValue(
              `assets.${index}.value`,
              amount !== ""
                ? numberFormat(
                    calculator(price, { parserBlacklist: ["$", ","] })
                      .multiply(amount)
                      .calculate()
                      .toString(),
                    {
                      decimalPlaces: 2,
                    },
                  )
                : "",
            );
          } else {
            setValue(`assets.${index}.value`, "" as any);
          }
          break;
        }
      }
    });

    return (): void => subscription.unsubscribe();
  }, [bookToVintageAllocations, books, index, selectedSourceType, setValue, watch]);

  useEffect(() => {
    if (isSubmitting && !canHighlightApproval) {
      setCanHighlightApproval(true);
      setHighlightApproval(false);
    }
    if (!isSubmitting && !isSubmitSuccessful && canHighlightApproval && hasOnlyApprovalErrors) {
      setHighlightApproval(true);
      setCanHighlightApproval(false);
    }
  }, [canHighlightApproval, hasOnlyApprovalErrors, isSubmitSuccessful, isSubmitting]);

  return (
    <Stack component={Box} gap={3}>
      {/* Transfer From */}
      <Controller
        name={`assets.${index}.sourceId`}
        control={control}
        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
          <TextField
            select
            label="Transfer From"
            value={value ?? ""}
            InputProps={{
              ref,
            }}
            error={!!errors.assets?.[index]?.sourceId}
            helperText={errors.assets?.[index]?.sourceId?.message}
            {...otherProps}
            fullWidth
          >
            {bookOptions
              .filter((option) => transferableBookTypes.some((type) => option.value?.type === type))
              .map((option) => (
                <MenuItem key={option.value?.id} value={option.value?.id} disabled={option.disabled}>
                  {option?.displayLabel || option.label}
                </MenuItem>
              ))}
          </TextField>
        )}
      />
      {/* Transfer To */}
      <Controller
        name={`assets.${index}.destinationId`}
        control={control}
        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
          <TextField
            select
            label="Transfer To"
            value={value ?? ""}
            InputProps={{ ref }}
            error={!!errors.assets?.[index]?.destinationId}
            helperText={errors.assets?.[index]?.destinationId?.message}
            {...otherProps}
            disabled={!selectedSourceType}
            fullWidth
          >
            {bookOptions
              .filter(({ value }) => selectedSourceBook?.type !== value?.type)
              .map((option) => (
                <MenuItem key={option.value?.id} value={option.value?.id} disabled={option.disabled}>
                  {option?.displayLabel || option.label}
                </MenuItem>
              ))}
          </TextField>
        )}
      />
      {/* Book Limit Warning */}
      <Maybe condition={bookLimitBreached}>
        <Stack className={classes.Warning}>
          <Stack direction="row" alignItems="center" gap={2}>
            <WarningRounded fontSize="small" color="warning" />
            <Typography variant="body2">
              This transfer exceeds the allowed limit for the {selectedDestinationBook?.name} book of{" "}
              {currencyFormat(
                selectedDestinationBook?.limit?.holdingPriceMax ? +selectedDestinationBook.limit.holdingPriceMax : 0,
              )}
            </Typography>
          </Stack>
        </Stack>
      </Maybe>
      {/* Compliance Approval */}
      <Maybe condition={promptForComplainceApproval}>
        <Stack gap={1}>
          <Stack
            className={`${classes.Approval}${highlightApproval && !!errors.assets?.[index]?.complainceApproved ? ` ${classes.ApprovalHighlighted}` : ""}`}
          >
            <Stack direction="row" alignItems="center" gap={2}>
              <ErrorOutlineRounded fontSize="small" color="error" />
              <Typography variant="body2">
                Transferring credits to/from the {BookTypeToLabel[BookType.COMPLIANCE_DEFAULT]?.toLowerCase()} book is
                not allowed without approval.
              </Typography>
            </Stack>
            <Controller
              name={`assets.${index}.complainceApproved`}
              control={control}
              render={({ field: { value, onChange, ...otherProps }, formState: { errors } }): JSX.Element => (
                <FormControl
                  className={classes.ApprovalCheckboxFormControl}
                  error={!!errors.assets?.[index]?.complainceApproved}
                >
                  <FormControlLabel
                    label={
                      <Typography className={classes.ApprovalCheckboxText} variant="body2">
                        I have obtained approval
                      </Typography>
                    }
                    control={<Checkbox checked={value} onChange={onChange} {...otherProps} />}
                  />
                </FormControl>
              )}
            />
          </Stack>
          <Maybe condition={highlightApproval && !!errors.assets?.[index]?.complainceApproved}>
            <Typography className={classes.ApprovalRequiredText} variant="caption">
              Required.
            </Typography>
          </Maybe>
        </Stack>
      </Maybe>
      {/* Rehab Approval */}
      <Maybe condition={promptForRehabApproval}>
        <Stack gap={1}>
          <Stack
            className={`${classes.Approval}${highlightApproval && !!errors.assets?.[index]?.rehabApproved ? ` ${classes.ApprovalHighlighted}` : ""}`}
          >
            <Stack direction="row" alignItems="center" gap={2}>
              <ErrorOutlineRounded fontSize="small" color="error" />
              <Typography variant="body2">
                Transferring credits into the {BookTypeToLabel[BookType.REHABILITATION_DEFAULT]?.toLowerCase()} book
                requires approval.
              </Typography>
            </Stack>
            <Controller
              name={`assets.${index}.rehabApproved`}
              control={control}
              render={({ field: { value, onChange, ...otherProps } }): JSX.Element => (
                <FormControl className={classes.ApprovalCheckboxFormControl}>
                  <FormControlLabel
                    label={
                      <Typography className={classes.ApprovalCheckboxText} variant="body2">
                        I have obtained approval
                      </Typography>
                    }
                    control={<Checkbox checked={value} onChange={onChange} {...otherProps} />}
                  />
                </FormControl>
              )}
            />
          </Stack>
          <Maybe condition={highlightApproval && !!errors.assets?.[index]?.rehabApproved}>
            <Typography className={classes.ApprovalRequiredText} variant="caption">
              Required.
            </Typography>
          </Maybe>
        </Stack>
      </Maybe>
      {/* Vintage */}
      <Controller
        name={`assets.${index}.assetId`}
        control={control}
        render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
          const selectedOption = vintageOptions.find((entry) => entry.value === value) ?? null;
          const vintage = selectedOption
            ? bookToVintageAllocations[selectedSourceType ?? ("" as any)]?.find(
                ({ asset }) => asset.id === selectedOption.value,
              )
            : null;

          return (
            <Autocomplete
              options={vintageOptions}
              value={selectedOption}
              disabled={!selectedDestinationType}
              onChange={(_, selection) => onChange(selection?.value)}
              id="assetId"
              renderInput={({ InputProps, ...params }) => (
                <TextField
                  {...params}
                  InputProps={{
                    ...InputProps,
                    endAdornment: (
                      <Stack direction="row">
                        <Maybe condition={!!vintage}>
                          <Typography variant="caption" color="GrayText">
                            Price: {currencyFormat(vintage?.currentPrice ? +vintage?.currentPrice : 0)} | Value:{" "}
                            {currencyFormat(
                              (vintage?.currentPrice ? +vintage?.currentPrice : 0) * (vintage?.amountAvailable ?? 0),
                            )}{" "}
                            | Available: {numberFormat(vintage?.amountAvailable ?? "", { fallback: MISSING_DATA })}{" "}
                            credits
                          </Typography>
                        </Maybe>
                        {InputProps.endAdornment}
                      </Stack>
                    ),
                  }}
                  label="Vintage"
                  {...otherProps}
                  error={!!errors.assets?.[index]?.assetId || !!errors.assets?.[index]?.asset || hasAssetWarning}
                  helperText={errors.assets?.[index]?.assetId?.message}
                  disabled={!selectedDestinationType}
                  fullWidth
                />
              )}
              classes={{
                ...px({ root: hasAssetWarning && classes.Warn }, [undefined, null]),
              }}
              renderOption={(props, option) => (
                <li {...props} key={option.value}>
                  {option?.displayLabel || option.label}
                </li>
              )}
              fullWidth
            />
          );
        }}
      />
      {/* Asset Warning */}
      <Maybe condition={assetIsNotRCTApproved || assetIsNotBufferApproved}>
        <Stack className={classes.Warning}>
          <Stack direction="row" alignItems="center" gap={2}>
            <WarningRounded fontSize="small" color="warning" />
            <Typography variant="body2">This asset does not meet the RCT book requirement.</Typography>
          </Stack>
        </Stack>
      </Maybe>
      {/* Quantity - Value */}
      <Stack direction="row" gap={4}>
        <Controller
          control={control}
          name={`assets.${index}.amount`}
          render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => {
            const available = selectedAsset?.amountAvailable;

            return (
              <NumericFormat
                allowNegative={false}
                thousandSeparator
                label="Quantity"
                value={value}
                isAllowed={(values: NumberFormatValues): boolean => {
                  const { floatValue } = values;
                  const formValue = floatValue ?? 0;

                  return formValue <= (available ?? 0);
                }}
                customInput={TextField}
                InputProps={{
                  ref,
                }}
                error={!!errors?.assets?.[index]?.amount}
                helperText={
                  <Maybe condition={!!selectedAssetId || !!errors?.assets?.[index]?.amount}>
                    <Box component="span" display="flex" flexDirection="column">
                      <Maybe condition={!!errors?.assets?.[index]?.amount}>
                        <Typography variant="caption">{errors.assets?.[index]?.amount?.message}</Typography>
                      </Maybe>
                      <Maybe condition={!!selectedAssetId}>
                        <Typography variant="caption">
                          Available - {numberFormat(available ?? "", { fallback: MISSING_DATA })}
                        </Typography>
                      </Maybe>
                    </Box>
                  </Maybe>
                }
                {...otherProps}
                disabled={!selectedAssetId}
                fullWidth
              />
            );
          }}
        />
        <Controller
          control={control}
          name={`assets.${index}.value`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label="Value"
              value={value}
              customInput={TextField}
              InputProps={{
                ref,
                readOnly: true,
              }}
              {...otherProps}
              disabled
              fullWidth
              readOnly
            />
          )}
        />
      </Stack>
    </Stack>
  );
};

export default TransferAssetItem;
