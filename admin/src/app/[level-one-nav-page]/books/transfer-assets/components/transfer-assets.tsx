import { Controller, useFieldArray } from "react-hook-form";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Box, Button, Container, Divider, Stack, TextField, Typography } from "@mui/material";
import KeyboardArrowRightRounded from "@mui/icons-material/KeyboardArrowRightRounded";
import {
  AssetType,
  AdminBookResponse,
  BookType,
  AdminTransferRequest,
  AdminTransferResponse,
  uuid,
  AdminAllocationResponse,
  AdminGroupingParentResponse,
  AdminAssetTransferRequest,
} from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@hooks/use-navigation";
import { useLogger } from "@providers/logging";
import { Fragment, useMemo, useState, type JSX } from "react";
import useAutoCompleteOptions from "@hooks/use-auto-complete-options";
import { calculator, currencyFormat, Maybe, numberFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { NoNumberFormat } from "@utils/formatters/no-number-format";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { MISSING_DATA } from "@constants/constants";
import TransferAssetItem from "./transfer-asset-item";
import ReviewItem from "./review-item";
import useNavigationInterceptor from "@hooks/use-navigation-interceptor";
import { combineByParentBookType } from "@utils/helpers/portfolio/grouping-parent-book-transformations";
import { TransferAssetsModel } from "../models/transfer-assets";
import { CLEAN_TRANSFER_ASSETS_STATE_FORM } from "../constants/transfer-assets-form";
import { useEnhancedForm } from "@hooks/use-enhanced-form";

import classes from "../styles/transfer-assets.module.scss";

const TransferAssetRequestResolver = classValidatorResolver(TransferAssetsModel);

const TransferAssets = ({ booksByParents }: { booksByParents: AdminGroupingParentResponse[] }): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [canConfirm, setCanConfirm] = useState<boolean>(false);
  const [requestBody, setRequestBody] = useState<AdminTransferRequest>();

  const {
    control,
    formState: { errors, isDirty, isSubmitting, isSubmitSuccessful },
    smartSetValue,
    getValues,
    watch,
    reset,
    handleSubmit,
  } = useEnhancedForm<TransferAssetsModel>({
    resolver: TransferAssetRequestResolver,
    defaultValues: CLEAN_TRANSFER_ASSETS_STATE_FORM,
    mode: "onChange",
  });

  const { fields: assets } = useFieldArray({
    control,
    name: "assets",
  });

  const { navigating, continueNavigation, cancelNavigation } = useNavigationInterceptor({
    shouldIntercept: () => isDirty && !isSubmitSuccessful,
  });

  const parentBookRelatedInfoByType = combineByParentBookType(booksByParents, [
    "parent_book",
    "owner_registry_vintage_nested_allocations",
  ]);

  const books = useMemo(
    () =>
      Object.values(parentBookRelatedInfoByType)
        .filter(({ parentBook }) => !!parentBook)
        .map(({ parentBook }) => parentBook!),
    [parentBookRelatedInfoByType],
  );

  const bookToAllocations = useMemo(
    () =>
      Object.entries(parentBookRelatedInfoByType || {})?.reduce(
        (record, [bookType, { ownerRegistryVintageNestedAllocations }]) => ({
          ...record,
          [bookType]: Object.values(ownerRegistryVintageNestedAllocations || {}),
        }),
        {} as Record<BookType, AdminAllocationResponse[]>,
      ),
    [parentBookRelatedInfoByType],
  );

  const transferableBookTypes = useMemo(
    () =>
      Object.entries(bookToAllocations || {})
        .filter(([, allocations]) => allocations?.some((allocation) => allocation?.amountAvailable > 0))
        .map(([type]) => type),
    [bookToAllocations],
  );
  const formValues = getValues();

  const bookOptions = useAutoCompleteOptions<AdminBookResponse, { id: uuid; type: BookType }>({
    data: books,
    keys: ["id", "type", "name", "ownerAllocationsByAssetType"],
    preTransform: (books) => books?.filter((book) => book?.type !== BookType.COMPLIANCE_DEFAULT), // filtered out Compliance Book for now.
    label: (entry) => entry?.name ?? "",
    displayLabel: (entry) => {
      const vintageAllocations = entry?.ownerAllocationsByAssetType?.find(
        (f) => f.assetType === AssetType.REGISTRY_VINTAGE,
      );
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <span>{entry.name}</span>
          <Typography variant="caption" color="GrayText">
            {currencyFormat(
              calculator(vintageAllocations?.groupedPrices?.totalPriceAllocated)
                .add(vintageAllocations?.groupedPrices?.totalPricePendingBuy)
                .subtract(vintageAllocations?.groupedPrices?.totalPricePendingSell)
                .calculate()
                .toNumber(),
            )}
          </Typography>
        </Stack>
      );
    },
    value: (entry) => ({
      id: entry?.id ?? ("" as any),
      type: entry.type ?? ("" as any),
    }),
    postTransform: (options) => options.sort((a, b) => a.label?.localeCompare(b.label)),
  });

  const { trigger: transfer, isMutating: postingTransfer } = useTriggerRequest<
    AdminTransferResponse,
    AdminTransferRequest
  >({
    url: "/admin/transfers",
    method: "post",
    requestBody,
    swrOptions: {
      onSuccess: () => {
        setTimeout(() => {
          reset();
          enqueueSuccess(`Successfully saved tranfer${formValues?.assets?.length > 1 ? "s" : ""}`);
          popFromPath(1);
        });
      },
      onError: (error: any) => {
        enqueueError("Unable to make transfer");
        logger.error(`Unable to make transfer: ${error?.message}`, {});
      },
    },
  });

  const onSubmit = (formData: TransferAssetsModel): void => {
    setRequestBody({
      assetTransfers: formData.assets.reduce((assets: AdminAssetTransferRequest[], item) => {
        if (!item?.destination?.id || !item?.source?.id) return assets;
        return [
          ...assets,
          {
            amount: Number(NoNumberFormat(item?.amount ?? 0)),
            assetId: item?.assetId,
            destinationId: item?.destination?.id,
            sourceId: item?.source?.id,
          },
        ];
      }, []),
      memo: formData?.memo,
    });
    setCanConfirm(true);
  };

  return (
    <Container className={classes.Container}>
      <Stack className={classes.Form} component="form" onSubmit={handleSubmit(onSubmit)} gap={3}>
        {/* Asset Items */}
        {assets.map((asset, index) => (
          <TransferAssetItem
            key={asset?.id}
            {...{
              isSubmitting,
              isSubmitSuccessful,
              index,
              control,
              errors,
              books: books,
              bookToVintageAllocations: bookToAllocations,
              transferableBookTypes,
              bookOptions,
              setValue: smartSetValue,
              watch,
            }}
          />
        ))}
        {/* Memo */}
        <Controller
          name="memo"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Memo (Optional)"
              value={value ?? ""}
              InputProps={{ ref }}
              minRows={4}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        {/* Actions */}
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        open={canConfirm}
        title="Review and Confirm Transfer"
        customActions={[
          {
            buttonText: "CANCEL",
            actionType: "negative",
            className: classes.NegativeAction,
            onClick: () => setCanConfirm(false),
          },
          {
            buttonText: "CONFIRM TRANSFER",
            actionType: "positive",
            loading: postingTransfer,
            onClick: () => setTimeout(async () => await transfer()),
          },
        ]}
        onClose={() => setCanConfirm(false)}
        classes={{
          root: classes.Dialog,
          actions: classes.DialogAction,
        }}
      >
        <Stack className={classes.DialogInnerContent} gap={3}>
          {formValues?.assets.map((item, index) => (
            <Fragment key={`${item.sourceId}-${item.assetId}-${item.destinationId}`}>
              <Stack gap={3}>
                <ReviewItem label="Transfer From" value={item?.source?.name} />
                <ReviewItem label="Transfer To" value={item?.destination?.name} />
                <ReviewItem
                  label="Vintage"
                  value={
                    <Typography>
                      {item?.asset?.asset?.registryProjectId} - {item?.asset?.asset?.name}{" "}
                      <Typography component="span" color="GrayText">
                        {item?.asset?.asset?.projectVintageName}
                      </Typography>
                    </Typography>
                  }
                />
                <ReviewItem
                  label="Quantity"
                  value={numberFormat(NoNumberFormat(item?.amount ?? "") ?? "", { fallback: MISSING_DATA })}
                />
                <ReviewItem
                  label="Value"
                  value={currencyFormat(NoNumberFormat(item?.value ?? "") ?? "", { fallback: MISSING_DATA })}
                />
              </Stack>
              <Maybe condition={index > 0 && index !== formValues?.assets?.length}>
                <Divider />
              </Maybe>
              <ReviewItem label="Memo" value={formValues?.memo} fallbackValue="No memo added" />
            </Fragment>
          ))}
        </Stack>
      </GenericDialog>
      <GenericDialog
        open={navigating}
        title="Cancel transfer?"
        positiveAction={{
          buttonText: "CANCEL TRANSFER",
          onClick: continueNavigation,
          className: classes.InterruptPositiveAction,
        }}
        negativeAction={{
          buttonText: "STAY",
          onClick: cancelNavigation,
        }}
        onClose={cancelNavigation}
        classes={{
          title: `${classes.Title} ${classes.InterruptTitle}`,
        }}
      >
        <Box className={classes.DialogInnerContent}>
          <Typography>You will lose the data you have entered for this transfer.</Typography>
        </Box>
      </GenericDialog>
    </Container>
  );
};

export default TransferAssets;
