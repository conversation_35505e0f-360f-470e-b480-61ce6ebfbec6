import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { generateQueryParams, Nullable } from "@rubiconcarbon/frontend-shared";
import {
  BookRelations,
  DocumentType,
  AdminForwardCreateRequest,
  AdminForwardQuery,
  AdminForwardQueryResponse,
  AdminForwardResponse,
  ForwardStatus,
  ForwardType,
  PermissionEnum,
  AdminProjectVintageQueryResponse,
  ProjectVintageRelations,
  AdminProjectVintageResponse,
  uuid,
  AdminGroupingParentQueryResponse,
  AdminAllocationResponse,
  AdminOrganizationQueryResponse,
  AdminForwardUpdateRequest,
} from "@rubiconcarbon/shared-types";
import GenericTable from "@components/ui/generic-table";
import useGenericTableUtility, { Values } from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import { FileDownloadRounded } from "@mui/icons-material";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { useCallback, useContext, useMemo, useState, type JSX } from "react";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Stack, Typography } from "@mui/material";
import usePerformantState from "@hooks/use-perfomant-state";
import { AxiosContext } from "@providers/axios-provider";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { isNothing, px, toNumber, Undefinable } from "@rubiconcarbon/frontend-shared";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import DocumentsModal, { FileType } from "@components/documents-upload/documents-modal";
import { DocumentTypeUILabel } from "@constants/documents";
import { useGetSet, useToggle } from "react-use";
import { GenericTableDocumentsButtonExtensions } from "@components/ui/generic-table/types/generic-table-documents-button";
import { combineByParentBookType } from "@utils/helpers/portfolio/grouping-parent-book-transformations";
import { useRouter, useSearchParams } from "next/navigation";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { FORWARD_COLUMNS } from "../constants/forward-columns";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { ForwardFormModel, ForwardModel, ForwardLineItem } from "../models/forward";
import LineItems from "./line-tems";
import { ProjectToVintages } from "../types/expanded-row-types";
import { NestedColumnExportRecord } from "@components/ui/generic-table/types/generic-table-export";

import classes from "../styles/forward.module.scss";

const FileTypesOptions: FileType[] = [
  {
    label: DocumentTypeUILabel[DocumentType.PROOF_OF_CONFIRMATION],
    value: DocumentType.PROOF_OF_CONFIRMATION,
  },
  {
    label: DocumentTypeUILabel[DocumentType.CONTRACT],
    value: DocumentType.CONTRACT,
  },
  {
    label: DocumentTypeUILabel[DocumentType.PROOF_OF_DELIVERY],
    value: DocumentType.PROOF_OF_DELIVERY,
  },
];

const ForwardFormResolver = classValidatorResolver(ForwardFormModel);

const ForwardDeliveryComponent = ({
  forwardsResponse: serverForwardResponse,
  organizationsResponse,
  booksByParentResponse: serverBooksByParentResponse,
  type,
  search,
}: {
  forwardsResponse: AdminForwardQueryResponse;
  organizationsResponse: AdminOrganizationQueryResponse;
  booksByParentResponse: AdminGroupingParentQueryResponse;
  type?: "buy" | "sell";
  search?: string;
}): JSX.Element => {
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [amendingNestedRows, toggleAmendingNestedRows] = useGetSet<boolean>(false);

  const [viewingRow, setViewingRow] = useState<Undefinable<GenericTableRowModel<ForwardModel>>>();

  const organizationCustomerItems = useMemo(() => {
    if (!!organizationsResponse) {
      const items = organizationsResponse?.data?.map((b) => {
        return {
          label: b?.name,
          value: b.id,
        };
      });

      return items;
    }
    return [];
  }, [organizationsResponse]);

  const organizationCounterpartyItems = useMemo(() => {
    if (!!organizationsResponse) {
      const items = organizationsResponse?.data
        ?.filter((org) => !!org.counterparty)
        ?.map((b) => {
          return {
            label: b?.name,
            value: b.id,
          };
        });

      return items;
    }
    return [];
  }, [organizationsResponse]);

  const [projectToVintages, setProjectToVintages] = usePerformantState<ProjectToVintages>({});
  const [forwardRequestPayload, setForwardRequestPayload] = usePerformantState<
    Nullable<AdminForwardCreateRequest | AdminForwardUpdateRequest>
  >(null);

  const { table, form } = useGenericTableUtility<ForwardModel>({
    form: {
      mode: "onSubmit",
      resolver: ForwardFormResolver as any,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { handleSubmit, watch } = form || {};

  const firstValue = watch?.("amends.0");

  const creating = useMemo(() => firstValue?.creating, [firstValue?.creating]);
  const editing = useMemo(() => firstValue?.editing, [firstValue?.editing]);

  const columns: GenericTableColumn<ForwardModel>[] = useMemo(
    () =>
      FORWARD_COLUMNS.reduce<GenericTableColumn<ForwardModel>[]>((accum, column) => {
        return [
          ...accum,
          column.field === "organization.id"
            ? {
                ...column,
                valueOptions: firstValue?.type === ForwardType.SELL ? organizationCustomerItems : organizationCounterpartyItems,
              }
            : column,
        ] as GenericTableColumn<ForwardModel>[];
      }, []),
    [firstValue?.type, organizationCustomerItems, organizationCounterpartyItems],
  );

  const exportColumns: GenericTableColumn<ForwardLineItem>[] = useMemo(
    () =>
      LINE_ITEM_COLUMNS.reduce<GenericTableColumn<ForwardLineItem>[]>((accum, column) => {
        return [
          ...accum,
          column.field === "grandTotal"
            ? {
                ...column,
                label: "Grand Total / Net Revenue",
              }
            : column.field === "rawPrice"
              ? {
                  ...column,
                  label: "Sub Total / Gross Revenue",
                }
              : column,
        ];
      }, []),
    [],
  );

  const {
    data: forwardsResponse,
    isMutating: loadingForwards,
    trigger: refreshForwards,
  } = useTriggerRequest<AdminForwardQueryResponse, object, object, AdminForwardQuery>({
    url: "admin/forwards",
    queryParams: {
      includeTotalCount: true,
      limit: SERVER_PAGINATION_LIMIT,
      ...px({ type }, [null, undefined]),
    },
    optimisticData: serverForwardResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to load forwards.");
        logger.error(`Unable to load forwards: ${error?.message}.`, {});
      },
    },
  });

  const {
    data: booksByParentResponse,
    isMutating: loadingBooksByParents,
    trigger: refreshBooksByParents,
  } = useTriggerRequest<AdminGroupingParentQueryResponse>({
    url: "/admin/books/parents",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      isEnabled: true,
      includeTotalCount: true,
      includeRelations: [
        BookRelations.OWNER_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
        BookRelations.PRICES,
      ],
    },
    optimisticData: serverBooksByParentResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load books");
        logger.error(`Unable to load books by parents: ${error?.message}`, {});
      },
    },
  });

  const parentBookRelatedInfoByType = combineByParentBookType(booksByParentResponse?.data ?? [], [
    "parent_book",
    "owner_registry_vintage_nested_allocations",
  ]);

  const books = useMemo(
    () => Object.values(parentBookRelatedInfoByType || {})?.map(({ parentBook }) => parentBook),
    [parentBookRelatedInfoByType],
  );

  // Map of project to vintages allocations broken down by book type
  const projectToVintageAllocations = useMemo(() => {
    const record: Record<uuid, AdminAllocationResponse[]> = {};

    for (const { ownerRegistryVintageNestedAllocations } of Object.values(parentBookRelatedInfoByType)) {
      for (const { asset, owner, ...rest }  of Object.values(ownerRegistryVintageNestedAllocations || {})) {
        const projectId = asset?.projectId;
        if (projectId) record[projectId] = {
          ...record[projectId],
          [`${asset?.id}_${owner.type}`]:  { asset, owner, ...rest }, // added owner (book) type to key
        };
      }
    }

    return record;
  }, [parentBookRelatedInfoByType]);

  const { trigger: commitForward, isMutating: committingForward } = useTriggerRequest<
    AdminForwardResponse,
    AdminForwardCreateRequest | AdminForwardUpdateRequest
  >({
    url: `admin/forwards${editing ? "/{id}" : ""}`,
    method: editing ? "patch" : "post",
    pathParams: {
      id: firstValue?.id,
    },
    requestBody: forwardRequestPayload || undefined,
    swrOptions: {
      onError: (error: any) => {
        enqueueError(`Unable to ${creating ? "create" : "update"} forward.`);
        logger.error(`Unable ${creating ? "create" : "update"} forward: ${error?.message}`, {});
      },
      onSuccess: async (data) => {
        enqueueSuccess(`Successfully  ${creating ? "created" : "updated"} forward.`);

        await table?.handleKeepRowAfterAmendment({
          ...data,
          creating,
          editing: !creating,
        } as unknown as GenericTableRowModel<ForwardModel>);

        if (!creating) {
          await refreshForwards();
          await refreshBooksByParents();
        }
      },
    },
  });

  const getVintagesByProject = async (id: uuid): Promise<void> => {
    try {
      const { data } = await api.get<AdminProjectVintageQueryResponse>(
        `admin/project-vintages?${generateQueryParams({
          projectIds: [id],
          limit: SERVER_PAGINATION_LIMIT,
          includeRelations: [ProjectVintageRelations.PROJECT],
        })}`,
      );

      setProjectToVintages({
        ...projectToVintages,
        [id]: {
          data: data?.data,
          fetching: false,
          fetched: true,
        },
      });
    } catch (error: any) {
      enqueueError("Unable to fetch project's vintages data.");
      logger.error(`Unable to fetch project's vintages data. Error: ${error?.message}`, {});
    }
  };

  const toRowModel = useCallback((row: AdminForwardResponse): GenericTableRowModel<ForwardModel> => {
    return {
      ...row,
      amount:
        row?.status === ForwardStatus.CANCELED
          ? row?.lineItems?.reduce((sum, { expectedAmount }) => sum + toNumber(expectedAmount), 0)
          : row?.amount,
    } as unknown as ForwardModel;
  }, []);

  const handleProjectVintageAddition = (projectId: uuid, vintage: AdminProjectVintageResponse): void => {
    setProjectToVintages({
      ...projectToVintages,
      [projectId]: {
        ...(projectToVintages?.[projectId] || {}),
        data: [...(projectToVintages?.[projectId]?.data || []), vintage],
      },
    });
  };

  const onSubmit = async (formData: Values<ForwardModel>): Promise<void> => {
    const {
      amends: [firstValue],
    } = formData;

    const { project, type, organization, memo } = firstValue || {};

    let payload: AdminForwardCreateRequest | AdminForwardUpdateRequest = null;
    if (creating) {
      payload = {
        projectId: project?.id as uuid,
        organizationId: organization?.id as uuid,
        type,
        memo: isNothing(memo, ["string"]) ? null : memo,
      };
    } else {
      payload = {
        memo: isNothing(memo, ["string"]) ? null : memo,
      };
    }

    setForwardRequestPayload(payload);
    setTimeout(async () => await commitForward());
  };

  const cleanupOnDocumentsDialogClose = (): void => {
    toggleDocumentsModal(false);
  };

  const handleSearchTermChange = (term: string): void => {
    const params = new URLSearchParams((searchParams ?? "")?.toString());
    if (term) {
      params.set("search", term);
    } else {
      params.delete("search");
    }
    router.replace(`/trading/forward-delivery?${params.toString()}`);
  };

  return (
    <>
      <GenericTable
        id="forwards"
        loading={loadingForwards || loadingBooksByParents}
        reloadingRow={loadingForwards || committingForward}
        columns={columns}
        pageableData={forwardsResponse}
        toRowModel={toRowModel}
        isExpandable
        dismissFormOnSubmit={false}
        eager={{
          expand: true,
        }}
        expandOn={{
          create: true,
          mount: (row) => !!search && row?.lineItems?.some(({ uiKey }) => uiKey === search), // expand only on exact match
        }}
        styles={{
          root: {
            maxHeight: "calc(100vh - 190px)",
          },
        }}
        globalSearch={{
          term: search,
          searchKeys: [
            "uiKey",
            "project.registryProjectId",
            "project.name",
            "type",
            "organization.name",
            "amount",
            "status",
            "lineItems.{number}.uiKey" as any,
          ],
          onSearchTermChange: handleSearchTermChange,
        }}
        export={{
          filename: `Forwards-${new Date()}`,
          nested: {
            lineItems: {
              uniquenessLabel: "Line Item",
              columns: exportColumns,
            },
          } as NestedColumnExportRecord<ForwardModel>,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.FORWARDS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loadingForwards || loadingBooksByParents || committingForward || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Buy / Sell",
            requiredPermission: PermissionEnum.FORWARDS_CREATE,
            isDisabled: loadingForwards || loadingBooksByParents || committingForward || creating || editing,
            onClickHandler: table?.handleAddRow,
          },
        ]}
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindKeepRowAfterAmendment={table?.bindKeepRowAfterAmendment}
        renderExpandContent={(row): JSX.Element => {
          const isBuy = row?.type === ForwardType.BUY;
          const projectId = row?.project?.id;

          const pvs = projectToVintages?.[projectId];

          if (!!projectId && !pvs?.data && !pvs?.fetching) {
            setProjectToVintages({
              ...projectToVintages,
              [projectId]: {
                fetching: true,
                fetched: false,
              },
            });

            setTimeout(async () => await getVintagesByProject(projectId));
          }

          return (
            <Stack className={classes.ExpandedContent} gap={2}>
              <Typography className={classes.Header}>Delivery Schedule</Typography>
              <LineItems
                hasParent={!row?.creating && row?.status === ForwardStatus.PENDING}
                forwardId={row?.creating ? undefined : (row?.id as uuid)}
                items={row?.lineItems}
                isBuy={isBuy}
                projectId={projectId}
                projectVintages={pvs ?? { data: [], fetching: false, fetched: false}}
                vintageAllocations={Object.values(projectToVintageAllocations[projectId] || {})}
                books={books.filter((book): book is NonNullable<typeof book> => book !== undefined)}
                exactLineItemKeyQuery={search}
                toggleAmendingNestedRows={toggleAmendingNestedRows as (nextValue?: boolean) => void}
                refreshForwards={refreshForwards}
                onProjectVintageAddition={handleProjectVintageAddition}
              />
            </Stack>
          );
        }}
        onFormSubmit={handleSubmit?.(onSubmit)}
        extensions={
          {
            amendingNestedRows,
            toggleDocumentsModal,
            setViewingRow,
          } as GenericTableDocumentsButtonExtensions
        }
      />
      <DocumentsModal
        open={openDocumentsModal}
        relatedUiKey={viewingRow?.uiKey}
        organizationId={viewingRow?.organization?.id}
        saveTypeOptions={FileTypesOptions}
        onPositiveClick={(callback) => {
          callback();
        }}
        onNegativeClick={(callback) => {
          callback();
          cleanupOnDocumentsDialogClose();
        }}
        onClose={async (callback, amended) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (amended) refreshForwards();
        }}
      />
    </>
  );
};

export default ForwardDeliveryComponent;
