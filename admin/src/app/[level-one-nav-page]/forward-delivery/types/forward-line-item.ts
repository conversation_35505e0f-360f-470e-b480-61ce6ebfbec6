import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { ForwardLineItem } from "../models/forward";
import { BaseSyntheticEvent } from "react";
import { <PERSON>mit<PERSON><PERSON><PERSON>, SubmitErrorHandler } from "react-hook-form";

export type ForwardLineItemExtensions = {
  readonlyParent: boolean;
  settleLineItem: (row: GenericTableRowModel<ForwardLineItem>) => Promise<void>;
  cancelLineItem: (row: GenericTableRowModel<ForwardLineItem>) => Promise<void>;
};

export type HandleLineItemSubmit = (
  onValid: SubmitHandler<ForwardLineItem>,
  onInvalid?: SubmitErrorHandler<ForwardLineItem>,
) => (e?: BaseSyntheticEvent) => Promise<void>;
