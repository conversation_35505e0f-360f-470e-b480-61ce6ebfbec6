// should move this to shared fe lib

import { AllKeys, isNothing } from "@rubiconcarbon/frontend-shared";
import { registerDecorator, ValidationOptions, ValidationArguments } from "class-validator";

const compareValues = (
  value: number,
  comparisonValue: number | [number, number],
  operator: "minOfField" | "maxOfField" | "betweenFields",
  exclusive: boolean = false,
): boolean => {
  switch (operator) {
    case "minOfField":
      return !isNothing(comparisonValue)
        ? exclusive
          ? value > (comparisonValue as number)
          : value >= (comparisonValue as number)
        : true;
    case "maxOfField":
      return !isNothing(comparisonValue)
        ? exclusive
          ? value < (comparisonValue as number)
          : value <= (comparisonValue as number)
        : true;
    case "betweenFields": {
      const [min, max] = comparisonValue as [number, number];
      return !isNothing(comparisonValue) && (comparisonValue as [number, number])?.length === 2
        ? exclusive
          ? value > min && value < max
          : value >= min && value <= max
        : true;
    }
    default:
      return false;
  }
};

export const MinOfField =
  <M>(property: AllKeys<M>, validationOptions?: ValidationOptions & { exclusive?: boolean }) =>
  (object: Record<string, any>, propertyName: string): void => {
    registerDecorator({
      name: "minOfField",
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate: (value: any, args: ValidationArguments) => {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return compareValues(value, relatedValue, "minOfField", validationOptions?.exclusive);
        },
        defaultMessage: (args: ValidationArguments) => {
          const [relatedPropertyName] = args.constraints;
          return `${propertyName} must be ${validationOptions?.exclusive ? "greater than" : "greater than or equal to"} ${relatedPropertyName}`;
        },
      },
    });
  };

export const MaxOfField =
  <M>(property: AllKeys<M>, validationOptions?: ValidationOptions & { exclusive?: boolean }) =>
  (object: Record<string, any>, propertyName: string): void => {
    registerDecorator({
      name: "maxOfField",
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate: (value: any, args: ValidationArguments) => {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return compareValues(value, relatedValue, "maxOfField", validationOptions?.exclusive);
        },
        defaultMessage: (args: ValidationArguments) => {
          const [relatedPropertyName] = args.constraints;
          return `${propertyName} must be ${validationOptions?.exclusive ? "less than" : "less than or equal to"} ${relatedPropertyName}`;
        },
      },
    });
  };

export const BetweenFields =
  <M>(
    minProperty: AllKeys<M>,
    maxProperty: AllKeys<M>,
    validationOptions?: ValidationOptions & { exclusive?: boolean },
  ) =>
  (object: Record<string, any>, propertyName: string): void => {
    registerDecorator({
      name: "betweenFields",
      target: object.constructor,
      propertyName: propertyName,
      constraints: [minProperty, maxProperty],
      options: validationOptions,
      validator: {
        validate: (value: any, args: ValidationArguments) => {
          const [minPropertyName, maxPropertyName] = args.constraints;
          const minValue = (args.object as any)[minPropertyName];
          const maxValue = (args.object as any)[maxPropertyName];
          return compareValues(value, [minValue, maxValue], "betweenFields", validationOptions?.exclusive);
        },
        defaultMessage: (args: ValidationArguments) => {
          const [minPropertyName, maxPropertyName] = args.constraints;
          const exclusiveText = validationOptions?.exclusive ? "exclusively " : "";
          return `${propertyName} must be ${exclusiveText}between ${minPropertyName} and ${maxPropertyName}`;
        },
      },
    });
  };
