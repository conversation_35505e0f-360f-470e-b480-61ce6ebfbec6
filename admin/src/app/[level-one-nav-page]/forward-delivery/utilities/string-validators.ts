// should move this to shared fe lib

import { registerDecorator, ValidationOptions, ValidationArguments } from "class-validator";

export const IsNotEmptyString = (validationOptions?: ValidationOptions) => {
  return (object: Record<string, any>, propertyName: string): void => {
    registerDecorator({
      name: "isNotEmptyString",
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate: (value: any) => typeof value === "string" && value.trim().length > 0,
        defaultMessage: (args: ValidationArguments) =>
          `${args.property} should not be an empty string or only whitespace.`,
      },
    });
  };
};
