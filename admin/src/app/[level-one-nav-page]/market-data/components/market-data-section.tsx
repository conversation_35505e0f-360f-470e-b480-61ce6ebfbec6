import { Maybe } from "@rubiconcarbon/frontend-shared";
import { Box, Grid, Paper, Stack, SxProps, useMediaQuery } from "@mui/material";
import { PropsWithChildren, ReactNode, type JSX } from "react";

import classes from "../styles/market-data-section.module.scss";

type MarketDataSectionProps = {
  title: ReactNode;
  subTitle?: ReactNode;
  Taskbar?: ReactNode;
  sx?: SxProps;
  headerSx?: SxProps;
};

const MarketDataSection = ({
  title,
  subTitle,
  Taskbar,
  sx = {},
  headerSx = {},
  children,
}: PropsWithChildren<MarketDataSectionProps>): JSX.Element => {
  const isMobile = useMediaQuery("(max-width: 700px)");

  return (
    <Stack component={Paper} className={classes.Section} gap={2} sx={sx}>
      <Grid
        container
        direction={isMobile ? "column" : "row"}
        justifyContent="space-between"
        alignItems={isMobile ? "stretch" : "center"}
        rowGap={2}
        sx={headerSx}
      >
        <Grid item xs={12} md={3} container direction="column" gap={1}>
          <Box className={classes.Title}>{title}</Box>
          <Maybe condition={!!subTitle}>
            <Box className={classes.SubTitle}>{subTitle}</Box>
          </Maybe>
        </Grid>
        <Maybe condition={!!Taskbar}>
          <Grid item xs={12} md={9} width="100%">
            {Taskbar}
          </Grid>
        </Maybe>
      </Grid>
      {children}
    </Stack>
  );
};

export default MarketDataSection;
