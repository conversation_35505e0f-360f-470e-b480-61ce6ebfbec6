import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import MarketData from "./components";

import type { JSX } from "react";

/**
 * Market Data Page
 *
 * This is a server component that renders the Market Data page
 */
export default function MarketDataPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.REPORTING_MARKET_DATA]}>
      <MarketData />
    </AuthorizeServer>
  );
}
