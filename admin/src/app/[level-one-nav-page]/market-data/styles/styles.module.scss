.boxContainer {
  padding: 16px 24px 16px 24px;
  gap: 16px;
  border-radius: 5px 0px 0px 0px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.14);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  width: 100%;
  overflow: auto;
  min-height: 120px;
}

.exploreTitle {
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  letter-spacing: 0.15000000596046448px;
  text-align: left;
  color: #121212;
}

.subTitle {
  font-size: small;
  font-weight: 400;
  color: #121212;
}

.search {
  .label {
    font-family: Helvetica Neue;
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
    text-align: left;
  }

  .labelEnhanced {
    font-family: Inter;
    font-size: 13px;
    font-weight: 600;
    line-height: 24px;
    text-align: left;
    background-color: rgba(154, 183, 154, 0.12);
    height: 44px !important;
    padding-top: 5px;
    padding-bottom: 5px;
  }
}

.display {
  .title {
    .mainLabel {
      font-family: Helvetica Neue;
      font-size: 14px;
      font-weight: 500;
      line-height: 12px;
      letter-spacing: 0.15000000596046448px;
      text-align: left;
      color: rgba(0, 0, 0, 0.6);
    }
  }

  .mainValue {
    font-family: Helvetica Neue;
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    letter-spacing: 0.15000000596046448px;
    text-align: left;
    color: rgba(0, 0, 0, 0.87);
  }

  .label {
    font-family: Helvetica Neue;
    font-size: 14px;
    font-weight: 500;
    line-height: 12px;
    letter-spacing: 0.15000000596046448px;
    text-align: left;
    color: rgba(0, 0, 0, 0.87);
  }

  .value {
    font-family: Helvetica Neue;
    font-size: 14px;
    font-weight: 400;
    line-height: 12px;
    letter-spacing: 0.15000000596046448px;
    text-align: left;
    color: rgba(0, 0, 0, 0.87);
  }
}

.note {
  font-family: Helvetica Neue;
  font-size: 14px;
  font-weight: 500;
  line-height: 12px;
  letter-spacing: 0.15000000596046448px;
  text-align: left;
  color: rgba(0, 0, 0, 0.6);
}
