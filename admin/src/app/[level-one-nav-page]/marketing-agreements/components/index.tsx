"use client";;
import Page from "@components/layout/containers/page";
import { AdminMarketingAgreementQueryResponse, AdminOrganizationQueryResponse } from "@rubiconcarbon/shared-types";
import MarketingAgreementsComponent from "./marketing-agreements";

import type { JSX } from "react";

const MarketingAgreementsPage = ({
  counterpartiesResponse,
  marketingAgreementsResponse,
}: {
  counterpartiesResponse: AdminOrganizationQueryResponse;
  marketingAgreementsResponse: AdminMarketingAgreementQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <MarketingAgreementsComponent
        counterpartiesResponse={counterpartiesResponse}
        marketingAgreementsResponse={marketingAgreementsResponse}
      />
    </Page>
  );
};

export default MarketingAgreementsPage;
