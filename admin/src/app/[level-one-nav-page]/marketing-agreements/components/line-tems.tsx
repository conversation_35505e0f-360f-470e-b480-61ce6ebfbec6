import AddNewVintageForm from "@components/project-vintages/components/add-new-vintage-form";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import useAuth from "@providers/auth-provider";
import useAutoCompleteOptions from "@hooks/use-auto-complete-options";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Stack, Typography, Tooltip, Chip, Fab, Zoom, Box } from "@mui/material";
import { MaybeNothing, isNothing, pickFromRecord, toNumber, Undefinable, Maybe } from "@rubiconcarbon/frontend-shared";
import {
  uuid,
  AdminProjectVintageResponse,
  PermissionEnum,
  AdminMarketingAreementLineItemCreateRequest,
  AdminMarketingAgreementLineItemUpdateRequest,
  AdminMarketingAgreementQueryResponse,
  AdminMarketingAgreementLineItemResponse,
  MarketingAgreementStatus,
} from "@rubiconcarbon/shared-types";
import { AxiosResponse } from "axios";
import { useContext, useMemo, useState, useCallback, useEffect, useRef, type JSX } from "react";
import { useToggle } from "react-use";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { useLogger } from "@providers/logging";
import { ProjectVintageEntry } from "../types/expanded-row-types";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { GenericTableNoDataContent } from "@components/ui/generic-table/components/generic-table-row";
import { MarketingAgreementLineItem, MultiMarketingAgreementLineItemFormModel } from "../models/marketing-agreement";
import { MarketingAgreementLineItemExtensions } from "../types/marketing-agreement";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import usePerformantEffect from "@hooks/use-performant-effect";
import { AxiosContext } from "@providers/axios-provider";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import Papa from "papaparse";

import dialogClasses from "../styles/line-item-modal.module.scss";
import classes from "../styles/line-items.module.scss";
import parentClasses from "../styles/marketing-agreement.module.scss";

type LineItemsProps = {
  hasParent: boolean;
  editingParent: boolean;
  maId?: uuid;
  items: MarketingAgreementLineItem[];
  projectId: uuid;
  projectVintages: ProjectVintageEntry;

  toggleAmendingNestedRows: (nextValue?: boolean) => void;
  refreshMarketingAgreements: () => Promise<AdminMarketingAgreementQueryResponse>;
  onProjectVintageAddition: (projectId: uuid, vintage: AdminProjectVintageResponse) => void;
};

const MultiMALineItemFormResolver = classValidatorResolver(MultiMarketingAgreementLineItemFormModel);

const LineItems = ({
  hasParent,
  editingParent,
  maId,
  items,
  projectId,
  projectVintages,
  toggleAmendingNestedRows,
  refreshMarketingAgreements,
  onProjectVintageAddition,
}: LineItemsProps): JSX.Element => {
  const { user } = useAuth();
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const permissions = useMemo(() => user?.permissions || [], [user?.permissions]);

  const hasPermissionToCreate = permissions.includes(PermissionEnum.FORWARDS_CREATE_LINE_ITEMS);

  const [openAddVintageModal, toggleAddVintageModal] = useToggle(false);
  const [openCancelLineItemModal, toggleCancelLineItemModal] = useToggle(false);
  const [canSubmitNewVintage, toggleCanSubmitNewVintage] = useToggle(false);
  const [submittingDialogContent, toggleSubmittingDialogContent] = useToggle(false);

  const [lastAmendedIndex, setAmendedIndex] = useState<MaybeNothing<number>>();
  const [viewingLineItem, setViewingLineItem] =
    useState<Undefinable<GenericTableRowModel<MarketingAgreementLineItem>>>();

  const { table, form, currentFormValue } = useGenericTableUtility<MarketingAgreementLineItem>({
    form: {
      mode: "onSubmit",
      resolver: MultiMALineItemFormResolver as any,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { getValues, smartSetValue, watch, trigger, handleSubmit } = form || {};

  const inCreateMode = useMemo(
    () => currentFormValue?.amends?.some((amend) => amend?.creating === true),
    [currentFormValue?.amends],
  );

  const inEditMode = useMemo(
    () => currentFormValue?.amends?.some((amend) => amend?.editing === true),
    [currentFormValue?.amends],
  );

  const vintageOptions = useAutoCompleteOptions({
    data: projectVintages?.data || [],
    keys: ["id", "name"],
    label: (entry) => entry?.name,
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  const columns = useMemo(
    () =>
      LINE_ITEM_COLUMNS.map((column) => {
        if (column.field === "projectVintage.id") {
          column.valueOptions = vintageOptions;
          column.autoCompleteLoading = projectVintages?.fetching;
        }

        return column;
      }),
    [projectVintages?.fetching, vintageOptions],
  );

  usePerformantEffect(() => {
    toggleAmendingNestedRows(inCreateMode || inEditMode);
  }, [inCreateMode, inEditMode]);

  useEffect(() => {
    const subscription = watch?.(({ amends = [] }, { name, type }) => {
      if (type === "change") {
        const [, index, ...path] = ((name as any) ?? "").split(".");
        const joinedPath = path.join(".") as any;

        if (!isNothing(index)) {
          const numIndex = Number(index);

          setAmendedIndex(numIndex);

          const formRow = amends?.at(numIndex);

          switch (path?.at(0)) {
            case "projectVintage": {
              const id = formRow && joinedPath ? (pickFromRecord(formRow, [joinedPath], true) as Record<string, uuid>)?.[joinedPath] : undefined;

              if (id === "_add_vintage_") {
                toggleAddVintageModal?.(true);
                smartSetValue?.(name as any, undefined);
              }
              break;
            }
            default:
              break;
          }
        }
      }
    });
    return (): void => subscription?.unsubscribe();
  }, [smartSetValue, toggleAddVintageModal, watch]);

  const toRowModel = useCallback(
    (row: MarketingAgreementLineItem): GenericTableRowModel<MarketingAgreementLineItem> => {
      return {
        ...row,
      };
    },
    [],
  );

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleCSVUpload = async (event: React.ChangeEvent<HTMLInputElement>): Promise<void> => {
    try {
      const file = event.target.files?.[0];
      if (!file) return;

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        quoteChar: '"',
        delimiter: ",",
        escapeChar: '"',
        dynamicTyping: false,
        complete: async (results) => {
          const failedVintages: string[] = [];
          const failedLineItems: string[] = [];
          const failedAmounts: string[] = [];
          const newRows: AdminMarketingAreementLineItemCreateRequest[] = [];
          const updateRows: any[] = [];
          const uniqueRowsMap = new Map();

          const parseFormattedNumber = (value: string | number | null | undefined): number => {
            if (isNothing(value, ["string", "number"])) return null;
            if (typeof value === "number") return value;
            return toNumber(value.toString().replace(/,/g, ""));
          };

          if (results.errors && results.errors.length > 0) {
            enqueueError(`CSV parsing error: ${results.errors[0].message}`);
            logger.error(`CSV parsing error: ${results.errors[0].message}`, {});
            return;
          }

          for (const rowData of results.data as any[]) {
            const rowKey = JSON.stringify({
              uiKey: rowData.uiKey,
              vintage: rowData.vintageId,
              qty: rowData.quantity,
              qty_issued: rowData.amountIssued,
              original_delivery_date: rowData.deliveryDate,
              expiration_date: rowData.expirationDate,
            });

            // Skip duplicate rows
            if (uniqueRowsMap.has(rowKey)) {
              continue;
            }

            uniqueRowsMap.set(rowKey, true);

            // Find the matching projectVintage by label
            const projectVintageLabel = rowData.vintageId;
            const matchingVintage = projectVintages.data.find((option) => option.name === projectVintageLabel);
            // Find the matching line item by label
            const matchingLineItem = items.find((option) => option.uiKey === rowData.uiKey);
            const newStatus =
              rowData.amountIssued > 0 ? MarketingAgreementStatus.ISSUED : MarketingAgreementStatus.PENDING;

            if (!matchingVintage && rowData.vintageId) {
              failedVintages.push(projectVintageLabel);
              continue; // Skip if no matching vintage is found
            }

            if (!matchingLineItem && rowData.uiKey) {
              failedLineItems.push(rowData.uiKey);
              continue; // Skip if no matching line item is found
            }

            if (parseFormattedNumber(rowData.quantity) === null) {
              failedAmounts.push(rowData.uiKey || rowData.vintageId);
              logger.error(`Invalid quantity or amountIssued for line item: ${rowData.uiKey || rowData.vintageId}`, {});
              continue; // Skip if quantity or amountIssued is invalid
            }
            if (!matchingLineItem) {
              const lineItem: AdminMarketingAreementLineItemCreateRequest = {
                amount: parseFormattedNumber(rowData.quantity),
                amountIssued: parseFormattedNumber(rowData.amountIssued) || null,
                deliveryDate: rowData.deliveryDate,
                expirationDate: rowData.expirationDate || null,
                projectVintageId: matchingVintage?.id || null,
                status: newStatus,
              };

              newRows.push(lineItem);
            } else {
              const csvAmount = parseFormattedNumber(rowData.quantity);
              const csvAmountIssued = parseFormattedNumber(rowData.amountIssued || null);

              // Get vintage ID from matching line item (handle possible structure differences)
              const existingVintageId = matchingLineItem.projectVintage?.id;

              const csvDeliveryTimestamp = rowData.deliveryDate ? new Date(rowData.deliveryDate).getTime() : null;
              const existingDeliveryTimestamp = matchingLineItem.lastUpdatedDeliveryDate
                ? new Date(matchingLineItem.lastUpdatedDeliveryDate).getTime()
                : null;

              const csvExpirationTimestamp = rowData.expirationDate ? new Date(rowData.expirationDate).getTime() : null;
              const existingExpirationTimestamp = matchingLineItem.expirationDate
                ? new Date(matchingLineItem.expirationDate).getTime()
                : null;

              const hasChanges =
                csvAmount !== matchingLineItem.amount ||
                csvAmountIssued !== matchingLineItem.amountIssued ||
                csvDeliveryTimestamp !== existingDeliveryTimestamp ||
                csvExpirationTimestamp !== existingExpirationTimestamp ||
                matchingVintage.id !== existingVintageId;

              // Only add to updateRows if there are actual changes
              if (hasChanges) {
                const lineItem: any = {
                  id: matchingLineItem.id,
                  amount: csvAmount,
                  amountIssued: csvAmountIssued,
                  deliveryDate: rowData.deliveryDate,
                  expirationDate: rowData.expirationDate || null,
                  projectVintageId: matchingVintage?.id || null,
                  status: newStatus,
                };

                updateRows.push(lineItem);
              }
            }
          }

          if (failedVintages.length > 0) {
            enqueueError(`Failed to find project vintage for: ${failedVintages.join(", ")}`);
            logger.error(`Failed to find project vintage for: ${failedVintages.join(", ")}`, {});
            return;
          }

          if (failedLineItems.length > 0) {
            enqueueError(`Failed to find existing line item for: ${failedLineItems.join(", ")}`);
            logger.error(`Failed to find existing line item for: ${failedLineItems.join(", ")}`, {});
            return;
          }

          if (failedAmounts.length > 0) {
            enqueueError(`Invalid amounts for: ${failedAmounts.join(", ")}`);
            logger.error(`Invalid amounts for: ${failedAmounts.join(", ")}`, {});
            return;
          }

          if (newRows.length > 0) {
            await createManyLineItems(newRows);
          }
          if (updateRows.length > 0) {
            await updateManyLineItems(updateRows);
          }
          if (newRows.length === 0 && updateRows.length === 0) {
            enqueueSuccess("No rows found in CSV to create or update.");
          }
        },
        error: (error) => {
          enqueueError(`Error processing CSV: ${error.message}`);
          logger.error(`Error processing CSV: ${error.message}`, {});
        },
      });

      event.target.value = "";
    } catch (error: any) {
      enqueueError(`Error processing CSV: ${error?.message}`);
      logger.error(`Error processing CSV: ${error?.message}`, {});
    }
  };

  const triggerFileUpload = (): void => {
    fileInputRef.current?.click();
  };

  const createLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    try {
      const payload: AdminMarketingAreementLineItemCreateRequest = {
        amount: toNumber(row?.amount),
        amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : undefined,
        deliveryDate: new Date(row.originalDeliveryDate),
        expirationDate: !isNothing(row?.expirationDate) ? new Date(row.expirationDate) : undefined,
        projectVintageId: row?.projectVintage?.id,
      };

      await api.post<
        AdminMarketingAreementLineItemCreateRequest,
        AxiosResponse<AdminMarketingAgreementLineItemResponse>
      >(`admin/marketing-agreements/${maId}/line-items`, payload);

      enqueueSuccess("Successfully created line item");

      table?.handleCancelRowAmendment(row);

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to save line item: ${error?.message}.`);
      logger.error(`Unable to save line item: ${error?.message}.`, {});
    }
  };

  const createManyLineItems = async (rows: AdminMarketingAreementLineItemCreateRequest[]): Promise<void> => {
    try {
      const results = await Promise.allSettled(
        rows.map(async (row) => {
          const payload: AdminMarketingAreementLineItemCreateRequest = {
            amount: toNumber(row?.amount),
            amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : null,
            deliveryDate: new Date(row.deliveryDate),
            expirationDate: !isNothing(row?.expirationDate) ? new Date(row.expirationDate) : null,
            projectVintageId: row.projectVintageId,
            status: row.status,
          };

          return {
            result: await api.post<
              AdminMarketingAreementLineItemCreateRequest,
              AxiosResponse<AdminMarketingAgreementLineItemResponse>
            >(`admin/marketing-agreements/${maId}/line-items`, payload),
            vintage: projectVintages.data.find((option) => option.id === row.projectVintageId)?.name,
          };
        }),
      );

      const successCount = results.filter((result) => result.status === "fulfilled").length;
      const failedResults = results.filter((result) => result.status === "rejected");
      const failCount = failedResults.length;

      const failures = rows
        .filter((_, index) => results[index].status === "rejected")
        .map((row) => projectVintages.data.find((option) => option.id === row.projectVintageId)?.name)
        .filter(Boolean);

      if (successCount > 0) {
        enqueueSuccess(`Successfully created ${successCount} line item${successCount !== 1 ? "s" : ""}`);
      }

      if (failCount > 0) {
        enqueueError(`Failed to create ${failCount} line item${failCount !== 1 ? "s" : ""}: ${failures.join(", ")}`);

        // Log details of failures
        failedResults.forEach((result) => {
          if (result.status === "rejected") {
            logger.error(`Failed to create line item: ${result.reason?.message}`, {});
          }
        });
      }

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to process line items: ${error?.message}.`);
      logger.error(`Unable to process line items: ${error?.message}.`, {});
    }
  };

  const updateLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    try {
      const payload: AdminMarketingAgreementLineItemUpdateRequest = {
        amount: toNumber(row?.amount),
        amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : undefined,
        deliveryDate: new Date(row.lastUpdatedDeliveryDate),
        expirationDate: !isNothing(row?.expirationDate) ? new Date(row.expirationDate) : undefined,
        projectVintageId: row?.projectVintage?.id,
      };

      await api.patch<
        AdminMarketingAgreementLineItemUpdateRequest,
        AxiosResponse<AdminMarketingAgreementLineItemResponse>
      >(`admin/marketing-agreements/${maId}/line-items/${row?.id}`, payload);

      enqueueSuccess("Successfully updated line item");

      table?.handleCancelRowAmendment(row);

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to update line item: ${error?.message}.`);
      logger.error(`Unable to update line item: ${error?.message}.`, {});
    }
  };

  const updateManyLineItems = async (rows: any[]): Promise<void> => {
    try {
      const results = await Promise.allSettled(
        rows.map(async (row) => {
          const payload: AdminMarketingAgreementLineItemUpdateRequest = {
            amount: toNumber(row?.amount),
            amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : null,
            deliveryDate: new Date(row.deliveryDate),
            expirationDate: !isNothing(row?.expirationDate) ? new Date(row?.expirationDate) : null,
            projectVintageId: row.projectVintageId || null,
            status: row.status,
          };
          return {
            result: await api.patch<
              AdminMarketingAgreementLineItemUpdateRequest,
              AxiosResponse<AdminMarketingAgreementLineItemResponse>
            >(`admin/marketing-agreements/${maId}/line-items/${row?.id}`, payload),
            vintage: projectVintages.data.find((option) => option.id === row.projectVintageId)?.name,
          };
        }),
      );

      const successCount = results.filter((result) => result.status === "fulfilled").length;
      const failedResults = results.filter((result) => result.status === "rejected");
      const failCount = failedResults.length;

      const failures = rows
        .filter((_, index) => results[index].status === "rejected")
        .map((row) => projectVintages.data.find((option) => option.id === row.projectVintageId)?.name)
        .filter(Boolean);

      if (successCount > 0) {
        enqueueSuccess(`Successfully updated ${successCount} line item${successCount !== 1 ? "s" : ""}`);
      }

      if (failCount > 0) {
        enqueueError(`Failed to updated ${failCount} line item${failCount !== 1 ? "s" : ""}: ${failures.join(", ")}`);

        // Log details of failures
        failedResults.forEach((result) => {
          if (result.status === "rejected") {
            logger.error(`Failed to updated line item: ${result.reason?.message}`, {});
          }
        });
      }

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to process line items: ${error?.message}.`);
      logger.error(`Unable to process line items: ${error?.message}.`, {});
    }
  };

  const updateLineItemStatus = async (
    row: GenericTableRowModel<MarketingAgreementLineItem>,
    status: "settle" | "cancel",
  ): Promise<void> => {
    setViewingLineItem(row);
    if (status === "cancel") toggleCancelLineItemModal(true);
  };

  const cancelLineItem = async (row: MarketingAgreementLineItem): Promise<void> => {
    try {
      await api.patch<
        AdminMarketingAgreementLineItemUpdateRequest,
        AxiosResponse<AdminMarketingAgreementLineItemResponse>
      >(`admin/marketing-agreements/${maId}/line-items/${row?.id}/cancel`);

      enqueueSuccess("Successfully canceled line item");

      await refreshMarketingAgreements();
      toggleCancelLineItemModal(false);
    } catch (error: any) {
      enqueueError(`Unable to cancel line item.`);
      logger.error(`Unable to cancel line item: ${error?.message}.`, {});
    }
  };

  const commitLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    const index = getValues?.()?.amends?.findIndex(({ id }) => id === row?.id);

    const canSubmit = index !== undefined ? await trigger?.(`amends.${index}`) : false;

    if (canSubmit) {
      if (row?.creating) await createLineItem(row);
      else await updateLineItem(row);
    }
  };

  const onSubmit = async (): Promise<void> => {};

  return (
    <Stack className={classes.LineItems} alignItems="flex-end" gap={1}>
      <GenericTable
        id="line-items"
        columns={columns}
        pageableData={{
          data: items,
          page: {
            offset: 0,
            limit: SERVER_PAGINATION_LIMIT,
            size: items?.length,
            totalCount: items?.length,
          },
        }}
        styles={{
          headerRow: {
            backgroundColor: "#EEEEEE",
            zIndex: 500,
          },
        }}
        toRowModel={toRowModel}
        isMultiEditable
        appendNewRows
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindCancelRowAmendment={table?.bindCancelRowAmendment}
        extensions={
          {
            readonlyParent: !editingParent,
            cancelLineItem: (row: GenericTableRowModel<MarketingAgreementLineItem>) =>
              updateLineItemStatus(row, "cancel"),
          } as MarketingAgreementLineItemExtensions
        }
        onFormSubmit={handleSubmit?.(onSubmit)}
        onRowSubmit={commitLineItem}
        renderNoDataContent={
          <GenericTableNoDataContent>
            <Stack className={parentClasses.NoData} justifyContent="center" alignItems="center">
              <Maybe condition={!hasParent}>
                <Chip
                  className={parentClasses.Chip}
                  icon={<MatIcon value="error_outline" variant="round" size={18} className={parentClasses.InfoIcon} />}
                  label={
                    <Typography className={parentClasses.Text}>Please create a marketing agreement first</Typography>
                  }
                />
              </Maybe>
              <Maybe condition={hasParent && !items?.length}>
                <Box>
                  <Typography>
                    Please click{" "}
                    <Typography component="span" variant="h6" fontWeight="bold">
                      +
                    </Typography>{" "}
                    icon to add line items
                  </Typography>
                </Box>
              </Maybe>
            </Stack>
          </GenericTableNoDataContent>
        }
      />
      <Box display="flex" flexDirection="row" justifyContent="space-between" width="100%" gap={1} sx={{ zIndex: 500 }}>
        <Zoom in={hasParent} unmountOnExit>
          <Tooltip title={!hasPermissionToCreate ? "Insufficient permissions" : ""}>
            <Fab
              className={classes.AddLineItem}
              size="medium"
              onClick={table?.handleAddRow}
              disabled={!hasPermissionToCreate || !hasParent || editingParent || inEditMode}
              sx={{ zIndex: 500 }}
            >
              <MatIcon
                value="add_circle"
                variant="round"
                size={48}
                color={hasPermissionToCreate && hasParent && !editingParent && !inEditMode ? "primary" : "disabled"}
              />
            </Fab>
          </Tooltip>
        </Zoom>
        <Zoom in={hasParent} unmountOnExit>
          <Tooltip title={!hasPermissionToCreate ? "Insufficient permissions" : "Upload line items via CSV"}>
            <Fab
              className={classes.AddLineItem}
              size="medium"
              onClick={triggerFileUpload}
              disabled={!hasPermissionToCreate || !hasParent || editingParent || inEditMode}
              color="primary"
              sx={{
                border: "4px solid #E7E7E7",
                "&:hover": {
                  border: "4px solid white",
                  "*.material-icons": {
                    color: "white",
                  },
                },
              }}
            >
              <MatIcon
                value="upload_file"
                size={28}
                sx={{
                  color: hasPermissionToCreate && hasParent && !editingParent && !inEditMode ? "#E7E7E7" : "gray",
                }}
              />
            </Fab>
          </Tooltip>
        </Zoom>
      </Box>
      <input type="file" ref={fileInputRef} onChange={handleCSVUpload} accept=".csv" style={{ display: "none" }} />
      <GenericDialog
        open={openAddVintageModal}
        title="Add New Vintage"
        onClose={() => toggleAddVintageModal(false)}
        positiveAction={{
          buttonText: "CREATE",
          disabled: !canSubmitNewVintage,
          onClick: () => toggleSubmittingDialogContent(true),
        }}
        negativeAction
        onNegativeClick={() => toggleAddVintageModal(false)}
        classes={{
          root: dialogClasses.AddOption,
          content: dialogClasses.AddOptionContent,
          actions: dialogClasses.Actions,
        }}
      >
        <AddNewVintageForm
          projectId={projectId}
          submitting={submittingDialogContent}
          setCanSumbit={toggleCanSubmitNewVintage}
          setSubmitting={toggleSubmittingDialogContent}
          onSubmitSuccess={(projectVintage) => {
            setTimeout(() => {
              toggleAddVintageModal(false);

              if (projectVintage?.project?.id) {
                onProjectVintageAddition(projectVintage.project.id, projectVintage);
              }

              smartSetValue?.(`amends.${lastAmendedIndex}.projectVintage.id` as any, projectVintage?.id, {
                shouldValidate: !!projectId,
              });
            });
          }}
        />
      </GenericDialog>
      <GenericDialog
        open={openCancelLineItemModal}
        title="Cancel Line Item"
        onClose={() => toggleCancelLineItemModal(false)}
        positiveAction={{
          buttonText: "YES, CANCEL",
          onClick: () => viewingLineItem && cancelLineItem(viewingLineItem as MarketingAgreementLineItem),
          className: dialogClasses.DestructivePositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: () => toggleCancelLineItemModal(false),
        }}
        classes={{
          root: dialogClasses.Dialog,
          title: dialogClasses.DestructiveTitle,
          content: dialogClasses.Content,
          actions: dialogClasses.Actions,
        }}
      >
        <Typography>Are you sure you want to cancel the line item?</Typography>
      </GenericDialog>
    </Stack>
  );
};

export default LineItems;
