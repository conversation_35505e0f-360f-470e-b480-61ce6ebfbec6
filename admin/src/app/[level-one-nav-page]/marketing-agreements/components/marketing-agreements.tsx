import {
  DocumentType,
  AdminMarketingAgreementCreateRequest,
  AdminMarketingAgreementQuery,
  AdminMarketingAgreementQueryResponse,
  AdminMarketingAgreementResponse,
  MarketingAgreementStatus,
  AdminMarketingAgreementUpdateRequest,
  PermissionEnum,
  AdminProjectVintageQueryResponse,
  ProjectVintageRelations,
  AdminProjectVintageResponse,
  uuid,
  AdminOrganizationQueryResponse,
} from "@rubiconcarbon/shared-types";
import { useCallback, useContext, useMemo, useState, type JSX } from "react";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Chip, Stack, Typography } from "@mui/material";
import {
  generateQueryParams,
  isNothing,
  px,
  toDecimal,
  Undefinable,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { useGetSet, useToggle } from "react-use";
import { DocumentTypeUILabel } from "@constants/documents";
import usePerformantState from "@hooks/use-perfomant-state";
import { AxiosContext } from "@providers/axios-provider";
import DocumentsModal, { FileType } from "@components/documents-upload/documents-modal";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import { GenericTableNoDataContent } from "@components/ui/generic-table/components/generic-table-row";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility, { Values } from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { MARKETING_AGREEMENT_COLUMNS } from "../constants/marketing-agreement-columns";
import { MarketingAgreementFormModel, MarketingAgreementModel } from "../models/marketing-agreement";
import { ProjectToVintages } from "../types/expanded-row-types";
import { MarketingAgreementExtensions } from "../types/marketing-agreement";
import LineItems from "./line-tems";
import { NestedColumnExportRecord } from "@components/ui/generic-table/types/generic-table-export";
import useAutoCompleteOptions from "@hooks/use-auto-complete-options";
import { MarketingAgreementTypeUILabel } from "../constants/common";

import classes from "../styles/marketing-agreement.module.scss";
import dialogClasses from "../styles/modal.module.scss";

const FileTypesOptions: FileType[] = [
  {
    label: DocumentTypeUILabel[DocumentType.CONTRACT],
    value: DocumentType.CONTRACT,
  },
];

const MarketingAgreementFormResolver = classValidatorResolver(MarketingAgreementFormModel);

const MarketingAgreements = ({
  counterpartiesResponse,
  marketingAgreementsResponse: serverMarketingAgreementsResponse,
}: {
  counterpartiesResponse: AdminOrganizationQueryResponse;
  marketingAgreementsResponse: AdminMarketingAgreementQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [openCancelAgreementModal, toggleCancelAgreementModal] = useToggle(false);
  const [amendingNestedRows, toggleAmendingNestedRows] = useGetSet<boolean>(false);

  const [viewingRow, setViewingRow] = useState<Undefinable<GenericTableRowModel<MarketingAgreementModel>>>();

  const [projectToVintages, setProjectToVintages] = usePerformantState<ProjectToVintages>({});
  const [payload, setPayload] = useState<AdminMarketingAgreementCreateRequest | AdminMarketingAgreementUpdateRequest>();
  const { table, form } = useGenericTableUtility<MarketingAgreementModel>({
    form: {
      mode: "onSubmit",
      resolver: MarketingAgreementFormResolver as any,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { handleSubmit, watch } = form || {};

  const firstValue = watch("amends.0");

  const creating = useMemo(() => firstValue?.creating, [firstValue?.creating]);
  const editing = useMemo(() => firstValue?.editing, [firstValue?.editing]);
  const readOnly = useMemo(() => !creating && !editing, [creating, editing]);

  const toRowModel = useCallback(
    (row: AdminMarketingAgreementResponse): GenericTableRowModel<MarketingAgreementModel> =>
      ({
        ...row,
        typeF: MarketingAgreementTypeUILabel[row?.type],
      }) as unknown as MarketingAgreementModel,
    [],
  );

  const {
    data: agreementsResponse,
    isMutating: loadingAgreements,
    trigger: refreshMarketingAgreements,
  } = useTriggerRequest<AdminMarketingAgreementQueryResponse, object, object, AdminMarketingAgreementQuery>({
    url: "admin/marketing-agreements",
    queryParams: {
      includeTotalCount: true,
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
    },
    optimisticData: serverMarketingAgreementsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch marketing agreements.");
        logger.error(`Unable to fetch marketing agreements: ${error?.message}`, {});
      },
    },
  });

  const { trigger: commitMA, isMutating: committingMA } = useTriggerRequest<
    AdminMarketingAgreementResponse,
    AdminMarketingAgreementCreateRequest | AdminMarketingAgreementUpdateRequest,
    { id?: uuid },
    object
  >({
    url: `admin/marketing-agreements${creating ? "" : "/{id}"}`,
    method: creating ? "post" : "patch",
    pathParams: {
      id: firstValue?.id as uuid,
    },
    requestBody: payload,
    swrOptions: {
      onError: (error: any) => {
        enqueueError(`Unable to ${creating ? "create" : "update"} marketing agreement.`);
        logger.error(`Unable ${creating ? "create" : "update"} marketing agreement: ${error?.message}`, {});
      },
      onSuccess: async (data) => {
        enqueueSuccess(`Successfully  ${creating ? "created" : "updated"} marketing agreement.`);

        await table?.handleKeepRowAfterAmendment({
          ...toRowModel(data),
          creating,
          editing: !creating,
        } as unknown as GenericTableRowModel<MarketingAgreementModel>);

        if (!creating) {
          await refreshMarketingAgreements();
        }
      },
    },
  });

  const { trigger: cancelMA, isMutating: cancelingMA } = useTriggerRequest<
    AdminMarketingAgreementResponse,
    AdminMarketingAgreementUpdateRequest,
    { id: uuid }
  >({
    url: `admin/marketing-agreements/{id}/cancel`,
    method: "patch",
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to cancel marketing agreement");
        logger.error(`Unable to cancel marketing agreemet: ${error?.message}`, {});
      },
      onSuccess: async () => {
        enqueueSuccess("Successfully  canceled marketing agreement.");
        toggleCancelAgreementModal(false);
        await refreshMarketingAgreements();
      },
    },
  });

  const getVintagesByProject = async (id: uuid): Promise<void> => {
    try {
      const { data } = await api.get<AdminProjectVintageQueryResponse>(
        `admin/project-vintages?${generateQueryParams({
          projectIds: [id],
          limit: SERVER_PAGINATION_LIMIT,
          includeRelations: [ProjectVintageRelations.PROJECT],
        })}`,
      );

      setProjectToVintages({
        ...projectToVintages,
        [id]: {
          data: data?.data,
          fetching: false,
          fetched: true,
        },
      });
    } catch (error: any) {
      enqueueError("Unable to fetch project's vintages data.");
      logger.error(`Unable to fetch project's vintages data. Error: ${error?.message}`, {});
    }
  };

  const counterpartyOptions = useAutoCompleteOptions({
    data: counterpartiesResponse?.data || [],
    keys: ["id", "name"],
    label: (entry) => entry?.name,
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const columns = useMemo(
    () =>
      MARKETING_AGREEMENT_COLUMNS.map((column) => {
        if (column.field === "organization.id") {
          column.valueOptions = counterpartyOptions;
        }

        return column;
      }),
    [counterpartyOptions],
  );

  const cleanupOnDocumentsDialogClose = (): void => {
    toggleDocumentsModal(false);
  };

  const handleProjectVintageAddition = (projectId: uuid, vintage: AdminProjectVintageResponse): void => {
    setProjectToVintages({
      ...projectToVintages,
      [projectId]: {
        ...(projectToVintages?.[projectId] || {}),
        data: [...(projectToVintages?.[projectId]?.data || []), vintage],
      },
    });
  };

  const onSubmit = async (formData: Values<MarketingAgreementModel>): Promise<void> => {
    const {
      amends: [firstValue],
    } = formData;

    const { creating, project, organization, type, floorPrice, status, feeCalculatorUrl, memo } = firstValue || {};

    if (creating) {
      const payload: AdminMarketingAgreementCreateRequest = {
        projectId: project?.id as uuid,
        organizationId: organization?.id as uuid,
        floorPrice: toDecimal(floorPrice),
        feeCalculatorUrl: isNothing(feeCalculatorUrl, ["string"]) ? null : feeCalculatorUrl,
        type: isNothing(type, ["string"]) ? null : type,
        memo: isNothing(memo, ["string"]) ? null : memo,
        ...px({ status }, [undefined, null, false]),
      };

      setPayload(payload);
      setTimeout(async () => await commitMA());
    } else {
      const payload: AdminMarketingAgreementUpdateRequest = {
        organizationId: organization?.id as uuid,
        floorPrice: toDecimal(floorPrice),
        feeCalculatorUrl: isNothing(feeCalculatorUrl, ["string"]) ? null : feeCalculatorUrl,
        type: isNothing(type, ["string"]) ? null : type,
        memo: isNothing(memo, ["string"]) ? null : memo,
        ...px({ status }, [undefined, null, false]),
      };

      setPayload(payload);
      setTimeout(async () => await commitMA());
    }
  };

  return (
    <>
      <GenericTable
        id="marketing-agreements"
        loading={loadingAgreements}
        reloadingRow={loadingAgreements}
        isExpandable={(row) => !!(readOnly || (creating && row?.creating) || (editing && row?.editing))}
        dismissFormOnSubmit={false}
        eager={{
          expand: true,
        }}
        expandOn={{
          create: true,
        }}
        collapseOn={{
          create: false,
        }}
        columns={columns}
        pageableData={agreementsResponse}
        toRowModel={toRowModel}
        sort={{
          sorts: {
            createdAt: "desc",
          },
        }}
        styles={{
          root: {
            maxHeight: "calc(100vh - 190px)",
          },
        }}
        globalSearch={{
          searchKeys: [
            "uiKey",
            "project.registryProjectId",
            "project.name",
            "organization.name",
            "type",
            "typeF",
            "floorPrice",
            "feeCalculatorUrl",
            "status",
          ],
        }}
        export={{
          filename: `MarketingAgreements-${new Date()}`,
          nested: {
            lineItems: {
              uniquenessLabel: "Line Item",
              columns: LINE_ITEM_COLUMNS,
            },
          } as NestedColumnExportRecord<MarketingAgreementModel>,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
            requiredPermission: PermissionEnum.MARKETING_AGREEMENTS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loadingAgreements || committingMA || cancelingMA || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Create",
            requiredPermission: PermissionEnum.MARKETING_AGREEMENTS_CREATE,
            isDisabled: loadingAgreements || committingMA || cancelingMA || creating || editing,
            onClickHandler: table?.handleAddRow,
          },
        ]}
        extensions={
          {
            amendingNestedRows,
            toggleDocumentsModal,
            toggleCancelAgreementModal,
            setViewingRow,
          } as MarketingAgreementExtensions
        }
        renderNoDataContent={
          <GenericTableNoDataContent>
            <Stack className={classes.NoData} justifyContent="center" alignItems="center">
              <Chip
                className={classes.Chip}
                icon={<MatIcon value="error_outline" variant="round" size={18} className={classes.InfoIcon} />}
                label={<Typography className={classes.Text}>Please create a marketing agreement to start</Typography>}
              />
            </Stack>
          </GenericTableNoDataContent>
        }
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindKeepRowAfterAmendment={table?.bindKeepRowAfterAmendment}
        renderExpandContent={(row): JSX.Element => {
          const projectId = row?.project?.id;

          const pvs = projectToVintages?.[projectId];

          if (!!projectId && !pvs?.data && !pvs?.fetching) {
            setProjectToVintages({
              ...projectToVintages,
              [projectId]: {
                fetching: true,
                fetched: false,
              },
            });

            setTimeout(async () => await getVintagesByProject(projectId));
          }

          return (
            <Stack className={classes.ExpandedContent} gap={2}>
              <Typography className={classes.Header}>Delivery Schedule</Typography>
              <LineItems
                hasParent={!row?.creating && row?.status !== MarketingAgreementStatus.CANCELED}
                editingParent={!readOnly}
                maId={row?.creating ? undefined : (row?.id as uuid)}
                items={row?.lineItems}
                projectId={projectId}
                projectVintages={pvs ?? { data: [], fetching: false, fetched: false }}
                toggleAmendingNestedRows={toggleAmendingNestedRows as (nextValue?: boolean) => void}
                refreshMarketingAgreements={refreshMarketingAgreements}
                onProjectVintageAddition={handleProjectVintageAddition}
              />
            </Stack>
          );
        }}
        onFormSubmit={handleSubmit?.(onSubmit)}
      />
      <GenericDialog
        open={openCancelAgreementModal}
        title="Cancel Agreement"
        onClose={() => toggleCancelAgreementModal(false)}
        positiveAction={{
          buttonText: "YES, CANCEL",
          onClick: () => cancelMA({ pathParams: { id: viewingRow?.id as uuid } }),
          className: dialogClasses.DestructivePositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: () => toggleCancelAgreementModal(false),
        }}
        classes={{
          root: dialogClasses.Dialog,
          title: dialogClasses.DestructiveTitle,
          content: dialogClasses.Content,
          actions: dialogClasses.Actions,
        }}
      >
        <Typography>Are you sure you want to cancel the agreement?</Typography>
      </GenericDialog>
      <DocumentsModal
        open={openDocumentsModal}
        relatedUiKey={viewingRow?.uiKey || ''}
        saveTypeOptions={FileTypesOptions}
        canUpload={viewingRow?.status !== MarketingAgreementStatus.CANCELED}
        onPositiveClick={(callback) => callback()}
        onNegativeClick={(callback) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (readOnly) setViewingRow(undefined);
        }}
        onClose={async (callback, amended) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (amended) refreshMarketingAgreements();
          if (readOnly) setViewingRow(undefined);
        }}
      />
    </>
  );
};

export default MarketingAgreements;
