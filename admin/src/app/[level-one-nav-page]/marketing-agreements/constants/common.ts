import { MarketingAgreementType } from "@rubiconcarbon/shared-types";

export const ZERO = 0;

export const MarketingAgreementTypeUILabel: Record<MarketingAgreementType, string> = {
  [MarketingAgreementType.MARKETING_AGREEMENT]: "Marketing Agreement",
  [MarketingAgreementType.OFFTAKE_UPSIDE_REVENUE_SHARE]: "Offtake + Upside Revenue Share",
  [MarketingAgreementType.PAYMENT_ON_DELIVERY]: "Payment on delivery",
  [MarketingAgreementType.PRE_PAID]: "Pre-paid",
};
