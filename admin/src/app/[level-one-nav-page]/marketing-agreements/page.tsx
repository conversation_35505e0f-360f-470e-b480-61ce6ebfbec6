import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, AdminMarketingAgreementQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";
import MarketingAgreements from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Marketing Agreements Page
 *
 * This is a server component that renders the Marketing Agreements page
 */
export default async function MarketingAgreementsPage(): Promise<JSX.Element> {
  const counterpartiesResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationQueryResponse>(
      `admin/organizations?${generateQueryParams({
        includeTotalCount: false,
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        isCounterparty: true,
      })}`,
    ),
  );

  const marketingAgreementsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminMarketingAgreementQueryResponse>(
      `admin/marketing-agreements?${generateQueryParams({
        includeTotalCount: true,
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(counterpartiesResponse)) return counterpartiesResponse;
  if (isValidElement(marketingAgreementsResponse)) return marketingAgreementsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.MARKETING_AGREEMENTS_READ]}>
      <MarketingAgreements
        counterpartiesResponse={counterpartiesResponse as AdminOrganizationQueryResponse}
        marketingAgreementsResponse={marketingAgreementsResponse as AdminMarketingAgreementQueryResponse}
      />
    </AuthorizeServer>
  );
}
