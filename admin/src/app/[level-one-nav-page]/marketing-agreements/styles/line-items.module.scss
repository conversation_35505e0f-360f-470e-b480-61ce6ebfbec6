.LineItems {
    .NoData {
        position: relative;
        right: -30vw;
        
        .Chip {
            height: 40px;
            border-radius: 5px;
            background-color: #E5F6FD;

            .InfoIcon {
                rotate: 180deg;
                color: #1195D6;
            }

            .Text {
                color: #014361;
            }
        }
    }

    .AddLineItem {
        position: sticky;
        right: 20px;
        bottom: 0;
    }
}