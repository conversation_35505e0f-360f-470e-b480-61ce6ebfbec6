.NoData {
    position: relative;
    
    .<PERSON> {
        padding-left: 15px;
        padding-right: 5px;
        width: fit-content;
        height: 40px;
        border-radius: 5px;
        background-color: #E5F6FD;

        .InfoIcon {
            rotate: 180deg;
            color: #1195D6;
        }

        .Text {
            color: #014361;
        }
    }
}

.ExpandedContent {
    padding: 15px;

    .Header {
        font-size: 16px;
        font-weight: 500;
    }
}