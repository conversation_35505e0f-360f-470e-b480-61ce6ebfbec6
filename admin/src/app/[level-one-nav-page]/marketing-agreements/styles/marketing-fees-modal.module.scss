.Dialog {
    max-width: 1000px;

    .Content {
        .Table {
            .TableHeader{
                .TableHeaderColumn  {
                    font-size: 14px;
                    font-weight: 600;
                }
            }

            .TableBody {
                .TableRow {
                    align-items: flex-start;
                    
                    .TableBodyColumn {
                        display: flex;
                        align-items: center;
                        gap: 8px;
    
                        .InputLabel {
                            top: -8px;
                            height: 40px;
    
                            &[data-shrink="true"] {
                                top: 0;
                            }
                        }
    
                        .InputRoot {
                            height: 40px;
    
                            .Input {
                                padding-top: 8px;
                                padding-bottom: 8px;
                            }
                        }

    
                        .InputRootMinMax {
                            min-width: 145px;
                        }
                    }
                }
            }

            .ErrorMessage {
                font-size: 0.75rem;
                font-weight: 400;
                color: #C90005;
            }
        }
    }

    .Actions {
        div {
            width: 100%;

            .ActionButton {
                border-radius: 4px;
            }

            .CancelButton {
                color: #C90005;
            }
        }
    }
}

.DialogSmall {
    max-width: 600px;
}
