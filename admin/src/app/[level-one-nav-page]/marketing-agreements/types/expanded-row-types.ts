import { AdminAllocationResponse, AdminProjectVintageResponse, uuid } from "@rubiconcarbon/shared-types";

export type ProjectVintageEntry = {
  fetching: boolean;
  fetched: boolean;
  data: AdminProjectVintageResponse[];
};
export type ProjectToVintages = Record<uuid, ProjectVintageEntry>;

export type ProjectVintageAllocationEntry = {
  fetching: boolean;
  fetched: boolean;
  data: AdminAllocationResponse[];
};
// key here is actually the project id
export type ProjectVintagesToAllocations = Record<uuid, ProjectVintageAllocationEntry>;
