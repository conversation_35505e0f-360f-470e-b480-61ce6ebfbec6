import { MISSING_DATA } from "@constants/constants";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import usePerformantEffect from "@hooks/use-performant-effect";
import { Box, Button, capitalize, Grid, Paper, Stack, SxProps, Typography, useMediaQuery } from "@mui/material";
import {
  calculator,
  currencyFormat,
  GenericTabItem,
  GenericTabKey,
  GenericTabs,
  Match,
  Maybe,
  numberFormat,
  percentageFormat,
} from "@rubiconcarbon/frontend-shared";
import {
  AdminDocumentResponse,
  AdminOrganizationResponse,
  AdminUserResponse,
  AdminAllocationResponse,
  AssetType,
  AdminGroupedAllocationWithNestedResponse,
  ModelPortfolioRelations,
  AdminModelPortfolioResponse,
  PermissionEnum,
  TransactionType,
  UserStatus,
  AdminMarketNewsResponse,
} from "@rubiconcarbon/shared-types";
import { PropsWithChildren, ReactNode, useCallback, useEffect, useMemo, type JSX } from "react";
import { toOrganizationModel } from "../../utilities/to-model";
import { TableCellStatusChip } from "@components/ui/table-cell-status-chip/table-cell-status-chip";
import useNavigation from "@hooks/use-navigation";
import COLORS from "@components/ui/theme/colors";
import { useStoreProvider } from "@providers/store-provider";
import CustomerTable from "./customer-table";
import { HOLDING_COLUMNS, HoldingModel } from "../constants/holding-columns";
import { toTrimmedTransactionModel } from "@utils/helpers/transaction/to-transaction-models";
import { TRANSACTION_COLUMNS } from "../constants/transaction-columns";
import { QUOTE_COLUMNS, QuoteModel } from "../constants/quote-columns";
import { USER_COLUMNS, UserModel } from "../constants/user-columns";
import { UserRoleLabel } from "@constants/user-role";
import { DOCUMENT_COLUMNS, DocumentModel } from "../constants/document-columns";
import { DocumentTypeUILabel } from "@constants/documents";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import useAuth from "@providers/auth-provider";
import BackButton from "@components/ui/back-button/back-button";
import { px } from "@rubiconcarbon/frontend-shared";
import { FeeTypeUILabel } from "@constants/fee-type";
import { NEWS_COLUMNS } from "../constants/news-columns";
import Link from "next/link";

import organizationClasses from "../styles/organization.module.scss";

export type CustomerTableTabsTypes = "holdings" | "transactions" | "quotes" | "documents" | "users" | "news";

type LabelValuePairProps = {
  label: ReactNode;
  orientation?: "horizontal" | "vertical";
  sx?: SxProps;
};

const LabelValuePair = ({
  label,
  orientation = "horizontal",
  children,
  sx,
}: PropsWithChildren<LabelValuePairProps>): JSX.Element => (
  <Stack
    direction={orientation === "horizontal" ? "row" : "column"}
    justifyContent={orientation === "horizontal" ? "flex-start" : "center"}
    alignItems="flex-start"
    gap={3}
    sx={sx}
  >
    <Box>{label}</Box>
    <Box> {children}</Box>
  </Stack>
);

const CustomerDetails = ({
  organizationResponse,
}: {
  organizationResponse: AdminOrganizationResponse;
}): JSX.Element => {
  const { user } = useAuth();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { pushToPath } = useNavigation();
  const { ephemeralState, updateEphemeralState } = useStoreProvider();
  const isNotLargeScreen = useMediaQuery("(max-width: 1290px)");

  const { customer: customerTabState } = ephemeralState;
  const { viewing: tab } = customerTabState;

  const organization = toOrganizationModel(organizationResponse as any);

  const organizationId = useMemo(() => organization?.id, [organization?.id]);

  const hasCustomerPortfolio = useMemo(
    () => organization?.customerPortfolio?.hasCustomerPortfolio,
    [organization?.customerPortfolio?.hasCustomerPortfolio],
  );
  const hasCounterparty = useMemo(
    () => organization?.counterparty?.hasCounterparty,
    [organization?.counterparty?.hasCounterparty],
  );

  const hasTransactionsPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRANSACTIONS_READ), [user]);
  const hasCustomerQuotesPermission = useMemo(() => user?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_READ), [user]);
  const hasDocumentsPermission = useMemo(() => user?.hasPermission(PermissionEnum.DOCUMENTS_READ), [user]);
  const hasUsersPermission = useMemo(() => user?.hasPermission(PermissionEnum.USERS_READ), [user]);

  useEffect(() => {
    updateEphemeralState("customer.viewing", hasCustomerPortfolio ? "holdings" : "transactions");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasCustomerPortfolio]);

  const emails = useMemo(
    () => organization?.counterparty?.tradeConfirmEmails?.map(({ value }) => value),
    [organization?.counterparty?.tradeConfirmEmails],
  );
  const fees = useMemo(
    () => organization?.counterparty?.defaultFees?.map(({ type, feeType, fee }) => ({ type, feeType, fee })),
    [organization?.counterparty?.defaultFees],
  );
  const buyFee = useMemo(() => fees?.find(({ type }) => type === "buy"), [fees]);
  const sellFee = useMemo(() => fees?.find(({ type }) => type === "sell"), [fees]);

  const tabs = useMemo(
    () =>
      [
        hasCustomerPortfolio
          ? {
              key: "holdings",
              data: "Holdings",
            }
          : null,
        hasTransactionsPermission
          ? {
              key: "transactions",
              data: "Transactions",
            }
          : null,
        hasCustomerQuotesPermission
          ? {
              key: "quotes",
              data: "Customer Quotes",
            }
          : null,
        hasDocumentsPermission
          ? {
              key: "documents",
              data: "Documents",
            }
          : null,
        hasUsersPermission && hasCustomerPortfolio
          ? {
              key: "users",
              data: "Users",
            }
          : null,
        {
          key: "news",
          data: "Corporate News",
        },
      ].filter((entry) => !!entry),
    [
      hasTransactionsPermission,
      hasCustomerQuotesPermission,
      hasDocumentsPermission,
      hasUsersPermission,
      hasCustomerPortfolio,
    ],
  ) as GenericTabItem<CustomerTableTabsTypes, string>[];

  usePerformantEffect(() => {
    if (organization?.name) updateBreadcrumbName("Organizations", organization?.name);
  }, [organization?.name, updateBreadcrumbName]);

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={organizationClasses.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  const StatusSection = useCallback(
    (): JSX.Element => (
      <>
        <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
          <LabelValuePair
            label={
              <Typography variant="body2" width={150} fontWeight="bold">
                Status:{" "}
              </Typography>
            }
          >
            <TableCellStatusChip
              data={organization}
              path={{ value: "customerPortfolio.isEnabled", label: "customerPortfolio.isEnabledF" }}
              conditionalSx={() => ({
                ...px({ chip: { marginTop: -1 } }),
              })}
            />
          </LabelValuePair>
        </Grid>

        <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
          <LabelValuePair
            label={
              <Typography variant="body2" width={150} fontWeight="bold">
                Onboard Status:{" "}
              </Typography>
            }
          >
            <TableCellStatusChip
              data={organization}
              path={{ value: "customerPortfolio.isOnboarded", label: "customerPortfolio.isOnboardedF" }}
              conditionalSx={() => ({
                ...px({ chip: { marginTop: -1 } }),
              })}
            />
          </LabelValuePair>
        </Grid>
      </>
    ),
    [isNotLargeScreen, organization],
  );

  const CounterpartySection = useCallback(
    (): JSX.Element => (
      <>
        <Grid item xs={12}>
          <LabelValuePair
            label={
              <Typography variant="body2" width={150} fontWeight="bold">
                Fees:{" "}
              </Typography>
            }
            sx={{
              "> div:nth-child(2)": {
                display: "flex",
                gap: 2,
                width: "100%",
              },
            }}
          >
            <>
              <Maybe condition={!!buyFee || !!sellFee}>
                <Maybe condition={!!buyFee}>
                  <Grid item xs={12}>
                    <LabelValuePair
                      label={<Typography variant="body2">Buy Fee</Typography>}
                      sx={{
                        flexDirection: "column",
                        "> div:first-child": {
                          width: "100%",
                          "> p": {
                            padding: "3px",
                            backgroundColor: "lightgray",
                          },
                        },
                        gap: 1,
                      }}
                    >
                      <Stack gap={1}>
                        <LabelValuePair
                          label={
                            <Typography variant="body2" width={75}>
                              Fee Type:{" "}
                            </Typography>
                          }
                        >
                          <Typography fontSize="0.875rem">{FeeTypeUILabel[buyFee?.feeType]}</Typography>
                        </LabelValuePair>
                        <LabelValuePair
                          label={
                            <Typography variant="body2" width={75}>
                              Fee:{" "}
                            </Typography>
                          }
                        >
                          <Typography fontSize="0.875rem">
                            {buyFee?.feeType === "percentage"
                              ? percentageFormat(buyFee?.fee, { scale: 0.01 })
                              : currencyFormat(buyFee?.fee)}
                          </Typography>
                        </LabelValuePair>
                      </Stack>
                    </LabelValuePair>
                  </Grid>
                </Maybe>
                <Maybe condition={!!sellFee}>
                  <Grid item xs={12}>
                    <LabelValuePair
                      label={<Typography variant="body2">Sell Fee</Typography>}
                      sx={{
                        flexDirection: "column",
                        "> div:first-child": {
                          width: "100%",
                          "> p": {
                            padding: "3px",
                            backgroundColor: "lightgray",
                          },
                        },
                        gap: 1,
                      }}
                    >
                      <Stack gap={1}>
                        <LabelValuePair
                          label={
                            <Typography variant="body2" width={75}>
                              Fee Type:{" "}
                            </Typography>
                          }
                        >
                          <Typography fontSize="0.875rem">{FeeTypeUILabel[sellFee?.feeType]}</Typography>
                        </LabelValuePair>
                        <LabelValuePair
                          label={
                            <Typography variant="body2" width={75}>
                              Fee:{" "}
                            </Typography>
                          }
                        >
                          <Typography fontSize="0.875rem">
                            {sellFee?.feeType === "percentage"
                              ? percentageFormat(sellFee?.fee, { scale: 0.01 })
                              : currencyFormat(sellFee?.fee)}
                          </Typography>
                        </LabelValuePair>
                      </Stack>
                    </LabelValuePair>
                  </Grid>
                </Maybe>
              </Maybe>
              <Maybe condition={!buyFee && !sellFee}>{MISSING_DATA}</Maybe>
            </>
          </LabelValuePair>
        </Grid>

        <Grid item xs={12}>
          <LabelValuePair
            label={
              <Typography variant="body2" width={150} fontWeight="bold">
                Emails:{" "}
              </Typography>
            }
          >
            <Maybe condition={emails?.length > 0}>
              <Stack>
                {emails?.map((email) => (
                  <Typography key={email} fontSize="0.875rem">
                    {email}
                  </Typography>
                ))}
              </Stack>
            </Maybe>
            <Maybe condition={emails?.length === 0}>{MISSING_DATA}</Maybe>
          </LabelValuePair>
        </Grid>
      </>
    ),
    [buyFee, emails, sellFee],
  );

  const AdditionalInfoSection = useCallback(
  (): JSX.Element => (
    <>
      <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
        <LabelValuePair
          label={
            <Typography variant="body2" width={150} fontWeight="bold">
              External Reference:{" "}
            </Typography>
          }
        >
          {!!organization?.externalReference ? (
            <Link href={organization?.externalReference} target="_blank">
              <Typography variant="body2" color="primary">
                {organization?.externalReference}
              </Typography>
            </Link>
          ) : (
            <Typography variant="body2" color="primary">
              {MISSING_DATA}
            </Typography>
          )}
        </LabelValuePair>
      </Grid>
      <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
        <LabelValuePair
          label={
            <Typography variant="body2" width={150} fontWeight="bold">
              Memo:{" "}
            </Typography>
          }
        >
          <Typography variant="body2" color="primary" sx={{ maxHeight: "120px", overflowY: "auto" }}>
            {organization?.memo ?? MISSING_DATA}
          </Typography>
        </LabelValuePair>
      </Grid>
    </>
  ),
  [isNotLargeScreen, organization],
);

  return (
    <Stack gap={2}>
      <Stack component={Paper} padding={2} gap={4}>
        <Grid container gap={0.5} rowGap={2}>
          <Maybe condition={hasCustomerPortfolio}>
            <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={150} fontWeight="bold">
                    Account Manager:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {organization?.customerPortfolio?.rubiconManager?.name ?? MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>

            <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={150} fontWeight="bold">
                    Salesforce Account ID:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {organization?.customerPortfolio?.salesforceIdentifier ?? MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>
          </Maybe>

          <Maybe condition={!hasCustomerPortfolio}>
            <StatusSection />
          </Maybe>

          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Created Date:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {organization?.createdAtF ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>
        </Grid>

        <Grid container gap={0.5} rowGap={2}>
          <Maybe condition={hasCustomerPortfolio}>
            <StatusSection />
          </Maybe>
          <Maybe condition={!hasCustomerPortfolio && hasCounterparty}>
            <CounterpartySection />
          </Maybe>
        </Grid>

        <Maybe condition={hasCustomerPortfolio && hasCounterparty}>
          <Grid container gap={0.5} rowGap={2}>
            <CounterpartySection />
          </Grid>
        </Maybe>

        <AdditionalInfoSection />

        <Stack direction="row" gap={2}>
          <Button
            variant="contained"
            sx={{ width: 80, height: 30 }}
            onClick={() => pushToPath("edit-organization")}
            disabled={!user.hasPermission(PermissionEnum.ORGANIZATIONS_UPDATE)}
          >
            Edit
          </Button>
          <BackButton />
        </Stack>
      </Stack>
      <Stack>
        <GenericTabs
          tabs={tabs}
          value={tab}
          renderTab={renderTab}
          onTabChange={(key: GenericTabKey): void => updateEphemeralState("customer.viewing", key)}
          classes={{
            root: organizationClasses.Tabs,
            tab: organizationClasses.Tab,
            active: organizationClasses.Active,
          }}
        />
        <Match
          value={tab}
          cases={[
            {
              case: "holdings",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/organizations/{id}/holdings"
                  pathParams={{ id: organizationId }}
                  queryParams={{
                    assetTypes: [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
                  }}
                  columns={HOLDING_COLUMNS}
                  searchKeys={["productF", "type", "amountAllocated", "fop"]}
                  toResponse={(data: AdminGroupedAllocationWithNestedResponse) => ({
                    data: data?.allocations,
                    page: {
                      offset: 0,
                      limit: data?.allocations?.length || 0,
                      size: data?.allocations?.length || 0,
                    },
                  })}
                  sorts={{
                    amountAllocated: "desc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch organization holdings.");
                    logger.error(`Unable to fetch organization holdings: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AdminAllocationResponse, allRows: AdminAllocationResponse[]): HoldingModel => {
                    let vintageName = "";
                    let registryProjectId = "";
                    let projectId = "";
                    let projectName = "";
                    let riskBufferPercentage = null;
                    let rctStandard = null;
                    let isScienceTeamApproved = null;
                    let suspended = null;
                    let productF = "";
                    let isRctEligible = null;

                    const sum = allRows?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0);

                    const isRctVintage = row?.asset?.type === AssetType.REGISTRY_VINTAGE;

                    if (isRctVintage) {
                      vintageName = row?.asset?.projectVintageName;
                      registryProjectId = row?.asset?.registryProjectId;
                      projectId = row?.asset?.projectId;
                      projectName = row?.asset?.name;
                      riskBufferPercentage = row?.asset?.riskBufferPercentage;
                      rctStandard = row?.asset?.isRctStandard;
                      isScienceTeamApproved = row?.asset?.isScienceTeamApproved;
                      suspended = row?.asset?.isSuspended;
                      isRctEligible = row?.asset?.isRctEligible;

                      productF = `${vintageName} ${registryProjectId} ${projectName}`;
                    } else productF = row?.asset?.name;

                    return {
                      id: row?.asset?.id,
                      productF,
                      type: isRctVintage ? "Project" : "Portfolio",
                      amountAllocated: row?.amountAllocated,
                      fop: row?.amountAllocated / sum,
                      portfolioName: row?.asset?.name,
                      vintageName,
                      registryProjectId,
                      projectId: projectId as any,
                      projectName,
                      riskBufferPercentage,
                      rctStandard,
                      isScienceTeamApproved,
                      suspended,
                      isRctEligible,
                    };
                  }}
                  toolbarItems={(allRows: AdminAllocationResponse[]) => (
                      <div>
                        Total Holdings:{" "}
                        <strong>
                          {numberFormat(
                            allRows?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0),
                            { fallback: MISSING_DATA },
                          )}
                        </strong>
                      </div>
                    )}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                    header: {
                      "div [class^='MuiStack-root']": {
                        flexDirection: "row-reverse",
                        gap: 2,
                      },
                    },
                  }}
                  triggerCondition={!!organization?.id}
                />
              ),
            },
            {
              case: "transactions",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/transactions"
                  queryParams={{
                    counterpartyId: organizationId,
                    types: [
                      TransactionType.TRADE,
                      TransactionType.PURCHASE,
                      TransactionType.RETIREMENT,
                      TransactionType.FORWARD_LINE_ITEM,
                    ],
                  }}
                  columns={TRANSACTION_COLUMNS}
                  searchKeys={[
                    "uiKey",
                    "typeF",
                    "counterpartyName",
                    "productF",
                    "totalQuantity",
                    "totalPrice",
                    "status",
                    "updatedAtF",
                  ]}
                  sorts={{
                    updatedAtF: "desc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch transactions.");
                    logger.error(`Unable to fetch transactions: ${error?.message}`, {});
                  }}
                  toRowModel={toTrimmedTransactionModel}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "quotes",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/model-portfolios"
                  queryParams={{
                    organizationId,
                    includeRelations: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS],
                  }}
                  columns={QUOTE_COLUMNS}
                  searchKeys={["uiKey", "name", "status", "unitPrice", "priceEstimate", "totalAmount", "updatedAtF"]}
                  sorts={
                    {
                      updatedAtF: "desc",
                    } as any
                  }
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer quotes.");
                    logger.error(`Unable to fetch customer quotes: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AdminModelPortfolioResponse): QuoteModel => {
                    const totalAmount = row?.modelPortfolioComponents?.reduce(
                      (sum, { amountAllocated }) => sum + amountAllocated,
                      0,
                    );

                    return {
                      ...row,
                      unitPrice: calculator(row?.priceEstimate, { treatNothingAsNaN: true })
                        .divide(totalAmount)
                        .calculate()
                        ?.toNumber(),
                      totalAmount,
                      updatedAtF: dateFormatterEST(row?.updatedAt?.toLocaleString()),
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "documents",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/documents"
                  queryParams={{
                    organizationId,
                  }}
                  columns={DOCUMENT_COLUMNS}
                  searchKeys={["filename", "typeF", "createdAtF"]}
                  sorts={{
                    createdAtF: "asc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer documents.");
                    logger.error(`Unable to fetch customer documents: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AdminDocumentResponse): DocumentModel => {
                    return {
                      ...row,
                      typeF: DocumentTypeUILabel[row?.type],
                      createdAtF: dateFormatterEST(row?.createdAt?.toLocaleString()),
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "users",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/organizations/{id}/users"
                  pathParams={{ id: organizationId }}
                  columns={USER_COLUMNS}
                  searchKeys={["name", "rolesF", "email", "createdAt", "statusF"]}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer users.");
                    logger.error(`Unable to fetch customer users: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AdminUserResponse): UserModel => {
                    return {
                      ...row,
                      rolesF: row?.organizationUserRoles?.reduce(
                        (accum, role, index) => accum?.concat(index === 0 ? "" : ", ", UserRoleLabel[role]),
                        "",
                      ),
                      createdAt: row?.createdAt,
                      statusF: capitalize(row?.status),
                      statusBool: row?.status === UserStatus.ENABLED,
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "news",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/market-news/"
                  queryParams={{
                    organizationId,
                  }}
                  exportable={false}
                  columns={NEWS_COLUMNS}
                  searchKeys={["header", "summary", "source", "hitword", "projects"]}
                  searchPlaceholder="Article title, content, publisher or hitword"
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer market news.");
                    logger.error(`Unable to fetch customer market news: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AdminMarketNewsResponse): AdminMarketNewsResponse => {
                    return row;
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                  hideHeader
                />
              ),
            },
                      ]}
        />
      </Stack>
    </Stack>
  );
};

export default CustomerDetails;
