import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { AllKeys, GenericRecord, NonEmptyArray, useRequest } from "@rubiconcarbon/frontend-shared";
import { BaseQuery } from "@rubiconcarbon/shared-types";
import { CustomerTableTabsTypes } from "./customer-details";
import GenericTable from "@components/ui/generic-table";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { GenericTableSortType } from "@components/ui/generic-table/types/generic-table-sort";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTablePageable } from "@components/ui/generic-table/types/generic-table-pageable";
import { ReactNode, useCallback, useMemo, type JSX } from "react";
import { capitalize, SxProps } from "@mui/material";

type CustomerTableProps<T, D> = {
  tab: CustomerTableTabsTypes;
  url: string;
  pathParams?: GenericRecord;
  queryParams?: GenericRecord & BaseQuery;
  columns: GenericTableColumn<T>[];
  searchKeys: NonEmptyArray<AllKeys<T>>;
  searchPlaceholder?: string;
  sorts?: Partial<Record<AllKeys<T>, GenericTableSortType>>;
  styles?: GenericRecord<string, SxProps>;
  triggerCondition?: boolean;
  exportable?: boolean;
  hideHeader?: boolean;
  hideFooter?: boolean;
  toolbarItems?: (allRows: D[]) => ReactNode;
  onError: (error: any) => void;
  toResponse?: (data: any) => GenericTablePageable<D>;
  toRowModel?: (row: D, allRows?: D[]) => T;
};

const CustomerTable = <T, D = T>({
  tab,
  url,
  pathParams = {},
  queryParams = {},
  columns,
  searchKeys,
  searchPlaceholder,
  sorts,
  styles = {},

  triggerCondition = true,
  exportable = true,
  hideHeader = false,
  hideFooter = false,

  toolbarItems,

  toResponse,
  toRowModel,

  onError,
}: CustomerTableProps<T, D>): JSX.Element => {
  const { table } = useGenericTableUtility<T>({});

  const { data, isLoading } = useRequest<GenericTablePageable<D>>({
    url: triggerCondition ? url : null,
    pathParams,
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    },
    swrOptions: {
      onError,
    },
  });

  const response = useMemo(() => (toResponse ? toResponse(data) : data), [data, toResponse]);

  const toActualRowModel = useCallback(
    (row: D) => (toRowModel ? toRowModel(row, response?.data) : row) as GenericTableRowModel<T>,
    [response?.data, toRowModel],
  );

  const ToolbarItems = useMemo(
    () => (toolbarItems ? toolbarItems(response?.data) : null),
    [response?.data, toolbarItems],
  );

  const ExportConfig = exportable
    ? {
        export: {
          filename: `${capitalize(tab)}-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        },
        toolbarActionButtons: [
          {
            children: "Export",
            startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: isLoading || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
        ],
      }
    : null;

  return (
    <GenericTable
      id={tab}
      loading={isLoading}
      pageableData={response}
      columns={columns}
      toRowModel={toActualRowModel}
      globalSearch={{
        searchKeys,
        placeholder: searchPlaceholder,
      }}
      sort={{
        sorts,
      }}
      export={ExportConfig?.export}
      toolbarActionButtons={ExportConfig?.toolbarActionButtons}
      toolbarItems={ToolbarItems}
      styles={styles}
      hide={{
        header: hideHeader,
        footer: hideFooter,
      }}
    />
  );
};

export default CustomerTable;
