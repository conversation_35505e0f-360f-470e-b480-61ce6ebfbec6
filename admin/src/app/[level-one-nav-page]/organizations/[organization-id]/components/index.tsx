"use client";;
import { AdminOrganizationResponse } from "@rubiconcarbon/shared-types";
import CustomerDetailsComponent from "./customer-details";

import type { JSX } from "react";

export default function CustomerDetails({
  organizationResponse,
}: {
  organizationResponse: AdminOrganizationResponse;
}): JSX.Element {
  return (
    <CustomerDetailsComponent
      organizationResponse={organizationResponse}
    />
  );
}
