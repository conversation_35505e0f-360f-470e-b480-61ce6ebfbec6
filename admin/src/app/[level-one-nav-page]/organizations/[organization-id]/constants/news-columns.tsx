import MarketIntelligenceItem from "@app/market-intelligence/components/market-intelligence-item";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { AdminMarketNewsResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

export const NEWS_COLUMNS: GenericTableColumn<AdminMarketNewsResponse>[] = [
  {
    field: "entry" as any,
    label: "",
    renderDataCell: (row: AdminMarketNewsResponse): JSX.Element => (
      <MarketIntelligenceItem
        style={{ padding: "2px !important", borderBottom: "none !important" }}
        item={row}
        isExtendedFunc
        isReadOnly
      />
    ),
  },
];
