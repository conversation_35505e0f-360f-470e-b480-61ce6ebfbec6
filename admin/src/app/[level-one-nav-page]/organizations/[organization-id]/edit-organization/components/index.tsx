"use client";;
import OrganizationFormComponent from "../../../components/form";
import { AdminOrganizationResponse, AdminUserResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

export default function OrganizationForm({
  organizationResponse,
  managers,
}: {
  organizationResponse?: AdminOrganizationResponse;
  managers: AdminUserResponse[];
}): JSX.Element {
  return <OrganizationFormComponent organizationResponse={organizationResponse} managers={managers} />;
}
