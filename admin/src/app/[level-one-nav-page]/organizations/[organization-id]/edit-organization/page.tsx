import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminOrganizationResponse,
  AdminUserResponse,
  AdminUserQueryResponse,
  OrganizationUserRole,
  PermissionEnum,
  UserStatus,
} from "@rubiconcarbon/shared-types";
import OrganizationForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Edit Organization Page
 *
 * This is a server component that renders the Edit Organization page
 */
export default async function EditOrganizationPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  let organizationResponse;

  if (!!id) {
    organizationResponse = await withErrorHandling(async () =>
      baseApiRequest<AdminOrganizationResponse>(`admin/organizations/${id}`),
    );
  }

  const usersResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminUserQueryResponse>(
      `admin/organizations/${process.env.NEXT_PUBLIC_ADMIN_ORGANIZATION}/users?${generateQueryParams({
        status: UserStatus.ENABLED,
        roles: [OrganizationUserRole.MANAGER],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;
  if (isValidElement(usersResponse)) return usersResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_UPDATE]}>
      <OrganizationForm
        organizationResponse={organizationResponse as AdminOrganizationResponse}
        managers={(usersResponse as AdminUserQueryResponse)?.data as AdminUserResponse[]}
      />
    </AuthorizeServer>
  );
}
