import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import CustomerDetails from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Organization Detail Page
 *
 * This is a server component that renders the organization detail page
 */
export default async function OrganizationDetailPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  const organizationResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationResponse>(`admin/organizations/${id}`),
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_READ]}>
      <CustomerDetails
        organizationResponse={organizationResponse as AdminOrganizationResponse}
      />
    </AuthorizeServer>
  );
}
