import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import DocumentUpload from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Upload Document Page
 *
 * This is a server component that renders the Upload Document page
 */
export default async function UploadDocumentPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  const organizationResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationResponse>(`admin/organizations/${id}`),
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.DOCUMENTS_CREATE]}>
      <DocumentUpload organizationResponse={organizationResponse as AdminOrganizationResponse} />
    </AuthorizeServer>
  );
}
