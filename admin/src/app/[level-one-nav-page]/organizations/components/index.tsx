"use client";;
import { AdminOrganizationQueryResponse } from "@rubiconcarbon/shared-types";
import OrganizationsComponent from "./organizations";

import type { JSX } from "react";

export default function Organizations({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element {
  return (
    <OrganizationsComponent
      organizationsResponse={organizationsResponse}
    />
  );
}
