import { AdminOrganizationQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { toOrganizationModel } from "../utilities/to-model";
import useNavigation from "@hooks/use-navigation";
import useAuth from "@providers/auth-provider";
import { COLUMNS } from "../constants/columns";
import GenericTable from "@components/ui/generic-table";
import MatIcon from "@components/ui/mat-icon/mat-icon";

import type { JSX } from "react";

const Organizations = ({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element => {
  const { user } = useAuth();
  const { pushToPath } = useNavigation();

  return (
    <GenericTable
      id="portal"
      eager={{
        expand: true,
      }}
      pageableData={organizationsResponse}
      columns={COLUMNS}
      toRowModel={toOrganizationModel}
      globalSearch={{
        searchKeys: ["name", "tag", "customerAccessStatusF", "isOnboardedStatusF", "createdAtF"],
      }}
      sort={{
        sorts: {
          createdAtF: "desc",
        },
      }}
      toolbarActionButtons={[
        {
          children: "Organization",
          startIcon: (
            <MatIcon
              value="add"
              variant="round"
              size={20}
              sx={{ color: user.hasPermission(PermissionEnum.ORGANIZATIONS_CREATE) ? "white" : "gray" }}
            />
          ),
          requiredPermission: PermissionEnum.ORGANIZATIONS_CREATE,
          onClickHandler: () => pushToPath("/create-organization"),
        },
      ]}
      styles={{
        root: {
          maxHeight: "calc(100vh - 250px)",
        },
      }}
    />
  );
};

export default Organizations;
