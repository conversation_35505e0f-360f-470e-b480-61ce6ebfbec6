import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { TableCellStatusChip } from "@components/ui/table-cell-status-chip/table-cell-status-chip";
import { OrganizationModel } from "@models/organization";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { AuthContext } from "@providers/auth-provider";
import { MouseEvent, useContext, useMemo, type JSX } from "react";
import { Divider, IconButton, Stack, Tooltip } from "@mui/material";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import useNavigation from "@hooks/use-navigation";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { px } from "@rubiconcarbon/frontend-shared";
import OrganizationName from "@components/ui/organization-name/organization-name";

const ActionsCell = ({ row }: { row: GenericTableRowModel<OrganizationModel> }): JSX.Element => {
  const { user } = useContext(AuthContext);
  const { pushToPath } = useNavigation();

  const permissions = useMemo(() => user.permissions, [user.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.ORGANIZATIONS_UPDATE);
  const hasPermissionToUpload = permissions.includes(PermissionEnum.DOCUMENTS_CREATE);

  return (
    <Stack direction="row" alignItems="center" gap={1}>
      <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : "Edit organization"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            pushToPath(`${row.id}/edit-organization`);
          }}
        >
          <MatIcon value="edit" variant="round" size={24} color={!hasPermissionToEdit ? "disabled" : "primary"} />
        </IconButton>
      </Tooltip>
      <Divider sx={{ height: 28.5 }} orientation="vertical" />
      <Tooltip title={!hasPermissionToUpload ? "Insufficient permissions" : "Upload document"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            pushToPath(`${row.id}/upload-document`);
          }}
        >
          <MatIcon
            value="file_upload"
            variant="round"
            size={24}
            color={!hasPermissionToEdit ? "disabled" : "primary"}
          />
        </IconButton>
      </Tooltip>
    </Stack>
  );
};

export const COLUMNS: GenericTableColumn<OrganizationModel>[] = [
  {
    field: "name",
    label: "Organization",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row): JSX.Element => <OrganizationName organization={row} style={{ fontSize: 14 }} />,
  },
  {
    field: "tag",
    label: "Tag",
    minWidth: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
  },
  {
    field: "customerAccessStatusF",
    label: "Access to Customer Portal",
    width: GenericTableFieldSizeEnum.xlarge,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <TableCellStatusChip
        data={row}
        path={{ value: "customerAccessStatus", label: "customerAccessStatusF" }}
        conditionalSx={(_, labelValue) => ({
          ...px({
            chip: labelValue === "No Access" ? { backgroundColor: "#EEEEEE" } : null,
            label: labelValue === "No Access" ? { color: "#757575" } : null,
          }),
        })}
      />
    ),
  },
  {
    field: "isOnboardedStatusF",
    label: "Onboarded As",
    minWidth: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
    renderDataCell: (row): JSX.Element => (
      <TableCellStatusChip data={row} path={{ value: "isOnboardedStatus", label: "isOnboardedStatusF" }} />
    ),
  },
  {
    field: "createdAtF",
    label: "Create Date",
    type: "date",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];