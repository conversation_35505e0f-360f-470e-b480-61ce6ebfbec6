import { AuthorizeServer } from "@app/authorize-server";
import { AdminUserQueryResponse, OrganizationUserRole, PermissionEnum, AdminUserResponse, UserStatus } from "@rubiconcarbon/shared-types";
import OrganizationForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Create Portal Organization Page
 *
 * This is a server component that renders the Create Portal Organization page
 */
export default async function CreatePortalOrganizationPage(): Promise<JSX.Element> {
  const usersResponse = await withErrorHandling(
    async () =>
      baseApiRequest<AdminUserQueryResponse>(
        `admin/organizations/${process.env.NEXT_PUBLIC_ADMIN_ORGANIZATION}/users?${generateQueryParams({
          status: UserStatus.ENABLED,
          roles: [OrganizationUserRole.MANAGER],
        })}`,
      ),
    );

  // Check if the result is a server error
  if (isValidElement(usersResponse)) return usersResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_CREATE]}>
      <OrganizationForm managers={(usersResponse as AdminUserQueryResponse)?.data as AdminUserResponse[]} />
    </AuthorizeServer>
  );
}
