import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import Organizations from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * Organizations Page
 *
 * This is a server component that renders the Organizations page
 */
export default async function OrganizationsPage(): Promise<JSX.Element> {
  const organizationsResponse = await withErrorHandling(
    async () =>
      baseApiRequest<AdminOrganizationQueryResponse>(
        `admin/organizations?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
        })}`,
      ),
  );

  // Check if the result is a server error
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer
      partiallyAuthorize
      permissions={[PermissionEnum.ORGANIZATIONS_READ]}
    >
      <Organizations
        organizationsResponse={organizationsResponse as AdminOrganizationQueryResponse}
      />
    </AuthorizeServer>
  );
}
