import { AdminOrganizationResponse } from "@rubiconcarbon/shared-types";
import { OrganizationTag } from "@constants/organization-tag";
import { isNothing } from "@rubiconcarbon/frontend-shared";

/**
 * Determines the appropriate organization tag based on the organization's properties and onboarding status.
 *
 * This function analyzes an organization's structure to determine its classification tag based on:
 * - Whether it has a customer portfolio
 * - Whether it has a counterparty
 * - Whether these components are onboarded (when checkOnboarded is true)
 *
 * @param organization - The organization data to analyze
 * @param checkOnboarded - When true, considers onboarding status rather than just existence
 *                         Default is false (just checks existence)
 * @returns The appropriate OrganizationTag enum value based on the organization's structure
 */
export const getOrganizationTag = (
  organization: AdminOrganizationResponse,
  checkOnboarded: boolean = false,
): OrganizationTag => {
  const hasCustomerPortfolio = !isNothing(organization?.customerPortfolio);
  const hasCounterparty = !isNothing(organization?.counterparty);
  const portfolioOnboarded = hasCustomerPortfolio && !!organization?.customerPortfolio?.isOnboarded;
  const counterpartyOnboarded = hasCounterparty && !!organization?.counterparty?.isOnboarded;

  if (checkOnboarded) {
    if (portfolioOnboarded && counterpartyOnboarded) {
      return OrganizationTag.CounterpartyAndCustomerPortfolio;
    } else if (portfolioOnboarded) {
      return OrganizationTag.CustomerPortfolio;
    } else if (counterpartyOnboarded) {
      return OrganizationTag.Counterparty;
    }
    return OrganizationTag.NotOnboarded;
  } else {
    if (hasCustomerPortfolio && hasCounterparty) {
      return OrganizationTag.CounterpartyAndCustomerPortfolio;
    } else if (hasCustomerPortfolio) {
      return OrganizationTag.CustomerPortfolio;
    } else if (hasCounterparty) {
      return OrganizationTag.Counterparty;
    }
  }

  return OrganizationTag.Missing;
};
