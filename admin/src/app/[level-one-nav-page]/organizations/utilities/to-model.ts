import { AdminDefaultFeeResponse, AdminOrganizationResponse, CounterpartyFeeType } from "@rubiconcarbon/shared-types";
import { OrganizationModel } from "@models/organization";
import { calculator, CalculatorInput, isNothing, toBoolean } from "@rubiconcarbon/frontend-shared";
import { getOrganizationTag } from "./get-organization-tag";
import dateFormatter from "@utils/formatters/date-formatter";

export const toOrganizationModel = (row: AdminOrganizationResponse): OrganizationModel => {
  const hasCustomerPortfolio = !isNothing(row?.customerPortfolio);
  const hasCounterparty = !isNothing(row?.counterparty);

  return {
    ...row,
    tag: getOrganizationTag(row),
    customerAccessStatus: !!row?.customerPortfolio?.isOnboarded,
    customerAccessStatusF: !!row?.customerPortfolio?.isOnboarded ? "Enabled" : "No Access",
    isOnboardedStatus: !!row?.counterparty?.isOnboarded || !!row?.customerPortfolio?.isOnboarded,
    isOnboardedStatusF: getOrganizationTag(row, true),
    memo: row?.memo,
    externalReference: row?.externalReference,
    createdAtF: dateFormatter(row?.createdAt?.toString(), "MM/dd/yyyy hh:mm:ss a"),
    counterparty: {
      ...row?.counterparty,
      hasCounterparty,
      isEnabled: toBoolean(row?.counterparty?.isEnabled),
      isEnabledF: !!toBoolean(row?.counterparty?.isEnabled) ? "Enabled" : "Disabled",
      isOnboarded: toBoolean(row?.counterparty?.isOnboarded),
      isOnboardedF: !!toBoolean(row?.counterparty?.isOnboarded) ? "Onboarded" : "Not Onboarded",
      defaultFees:
        row?.counterparty?.defaultFees?.map((fee) => ({
          type: fee.type,
          feeType: fee.feeType,
          fee: calculator(fee[fee.feeType.replace(/[-_]+(.)/g, (_, c: string) => c.toUpperCase()).concat("Fee") as keyof AdminDefaultFeeResponse] as CalculatorInput)
            .multiply(fee.feeType === CounterpartyFeeType.PERCENTAGE ? 100 : 1)
            .calculate()
            .toString(),
        })) || [],
      tradeConfirmEmails: row?.counterparty?.tradeConfirmEmails?.map((value) => ({ value })) || [],
    },
    customerPortfolio: {
      ...row?.customerPortfolio,
      hasCustomerPortfolio,
      isEnabled: toBoolean(row?.customerPortfolio?.isEnabled),
      isEnabledF: !!toBoolean(row?.customerPortfolio?.isEnabled) ? "Enabled" : "Disabled",
      isOnboarded: toBoolean(row?.customerPortfolio?.isOnboarded),
      isOnboardedF: !!toBoolean(row?.customerPortfolio?.isOnboarded) ? "Onboarded" : "Not Onboarded",
      rubiconManager: {
        ...row?.customerPortfolio?.rubiconManager,
        statusF: !!row?.customerPortfolio?.rubiconManager?.status ? "Enabled" : "Disabled",
      },
    },
  };
};
