import React, { type JSX } from "react";
import { AdminModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { Box } from "@mui/material";
import PortfolioSandboxComponents from "./portfolio-sandbox-table/portfolio-sandbox-components";
import { Maybe, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";

export default function EditPortfolioSandbox({
  modelPortfolio: serverModelPortfolio,
}: {
  modelPortfolio: AdminModelPortfolioResponse;
}): JSX.Element {
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const { data: modelPortfolio, trigger: refreshModelPortfolio } = useTriggerRequest<AdminModelPortfolioResponse>({
    url: `/admin/model-portfolios/${serverModelPortfolio?.id}`,
    optimisticData: serverModelPortfolio,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load model portfolio");
        logger.error(`Unable to load model portfolio: ${error?.message}`, {});
      },
    },
  });

  return (
    <Box mt={2}>
      <Maybe condition={!!modelPortfolio}>
        <PortfolioSandboxComponents
          mockPortfolio={modelPortfolio}
          refreshPortfolio={refreshModelPortfolio}
        />
      </Maybe>
    </Box>
  );
}
