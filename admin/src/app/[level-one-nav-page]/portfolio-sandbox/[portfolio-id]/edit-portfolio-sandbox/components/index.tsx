"use client";;
import Page from "@components/layout/containers/page";
import EditPortfolioSandboxComponent from "./edit-portfolio-sandbox";
import { AdminModelPortfolioResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const EditPortfolioSandbox = ({ modelPortfolio }: { modelPortfolio: AdminModelPortfolioResponse }): JSX.Element => {
  return (
    <Page>
      <EditPortfolioSandboxComponent modelPortfolio={modelPortfolio} />
    </Page>
  );
};

export default EditPortfolioSandbox;
