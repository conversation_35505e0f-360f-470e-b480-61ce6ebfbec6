import { EligibilityAndAccreditationsToUILabel } from "@constants/projects";
import {
  CountryRegions,
  ProjectEligibilityAccreditation,
  ProjectEmissionsImpactType,
  ProjectRiskScore,
  ProjectTypeCategory,
} from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import { FilterType } from "./portfolio-sandbox-filter";

export interface FilterData {
  filter: {
    emissionsImpactTypes: string[];
    regions: string[];
    projectTypesCategories: string[];
    eligibilityAccreditations: string[];
    integrityGrades: string[];
  };
}

export interface FilterDefinition {
  id: string;
  name: string;
  value: string[];
  label: string;
  width?: number;
  type: FilterType[];
}

export function buildFilterData(): FilterData {
  const filterData: FilterData = {
    filter: {
      emissionsImpactTypes: [],
      regions: [],
      projectTypesCategories: [],
      eligibilityAccreditations: [],
      integrityGrades: [],
    },
  };

  Object.keys(ProjectRiskScore).map((key) => filterData.filter.integrityGrades.push(ProjectRiskScore[key as keyof typeof ProjectRiskScore]));
  Object.keys(CountryRegions).map((key) => filterData.filter.regions.push(CountryRegions[key as keyof typeof CountryRegions]));
  Object.keys(ProjectTypeCategory).map((key) =>
    filterData.filter.projectTypesCategories.push(ProjectTypeCategory[key as keyof typeof ProjectTypeCategory]),
  );
  Object.keys(ProjectEmissionsImpactType).map((key) =>
    filterData.filter.emissionsImpactTypes.push(ProjectEmissionsImpactType[key as keyof typeof ProjectEmissionsImpactType]),
  );
  Object.keys(ProjectEligibilityAccreditation).map((key) => {
    const displayItem = EligibilityAndAccreditationsToUILabel[ProjectEligibilityAccreditation[key as keyof typeof ProjectEligibilityAccreditation]];
    if (displayItem) {
      filterData.filter.eligibilityAccreditations.push(displayItem);
    }
  });

  Object.keys(filterData.filter).forEach((key) => {
    if ((filterData.filter as any)[key]) {
      (filterData.filter as any)[key] = (filterData.filter as any)[key]?.sort((a: string, b: string) =>
        a.localeCompare(b),
      );
    }
  });

  return filterData;
}
const getObjKeysByValue = (selection: string[], obj: any): string[] => {
  const list: string[] = [];
  selection?.forEach((item) => {
    const foundKey = Object.keys(obj).find((key) => obj[key] === item);
    if (foundKey) list.push(foundKey);
  });

  return list;
};

export function createQueryFromFilter(selection: FilterData): string {
  let query = "";
  if (!!selection && !isEmpty(selection?.filter)) {
    Object.keys(selection?.filter).forEach((key) => {
      if (!isEmpty(selection?.filter[key as keyof typeof selection.filter])) {
        switch (key) {
          case "eligibilityAccreditations": {
            const list = getObjKeysByValue(selection?.filter[key], EligibilityAndAccreditationsToUILabel);
            if (!isEmpty(list)) {
              query = query + `&${key + "=" + list.join(",")}`;
            }
            break;
          }
          default:
            query = query + `&${key}=${selection.filter[key as keyof typeof selection.filter].join(",")}`;
            break;
        }
      }
    });
  }
  return query;
}
