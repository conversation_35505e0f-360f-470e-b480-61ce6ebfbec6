import React, { useState, useEffect, useMemo, useCallback, type JSX } from "react";
import {
  Box,
  Checkbox,
  Chip,
  FormControl,
  Grow,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Stack,
  Typography,
} from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import { buildFilterData, FilterData, FilterDefinition } from "./filter-model";
import { useMediaQuery } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { isEmpty } from "lodash";
import CancelIcon from "@mui/icons-material/Cancel";
import ActionButton from "@components/ui/action-button/action-button-enhanced";

import classes from "../../styles/portfolio-sandbox-filter.module.scss";

export enum FilterType {
  AVAILABLE_PROJECTS,
  OTHER_PROJECTS,
}

const emptyFilter: FilterData = {
  filter: {
    emissionsImpactTypes: [],
    regions: [],
    projectTypesCategories: [],
    eligibilityAccreditations: [],
    integrityGrades: [],
  },
};

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
      minHeight: 40,
    },
  },
  sx: {
    "&& .MuiMenuItem-root": {
      backgroundColor: "white",
    },
  },
};

export default function PortfolioSandboxFilter(props: {
  initialSelection: FilterData;
  isDisabled: boolean;
  filterType: FilterType;
  onFilterHandler: (selection: FilterData) => void;
}): JSX.Element {
  const { initialSelection, isDisabled, filterType, onFilterHandler } = props;

  const [isFilterEmpty, setIsFilterEmpty] = useState<boolean>(true);
  const [filterData, setFilterData] = useState<FilterData>();
  const [selectedFilter, setSelectedFilter] = useState<FilterData>(emptyFilter);
  const [activeFilter, setActiveFilter] = useState<FilterData>(emptyFilter);
  const isSmallScreen = useMediaQuery("(max-width: 1300px)");

  useEffect(() => {
    const data = buildFilterData();
    setFilterData(data);

    if (initialSelection) {
      setSelectedFilter(initialSelection);
      setActiveFilter(initialSelection);
    }
  }, [initialSelection]);

  useEffect(() => {
    if (
      !!activeFilter &&
      (activeFilter?.filter?.projectTypesCategories?.length > 0 ||
        activeFilter?.filter?.eligibilityAccreditations?.length > 0 ||
        activeFilter?.filter?.emissionsImpactTypes?.length > 0 ||
        activeFilter?.filter?.integrityGrades?.length > 0 ||
        activeFilter?.filter?.regions?.length > 0)
    )
      setIsFilterEmpty(false);
    else setIsFilterEmpty(true);
  }, [activeFilter]);

  const handleFilterChange = useCallback(
    (event: SelectChangeEvent<string[]>) => {
      const {
        target: { value },
      } = event;
      setSelectedFilter({
        ...selectedFilter,
        filter: {
          ...selectedFilter.filter,
          [event.target.name]: typeof value === "string" ? value.split(",") : value,
        },
      });
    },
    [selectedFilter],
  );

  const handleApply = useCallback(() => {
    onFilterHandler(selectedFilter);
    setActiveFilter(selectedFilter);
  }, [selectedFilter, onFilterHandler]);

  const handleDeleteFilter = useCallback(
    (input: string): void => {
      let newFilter = emptyFilter;
      if (!isEmpty(input)) {
        newFilter = { ...selectedFilter };
        for (const [key, value] of Object.entries(newFilter.filter)) {
          const keyTyped = key as keyof typeof newFilter.filter;
          if (!!value && value.length > 0) {
            newFilter.filter[keyTyped] = value.filter((item) => item !== input);
          }
        }
      }

      setSelectedFilter(newFilter);
      setActiveFilter(newFilter);
      onFilterHandler(newFilter);
    },
    [selectedFilter, onFilterHandler],
  );

  const filterDef: FilterDefinition[] = useMemo(
    () => [
      {
        id: "emissions",
        name: "emissionsImpactTypes",
        value: selectedFilter.filter.emissionsImpactTypes,
        label: "Emissions Impact",
        type: [FilterType.AVAILABLE_PROJECTS],
      },
      {
        id: "regions",
        name: "regions",
        value: selectedFilter.filter.regions,
        label: "Geography",
        type: [FilterType.AVAILABLE_PROJECTS, FilterType.OTHER_PROJECTS],
      },
      {
        id: "categories",
        name: "projectTypesCategories",
        value: selectedFilter.filter.projectTypesCategories,
        label: "Categories",
        type: [FilterType.AVAILABLE_PROJECTS, FilterType.OTHER_PROJECTS],
      },
      {
        id: "eligibility",
        name: "eligibilityAccreditations",
        value: selectedFilter.filter.eligibilityAccreditations,
        label: "Eligibility & Accreditations",
        type: [FilterType.AVAILABLE_PROJECTS],
      },
      {
        id: "integrityGrade",
        name: "integrityGrades",
        value: selectedFilter.filter.integrityGrades,
        label: "Integrity Grade",
        width: 180,
        type: [FilterType.AVAILABLE_PROJECTS],
      },
    ],
    [selectedFilter],
  );

  return (
    <Stack direction="column" spacing={0} className={classes.container}>
      <Grow in={true} style={{ transformOrigin: "0 0 0" }} {...{ timeout: 500 }}>
        <Stack direction={isSmallScreen ? "column" : "row"}>
          <Typography className={classes.title}>Filters:</Typography>
          {filterDef
            ?.filter((fd) => fd?.type?.includes(filterType))
            .map((fd) => (
              <FormControl key={fd.id} sx={{ m: 1, width: isSmallScreen ? 350 : (fd?.width ?? 250) }}>
                <InputLabel size="small" id={`${fd.id}-label`}>
                  {fd.label}
                </InputLabel>
                <Select
                  labelId={`${fd.id}-label`}
                  id={fd.id}
                  name={fd.name}
                  disabled={isDisabled}
                  multiple
                  size="small"
                  value={fd.value}
                  renderValue={(selected): string =>
                    selected.length > 1 ? `${selected.length} selected` : selected.toString()
                  }
                  onChange={handleFilterChange}
                  input={<OutlinedInput label={fd.label} />}
                  MenuProps={{
                    ...MenuProps,
                    ...{
                      PaperProps: {
                        style: {
                          width: fd.width,
                        },
                      },
                    },
                  }}
                  sx={{
                    backgroundColor: "white",
                    ".& MuiMenuItem-root": {
                      backgroudColor: "red",
                    },
                  }}
                >
                  {!!filterData &&
                    (filterData.filter as any)[fd.name].map((type: string) => (
                      <MenuItem key={type} value={type} sx={{ paddingLeft: "0px" }}>
                        <Checkbox
                          checked={fd.value.findIndex((item) => item === type) >= 0}
                          sx={{
                            "&.Mui-checked": {
                              color: "#04CDB7",
                            },
                          }}
                        />
                        <ListItemText primary={type} />
                      </MenuItem>
                    ))}
                  <Maybe condition={!filterData || (filterData.filter as any)[fd.name].length < 1}>
                    <MenuItem disabled={true}>
                      <ListItemText primary="No options" />
                    </MenuItem>
                  </Maybe>
                </Select>
              </FormControl>
            ))}
          <Box sx={{ paddingLeft: "10px", display: "inline-flex" }} mb={2}>
            <Box sx={{ paddingLeft: isSmallScreen ? "0px" : "10px" }}>
              <ActionButton
                onClickHandler={handleApply}
                isDisabled={isDisabled}
                style={{ textTransform: "none", marginTop: "10px" }}
              >
                Apply
              </ActionButton>
            </Box>
          </Box>
        </Stack>
      </Grow>
      <Maybe condition={!isFilterEmpty}>
        <Grow in={true} style={{ transformOrigin: "0 0 0" }} {...{ timeout: 500 }}>
          <Box className={classes.activeFilterContainer}>
            <Stack direction="row" spacing={0}>
              <Box
                sx={{
                  marginTop: "4px",
                  height: "25px",
                  paddingRight: "20px",
                  borderRight: 2,
                  borderColor: "rgba(22, 122, 143, 1)",
                }}
              >
                <Typography component="span" className={classes.filterLabel}>
                  Active Filters
                </Typography>
              </Box>
              <Box sx={{ marginTop: "-8px" }}>
                {activeFilter.filter.projectTypesCategories
                  .concat(activeFilter.filter.eligibilityAccreditations)
                  .concat(activeFilter.filter.emissionsImpactTypes)
                  .concat(activeFilter.filter.integrityGrades)
                  .concat(activeFilter.filter.regions)
                  .map((f) => (
                    <Chip
                      key={f}
                      sx={{ marginLeft: "10px" }}
                      variant="outlined"
                      className={classes.chip}
                      deleteIcon={<CancelIcon sx={{ fontSize: 17 }} />}
                      label={f}
                      onDelete={(): void => handleDeleteFilter(f)}
                    />
                  ))}
              </Box>
            </Stack>
            <Box mt={0.4}>
              <a href="#" onClick={(): void => handleDeleteFilter("")} className={classes.anchorButton}>
                Clear all filters
              </a>
            </Box>
          </Box>
        </Grow>
      </Maybe>
    </Stack>
  );
}
