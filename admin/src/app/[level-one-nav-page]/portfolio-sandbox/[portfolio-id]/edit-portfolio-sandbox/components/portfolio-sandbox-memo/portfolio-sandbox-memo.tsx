import React, { useState, useEffect, useCallback, useContext, useMemo, type JSX } from "react";
import { AdminModelPortfolioResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { Box, Stack, Typography, TextField } from "@mui/material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AxiosContext } from "@providers/axios-provider";
import { KeyedMutator } from "swr";
import useAuth from "@providers/auth-provider";
import { isEmpty } from "lodash";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { MemoModel } from "./memo-model";
import { Controller, useForm } from "react-hook-form";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

const MEMO_MAX_LENGTH = 5000;

export default function PortfolioSandboxMemo(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  refreshPortfolio: KeyedMutator<AdminModelPortfolioResponse>;
}): JSX.Element {
  const { mockPortfolio, refreshPortfolio } = props;
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { api } = useContext(AxiosContext);
  const { user: loginUser } = useAuth();

  const [isConfirmationOpen, setIsConfirmationOpen] = useState<boolean>(false);

  const MemoResolver = useMemo(() => classValidatorResolver(MemoModel), []);

  const {
    formState: { isDirty },
    control,
    reset,
    watch,
  } = useForm<MemoModel>({
    resolver: MemoResolver,
    mode: "onSubmit",
    defaultValues: new MemoModel(),
  });

  const memoValue = watch("memo");

  useEffect(() => {
    if (mockPortfolio) {
      reset({
        memo: mockPortfolio?.memo ?? "",
      });
    }
  }, [mockPortfolio, reset]);

  const onConfirmMemoUpdate = useCallback(async () => {
    setIsConfirmationOpen(false);
    const payload = {
      memo: isEmpty(memoValue) ? null : memoValue,
    };

    try {
      await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      enqueueSuccess("Successfully updated portfolio memo");
      refreshPortfolio();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to update portfolio memo");
    }
  }, [memoValue, setIsConfirmationOpen, enqueueSuccess, enqueueError, api, refreshPortfolio, mockPortfolio?.id]);

  const memoDialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmMemoUpdate(),
    },
  ];

  return (
    <>
      <Stack direction="column" sx={{ width: "100%", fliat: "right", textAlign: "right" }} gap={2}>
        <fieldset style={{ display: "contents" }}>
          <Controller
            control={control}
            name="memo"
            render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
              <TextField
                id="memo"
                label="Memo (optional)"
                multiline
                rows={4}
                sx={{ paddingBottom: "50px", paddingRight: "-10px" }}
                value={value ?? ""}
                inputProps={{ ref, maxLength: MEMO_MAX_LENGTH }}
                {...otherProps}
                disabled={!loginUser?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_COMMENT)}
              />
            )}
          />
        </fieldset>
        <Box>
          <Box sx={{ float: "right", paddingRight: "15px" }}>
            <ActionButton
              onClickHandler={() => setIsConfirmationOpen(true)}
              isDisabled={!isDirty || !loginUser?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_COMMENT)}
              requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_COMMENT}
              style={{ position: "relative", textTransform: "none", bottom: "110px" }}
            >
              Comment
            </ActionButton>
          </Box>
        </Box>
      </Stack>
      <ConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={memoDialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          Are you sure you would like to update the memo for this portfolio?
        </Typography>
      </ConfirmationModal>
    </>
  );
}
