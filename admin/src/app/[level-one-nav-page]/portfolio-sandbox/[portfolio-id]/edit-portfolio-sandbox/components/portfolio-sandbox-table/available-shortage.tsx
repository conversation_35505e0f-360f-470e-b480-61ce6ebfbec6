import React, { type JSX } from "react";
import { Tooltip } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { getShortageAmount } from "./portfolio-sandbox-model";
import SyncAltIcon from "@mui/icons-material/SyncAlt";
import { AdminBookTypeGroupedAllocationResponse } from "@rubiconcarbon/shared-types";

export default function AvailableShortage(props: {
  quantity: number;
  availableAmount: number;
  assetAllocationsByBookType: AdminBookTypeGroupedAllocationResponse[];
}): JSX.Element {
  const { quantity, availableAmount, assetAllocationsByBookType } = props;
  const availableShortage = +getShortageAmount(quantity, availableAmount, assetAllocationsByBookType);
  return (
    <>
      <Maybe condition={availableShortage < 0}>
        <Tooltip title={"Some credits are reserved or allocated and therefore unavailable."}>
          <SyncAltIcon sx={{ color: "red", marginTop: "-2px" }} />
        </Tooltip>
      </Maybe>
    </>
  );
}
