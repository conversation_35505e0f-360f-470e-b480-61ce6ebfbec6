import { BookType } from "@rubiconcarbon/shared-types";

export const PORTFOLIO_COLUMNS_EXTENDED = [
  "",
  "Project",
  "Type",
  "Integrity Grade",
  "Country",
  "Vintage",
  "Required Buffer",
  "Buffer Category",
  "Quantity",
  "Shortage",
  "Cost Basis",
  "MTM",
  "Algorithmic Price",
  "Sell Price",
  "Actions",
];

export const PORTFOLIO_COLUMNS_RESTRICTED = [
  "",
  "Project",
  "Type",
  "Integrity Grade",
  "Country",
  "Vintage",
  "Quantity",
  "",
];

export const AllRubiconAvailableBookTypes = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_DEFAULT,
  BookType.REHABILITATION_DEFAULT,
];

export const AllRubiconHoldingBookTypes = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_CUSTOM,
  BookType.PORTFOLIO_DEFAULT,
  BookType.REHABILITATION_DEFAULT,
  BookType.PORTFOLIO_PUBLIC,
  BookType.PORTFOLIO_RESERVES,
  BookType.PORTFOLIO_CUSTOMER,
];
