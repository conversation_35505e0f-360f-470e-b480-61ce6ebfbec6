import { Typography, Box } from "@mui/material";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

import type { JSX } from "react";

export default function EraseEstimateConfirmationModal(props: {
  dialogButtons: ButtonDef[];
  isOpen: boolean;
  onClose: () => void;
}): JSX.Element {
  const { dialogButtons, isOpen, onClose } = props;

  return (
    <Box>
      <ConfirmationModal
        isOpen={isOpen}
        onClose={onClose}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          {"Updating the portfolio composition will erase the price estimate."}
          <br /> {"Do you want to continue?"}
        </Typography>
      </ConfirmationModal>
    </Box>
  );
}
