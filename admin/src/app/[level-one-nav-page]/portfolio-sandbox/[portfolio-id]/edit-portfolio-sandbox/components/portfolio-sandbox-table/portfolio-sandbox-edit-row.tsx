import React, { type JSX, useState, useEffect, use<PERSON><PERSON>back, useContext, useMemo } from "react";
import { Stack, IconButton, TableCell, TableRow, createFilterOptions, Typography, Box, SxProps } from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import {
  uuid,
  AdminProjectResponse,
  AdminProjectVintageResponse,
  AdminBufferCategoryResponse,
} from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import CancelIcon from "@mui/icons-material/Cancel";
import { AxiosContext } from "@providers/axios-provider";
import integerFormat from "@utils/formatters/integer-format";
import {
  ColDef,
  PortfolioRow,
  Project,
  Vintage,
  VintageFormField,
  PortfolioComponentResponse,
  getInventoryShortage,
  validateRequiredNumberInput,
  isValidRow,
  handleQuantityChange,
  convertStringToNumber,
  BufferCategory,
  getEmptyPortfolioRow,
  ProjectPopulationType,
  isValueBetweenThresholdLimits,
  SELL_PRICE_THRESHOLD_PERCENT,
} from "./portfolio-sandbox-model";
import Decimal from "decimal.js";
import { MISSING_DATA } from "@constants/constants";
import { Nullable, percentageFormat, Undefinable } from "@rubiconcarbon/frontend-shared";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import { isEmpty } from "lodash";
import decimalFormat from "@utils/formatters/decimal-format";
import IntegrityScore from "@components/integrity-score/integrity-score-chart";
import InventoryShortage from "./inventory-shortage";
import ProjectLink from "@app/projects/[project-id]/components/project-details/project-link";
import SellPriceConfirmation from "./sell-price-confirmation";
import BaseRow from "../portfolio-sandbox-row";
import currencyFormat from "@utils/formatters/currency-format";

import classes from "../../styles/styles.module.scss";

const baseOverrideMTMStyle: SxProps = {
  textAlign: "center",
  height: "40px",
  paddingLeft: "5px",
  paddingRight: "5px",
};

const overrideMTMStyle: SxProps = {
  backgroundColor: "rgb(237, 247, 237)",
};

const getCategoryById = (id: Undefinable<uuid>, bufferCategories: AdminBufferCategoryResponse[]): Undefinable<string> => {
  if (!!id && !isEmpty(bufferCategories)) {
    return bufferCategories.find((c) => c.id === id)?.name;
  }
  return undefined;
};

const validateRow = (row: PortfolioRow): PortfolioRow => {
  return {
    ...row,
    amountAllocated: validateRequiredNumberInput(row.amountAllocated),
    bufferCategory: {
      ...row.bufferCategory,
    },
    costBasis: {
      ...row.costBasis,
    },
    portfolioManagerEstimate: {
      ...row.portfolioManagerEstimate,
    },
  };
};

export default function Row(props: {
  row: PortfolioComponentResponse;
  idx: uuid;
  editIdx?: uuid;
  isRestricted?: boolean;
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  deleteHandler: (id: uuid) => void;
  submitEditRow: (updatedRow: PortfolioRow) => void;
  availableProjects: Project[];
  bufferCategories: AdminBufferCategoryResponse[];
  isActionDisabled?: boolean;
  projectsPopulationType?: ProjectPopulationType;
  hasEstimate: boolean;
}): JSX.Element {
  const {
    row,
    idx,
    editIdx,
    startEditing,
    stopEditing,
    deleteHandler,
    submitEditRow,
    availableProjects,
    bufferCategories,
    isActionDisabled = false,
    isRestricted = false,
    projectsPopulationType = ProjectPopulationType.NOT_APPLICABLE,
    hasEstimate,
  } = props;
  const anyRow = row as any;
  
  const [portfolioRow, setPortfolioRow] = useState<PortfolioRow>(getEmptyPortfolioRow());
  const [availableVintages, setAvailableVintages] = useState<Vintage[]>();
  const [isConfirmSellPriceOpen, setIsConfirmSellPriceOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const filter = createFilterOptions();
  const currentlyEditing = idx === editIdx;

  const isCustomVintage: boolean = useMemo(() => !portfolioRow?.vintageInterval?.value?.id, [portfolioRow]);

  const getAvailableVintages = useCallback(
    async (id: uuid): Promise<AdminProjectVintageResponse[]> => {
      const vintagesResponse = await api.get<AdminProjectVintageResponse[]>(`admin/projects/${id}/project-vintages`);
      return vintagesResponse.data;
    },
    [api],
  );

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const name = event.target.name;
      const row = { ...portfolioRow };
      if (name === 'costBasis' || name === 'portfolioManagerEstimate') {
        (row[name] as { value: string }).value = value;
      }
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferPercentageChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const row = { ...portfolioRow };
      if (!row.bufferPercentage) {
        row.bufferPercentage = {};
      }
      row.bufferPercentage.value = !isEmpty(value) ? ((convertStringToNumber(value) ?? 0) / 100).toString() : undefined;
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferCategoryChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: BufferCategory): void => {
      const row = { ...portfolioRow };
      if (!row.bufferCategory) {
        row.bufferCategory = {};
      }
      row.bufferCategory.value = newValue;
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onQuantityChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      setPortfolioRow(handleQuantityChange(portfolioRow, event.target.value, projectsPopulationType));
    },
    [portfolioRow, projectsPopulationType],
  );

  const projectSelectionHandler = useCallback(
    async (_: React.ChangeEvent<HTMLInputElement>, newValue: AdminProjectResponse | null): Promise<void> => {
      if (newValue?.name) {
        //updating both project id and project name
        setPortfolioRow({
          ...portfolioRow,
          registryProjectId: { value: newValue ?? undefined },
          vintageInterval: { value: { name: "" } },
          projectName: { value: newValue?.name },
        });
      } else {
        setPortfolioRow({
          ...portfolioRow,
          registryProjectId: { value: newValue ?? undefined },
          vintageInterval: { value: { name: "" } },
        });
      }

      //Retrieving vintages
      if (newValue?.registryProjectId) {
        const vintages = await getAvailableVintages(newValue.id);
        if (vintages) {
          const result: Vintage[] = vintages.map((v) => ({
            id: v.id,
            name: v.name,
          }));

          setAvailableVintages(result);
        }
      }
    },
    [getAvailableVintages, portfolioRow],
  );

  const vintageSelectionHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: Vintage): void => {
      //1. typed a value and clicked enter
      if (typeof newValue === "string") {
        const newVintageInterval: VintageFormField = {
          value: { name: newValue },
        };
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: newVintageInterval,
        });
      } else if (newValue && newValue.inputValue) {
        //2. selected to add a new item
        // Create a new value from the user input
        const newVintageInterval: VintageFormField = {
          value: { name: newValue.name },
        };
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: newVintageInterval,
        });
      } else {
        //3. Selected an existing item from the list - regular flow
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: { value: newValue },
        });
      }
    },
    [portfolioRow],
  );

  const projectCategoryDefaultProps = useMemo(
    () => ({
      options: bufferCategories ?? [],
      getOptionLabel: (option: AdminBufferCategoryResponse): string => (option?.name ? option.name : ""),
      isOptionEqualToValue: (option: AdminBufferCategoryResponse, value: AdminBufferCategoryResponse): boolean => option.id === value.id,
    }),
    [bufferCategories],
  );

  const projectRegistryIdDefaultProps = useMemo(
    () => ({
      options: availableProjects ?? [],
      getOptionLabel: (option: Project): string => (option?.registryProjectId ? option.registryProjectId : ""),
      isOptionEqualToValue: (option: Project, value: Project): boolean => option.id === value.id,
    }),
    [availableProjects],
  );

  const vintagesDefaultProps = useMemo(
    () => ({
      options: availableVintages ?? [], //
      getOptionLabel: (option: Vintage): string => {
        if (typeof option === "string") {
          return option;
        }
        // Add "xxx" option created dynamically
        if (option.inputValue) {
          return option.inputValue;
        }
        return option?.name ?? '';
      },
      isOptionEqualToValue: (option: Vintage, value: Vintage): boolean => option.id === value.id,
      filterOptions: (options: Vintage[], params: { inputValue: string }): unknown[] => {
        const filtered = filter(options, {
          inputValue: params.inputValue,
          getOptionLabel: (option: unknown): string => {
            if (typeof option === 'string') {
              return option;
            }
            const vintageOption = option as Vintage;
            if (vintageOption?.inputValue) {
              return vintageOption.inputValue;
            }
            return vintageOption?.name ?? '';
          },
        });
        // Suggest the creation of a new value
        if (params.inputValue !== "") {
          filtered.push({
            inputValue: `+ Add ${params.inputValue}`,
            name: params.inputValue,
          });
        }
        return filtered;
      },
    }),
    [availableVintages, filter],
  );

  const existingRowDef = useMemo<ColDef[]>(
    () => [
      {
        id: "rctEligible",
        title: "",
        type: "display",
        value: null,
        mode: "display",
        formatter: {
          func: (x: any): JSX.Element => {
            const vintage = {
              isRctEligible: x.get("isRctEligible"), // todo : @kofi/@adir where is this set from?
              riskBufferPercentage: x.get("bufferPercentage") as any,
              project: {
                suspended: x.get("suspended"),
                rctStandard: x.get("rctStandard"),
                isScienceTeamApproved: x.get("isScienceTeamApproved"),
              },
            } as any;

            return (
              <Typography variant="body2" component="div" sx={{ marginTop: "2px" }}>
                <RCTEligibilityChip vintage={vintage} />
              </Typography>
            );
          },
          inputFields: ["bufferPercentage", "suspended", "rctStandard", "isScienceTeamApproved", "isRctEligible"],
        },
      },
      {
        id: "registryProjectId",
        title: "Project",
        type: "autocomplete",
        value: portfolioRow?.registryProjectId?.value,
        onChange: projectSelectionHandler,
        helperText: portfolioRow?.registryProjectId?.message,
        error: portfolioRow?.registryProjectId?.error,
        defaultProps: projectRegistryIdDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.registryProjectId?.value?.id) &&
          !isEmpty(portfolioRow?.registryProjectId?.value?.registryProjectId)
            ? "this is a custom project"
            : "",
        mode: "display",
        formatter: {
          func: (x: any): JSX.Element => {
            return (
              <>
                <div>
                  <ProjectLink
                    projectId={anyRow?.project.id}
                    content={`${anyRow?.project.registryProjectId} - ${anyRow?.project.name}`} // todo : double check
                  />
                  <Stack direction="row" gap={1}>
                    <Maybe condition={x.get("suspended") === true}>
                      <SuspendedChip />
                    </Maybe>
                  </Stack>
                </div>
              </>
            );
          },
          inputFields: ["registryProjectId", "suspended", "rctStandard", "projectName"],
        },
      },
      {
        id: "type",
        title: "Type",
        type: "display",
        value: anyRow?.project?.projectType?.type ?? MISSING_DATA,
      },
      {
        id: "integrityGradeScore",
        title: "Rubicon Carbon Integrity Grade",
        type: "display",
        value: anyRow?.project?.integrityGradeScore ?? MISSING_DATA,
        formatter: {
          func: (): JSX.Element => {
            const integrityGradeScore =
              anyRow?.project?.integrityGradeScore ||
              availableProjects?.find((p) => p.id === anyRow?.project?.id)?.integrityGradeScore;

            return (
              <>
                <Maybe condition={!!integrityGradeScore}>
                  <Box sx={{ marginTop: "-5px" }}>
                    <IntegrityScore
                      score={Math.round(integrityGradeScore ?? 0)}
                      separate
                      hideSubtext
                      className={classes.integrityGrade}
                    />
                  </Box>
                </Maybe>
                <Maybe condition={!integrityGradeScore}>
                  <Box>{MISSING_DATA}</Box>
                </Maybe>
              </>
            );
          },
        },
      },
      {
        id: "country",
        title: "Country",
        type: "display",
        value: anyRow?.project?.country?.name ?? MISSING_DATA,
      },
      {
        id: "vintageInterval",
        title: "Vintage",
        type: "autocomplete",
        value: portfolioRow?.vintageInterval?.value,
        onChange: vintageSelectionHandler,
        helperText: portfolioRow?.vintageInterval?.message,
        error: portfolioRow?.vintageInterval?.error,
        defaultProps: vintagesDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.vintageInterval?.value?.id) && !isEmpty(portfolioRow?.vintageInterval?.value?.name)
            ? "this is a custom vintage"
            : "",
        mode: "display",
      },
      {
        id: "bufferPercentage",
        title: "Required Buffer",
        type: isCustomVintage ? "number" : "display",
        value: portfolioRow?.bufferPercentage?.value,
        onChange: onBufferPercentageChangeHandler,
        helperText: portfolioRow?.bufferPercentage?.message,
        error: portfolioRow?.bufferPercentage?.error,
        formatter: { 
          func: (input: any): Nullable<string> => {
            if (typeof input === 'number') {
              return decimalFormat(input * 100);
            }
            const value = input instanceof Map ? input.get('bufferPercentage') : input;
            return decimalFormat(Number(value) * 100);
          },
        },
        mode: "edit",
        hidden: isRestricted,
        valueFormatter: percentageFormat,
        suffix: "%",
      },
      {
        id: "bufferCategory",
        title: "Buffer Category",
        type: isCustomVintage ? "autocomplete" : "display",
        value:
          !!portfolioRow?.vintageInterval?.value?.id || !currentlyEditing
            ? portfolioRow?.bufferCategory?.value?.name || MISSING_DATA
            : portfolioRow?.bufferCategory?.value,
        onChange: onBufferCategoryChangeHandler,
        helperText: portfolioRow?.bufferCategory?.message,
        error: portfolioRow?.bufferCategory?.error,
        defaultProps: projectCategoryDefaultProps as any,
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "amountAllocated",
        title: "Quantity",
        type: "number",
        value: portfolioRow?.amountAllocated?.value,
        onChange: onQuantityChangeHandler,
        helperText: portfolioRow?.amountAllocated?.message,
        error: portfolioRow?.amountAllocated?.error,
        formatter: { func: integerFormat as any },
        mode: "edit",
      },
      {
        id: "inventoryShortage",
        title: "Inventory Shortage",
        type: "display",
        value: MISSING_DATA,
        formatter: {
          func: (): JSX.Element => (
            <InventoryShortage
              projectsPopulationType={projectsPopulationType}
              availableAmount={anyRow?.amountAvailable ?? 0}
              portfolioRow={portfolioRow}
            />
          ),
        },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "costBasis",
        title: "Cost Basis",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.costBasis?.value ?? null,
        onChange: onChangeHandler,
        helperText: portfolioRow?.costBasis?.message,
        error: portfolioRow?.costBasis?.error,
        decimalScale: 4,
        prefix: "$",
        formatter: { func: (x: any): Nullable<string> => currencyFormat(+x, 4) },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "portfolioManagerEstimate",
        title: "MTM",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.portfolioManagerEstimate?.value ?? null,
        onChange: onChangeHandler,
        helperText: portfolioRow?.portfolioManagerEstimate?.message,
        error: portfolioRow?.portfolioManagerEstimate?.error,
        decimalScale: 2,
        prefix: "$",
        formatter: { func: (x: any): Nullable<string> => currencyFormat(+x) },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "markupPrice",
        title: "algorithmicPrice",
        type: "display",
        value: portfolioRow?.markupPrice?.value ?? null,
        formatter: {
          func: (x: any): Nullable<string> => (!!x ? currencyFormat(+x) : MISSING_DATA),
        },
        hidden: isRestricted,
        sxProps:
          !row?.overrideMTM && hasEstimate ? { ...baseOverrideMTMStyle, ...overrideMTMStyle } as SxProps : baseOverrideMTMStyle,
      },
      {
        id: "overrideMTM",
        title: "Sell Price",
        type: isRestricted ? "display" : "number",
        value: portfolioRow?.overrideMTM?.value ?? null,
        onChange: onChangeHandler,
        helperText: portfolioRow?.overrideMTM?.message,
        error: portfolioRow?.overrideMTM?.error,
        decimalScale: 2,
        prefix: "$",
        formatter: {
          func: (x: any): Nullable<string> => (!!x ? currencyFormat(+x) : MISSING_DATA),
        },
        mode: "edit",
        hidden: isRestricted,
        sxProps:
          row?.overrideMTM && hasEstimate ? { ...baseOverrideMTMStyle, ...overrideMTMStyle } as SxProps : baseOverrideMTMStyle,
      },
    ],
    [
      isCustomVintage,
      onBufferCategoryChangeHandler,
      projectCategoryDefaultProps,
      portfolioRow,
      anyRow,
      onChangeHandler,
      onBufferPercentageChangeHandler,
      onQuantityChangeHandler,
      projectRegistryIdDefaultProps,
      projectSelectionHandler,
      vintageSelectionHandler,
      vintagesDefaultProps,
      availableProjects,
      currentlyEditing,
      isRestricted,
      projectsPopulationType,
      hasEstimate,
      row,
    ],
  );

  useEffect(() => {
    const rowBufferCatId = anyRow?.projectVintage?.id
      ? (anyRow?.projectVintage as AdminProjectVintageResponse)?.project?.bufferCategory?.id
      : anyRow?.bufferCategory?.id;

    setPortfolioRow({
      id: anyRow?.id,
      registryProjectId: {
        value: {
          registryProjectId: anyRow?.project?.registryProjectId,
          id: anyRow?.project?.id,
        },
      },
      vintageInterval: {
        value: {
          name: anyRow?.projectVintage?.name ?? "", // todo : @adir why does this not work? :(
          id: anyRow?.projectVintage?.id ?? undefined,
          amountAvailable: anyRow?.amountAvailable,
          holdingAmount: anyRow?.holdingAmount,
        },
      },
      bufferPercentage: { value: anyRow?.bufferPercentage?.toString() },
      bufferCategory: {
        value: {
          name: getCategoryById(rowBufferCatId, bufferCategories) ?? "",
          id: rowBufferCatId,
        },
      },
      assetAllocationsByBookType: anyRow?.assetAllocationsByBookType,
      amountAllocated: {
        value: anyRow?.amountAllocated?.toString(),
        message: `${anyRow?.holdingAmount ? `Holding: ${integerFormat(anyRow.holdingAmount)}` : ""} ${anyRow?.amountAvailable ? `Available: ${integerFormat(anyRow.amountAvailable)}` : ""}`,
      },
      costBasis: { value: anyRow?.costBasis ? anyRow.costBasis?.toFixed(4, Decimal.ROUND_HALF_UP).toString() : "0" },
      portfolioManagerEstimate: {
        value: anyRow?.portfolioManagerEstimate ? anyRow?.portfolioManagerEstimate?.toString() : "0",
      },
      inventoryShortage: getInventoryShortage(
        anyRow?.amountAllocated,
        anyRow?.holdingAmount ?? 0,
        anyRow?.assetAllocationsByBookType ?? [],
      ),
      overrideMTM: { value: !!row?.overrideMTM ? row?.overrideMTM.toString() : null },
      markupPrice: { value: !!row?.markupPrice ? row?.markupPrice.toString() : null },
    });
  }, [startEditing, bufferCategories, anyRow, row]);

  const cancelEditHandler = (): void => {
    stopEditing();
  };

  const submitRowHandler = (): void => {
    const validatedRow = validateRow(portfolioRow);
    setPortfolioRow(validatedRow);
    if (isValidRow(validatedRow)) {
      if (!!validatedRow?.overrideMTM?.value) {
        if (isValueBetweenThresholdLimits(
          convertStringToNumber(validatedRow?.overrideMTM?.value),
          convertStringToNumber(validatedRow?.markupPrice?.value),
          SELL_PRICE_THRESHOLD_PERCENT,
        )) {
          submitEditRow(validatedRow);
        } else {
          setIsConfirmSellPriceOpen(true);
        }
      } else {
        submitEditRow(validatedRow);
      }
    }
  };

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        <BaseRow rowDef={existingRowDef} type={"existing"} isEdit={currentlyEditing} rowData={anyRow} />
        <TableCell>
          {currentlyEditing ? (
            <Stack direction="row" gap={2}>
              <IconButton sx={{ color: "rgba(154, 198, 106, 1)" }} edge="start" onClick={submitRowHandler}>
                <CheckIcon />
              </IconButton>
              <IconButton
                sx={{ marginLeft: "1px", color: COLORS.rubiconGreen }}
                edge="start"
                onClick={cancelEditHandler}
              >
                <CancelIcon />
              </IconButton>
            </Stack>
          ) : (
            <Stack direction="row" gap={2}>
              <IconButton
                disabled={isActionDisabled}
                sx={{ color: COLORS.rubiconGreen }}
                edge="start"
                onClick={() => startEditing(idx)}
              >
                <CreateIcon />
              </IconButton>
              <IconButton
                disabled={isActionDisabled}
                sx={{ color: COLORS.rubiconGreen }}
                edge="start"
                onClick={() => deleteHandler(anyRow.id)}
              >
                <DeleteIcon />
              </IconButton>
            </Stack>
          )}
        </TableCell>
      </TableRow>
      <Maybe condition={isConfirmSellPriceOpen}>
        <SellPriceConfirmation
          portfolioRow={portfolioRow}
          onSubmit={submitEditRow}
          onClose={() => setIsConfirmSellPriceOpen(false)}
        />
      </Maybe>
    </React.Fragment>
  );
}
