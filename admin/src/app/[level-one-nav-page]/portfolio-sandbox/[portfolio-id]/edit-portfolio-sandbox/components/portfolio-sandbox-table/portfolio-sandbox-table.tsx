import React, { useState, useCallback, useContext, useMemo, type JSX } from "react";
import Decimal from "decimal.js";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import {
  PermissionEnum,
  uuid,
  AdminModelPortfolioResponse,
  AdminModelPortfolioComponentResponse,
  AdminModelPortfolioComponentRequest,
  BaseModelPortfolioComponentDelete,
  BaseModelPortfolioComponentCreate, // todo : double check what this is
  AdminBufferCategoryResponse,
  ModelPortfolioStatus,
} from "@rubiconcarbon/shared-types";
import AddIcon from "@mui/icons-material/Add";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { isEmpty, pick } from "lodash";
import { Maybe, Nullable, Undefinable } from "@rubiconcarbon/frontend-shared";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import { KeyedMutator } from "swr";
import {
  PortfolioRow,
  Project,
  PortfolioComponentResponse,
  isValidRow,
  convertStringToNumber,
  ProjectPopulationType,
  AdminModelPortfolioComponentResponseFE,
} from "./portfolio-sandbox-model";
import NewRow from "./portfolio-sandbox-new-row";
import Row from "./portfolio-sandbox-edit-row";
import { PORTFOLIO_COLUMNS_EXTENDED, PORTFOLIO_COLUMNS_RESTRICTED } from "./constants";
import { useLogger } from "@providers/logging";
import { FilterData } from "../portfolio-sandbox-filter/filter-model";
import PortfolioSandboxFilter, { FilterType } from "../portfolio-sandbox-filter/portfolio-sandbox-filter";
import useNavigation from "@hooks/use-navigation";

const emptyFilter: FilterData = {
  filter: {
    emissionsImpactTypes: [],
    regions: [],
    projectTypesCategories: [],
    eligibilityAccreditations: [],
    integrityGrades: [],
  },
};

export default function PortfolioSandboxTable(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  isDisabled: boolean;
  hasEstimate: boolean;
  availableComponents: PortfolioComponentResponse[];
  availableProjects: Project[];
  bufferCategories: AdminBufferCategoryResponse[];
  refreshPortfolio: KeyedMutator<AdminModelPortfolioResponse>;
  isRestricted?: boolean;
  projectsPopulationType?: ProjectPopulationType;
  onEditAction: (status: boolean) => void;
}): JSX.Element {
  const {
    mockPortfolio,
    isDisabled,
    hasEstimate,
    availableComponents,
    availableProjects,
    bufferCategories,
    refreshPortfolio,
    onEditAction,
    isRestricted = false,
    projectsPopulationType = ProjectPopulationType.NOT_APPLICABLE,
  } = props;
  const [editIdx, setEditIdx] = useState<Undefinable<uuid>>();
  const [showNewPortfolioRow, setShowNewPortfolioRow] = useState<boolean>(false);
  const [isDeleteConfirmationOpen, setIsDeleteConfirmationOpen] = useState<boolean>(false);
  const [isUpdateComposition, setIsUpdateComposition] = useState<boolean>(false);
  const [selectedVintage, setSelectedVintage] = useState<Undefinable<uuid>>();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();
  const [searchFilter, setSearchFilter] = useState<FilterData>(emptyFilter);
  const { replacePathFromSegment } = useNavigation();

  const title: string = useMemo(() => {
    switch (projectsPopulationType) {
      case ProjectPopulationType.AVAILABLE:
        return "Available Projects";
      case ProjectPopulationType.OTHER:
        return "Other Projects";
      default:
        return "";
    }
  }, [projectsPopulationType]);

  const portfolioColumns: string[] = useMemo(
    () => (isRestricted ? PORTFOLIO_COLUMNS_RESTRICTED : PORTFOLIO_COLUMNS_EXTENDED),
    [isRestricted],
  );

  const erasePriceEstimate = useCallback(async () => {
    const payload = {
      priceEstimate: 0,
    };

    try {
      await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      refreshPortfolio();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        logger.error(`Unable to reset price estimate: ${error?.response?.data?.message}`, {});
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to reset price estimate");
    }
  }, [mockPortfolio?.id, api, refreshPortfolio, enqueueError, logger]);

  const onConfirmDeleteVintage = useCallback(
    async (ids: uuid[]): Promise<boolean> => {
      setIsDeleteConfirmationOpen(false);
      if (hasEstimate) {
        await erasePriceEstimate();
      }

      const deleteData: BaseModelPortfolioComponentDelete[] = ids.map((id) => {
        return {
          id,
        };
      });

      const payload: AdminModelPortfolioComponentRequest = {
        type: "delete",
        delete: deleteData,
      };

      try {
        await api.patch<AdminModelPortfolioComponentResponse>(
          `admin/model-portfolios/${mockPortfolio?.id}/components`,
          payload,
        );
        enqueueSuccess("Delete completed successfully");
        refreshPortfolio();
        return true;
      } catch {
        enqueueError("Unable to delete");
        logger.error(`Failed to delete vintage`, {});
        return false;
      }
    },
    [
      mockPortfolio?.id,
      erasePriceEstimate,
      hasEstimate,
      refreshPortfolio,
      setIsDeleteConfirmationOpen,
      enqueueSuccess,
      enqueueError,
      api,
      logger,
    ],
  );

  const copyPortfolioContent = useCallback(
    async (id: uuid): Promise<void> => {
      if (isEmpty(mockPortfolio?.modelPortfolioComponents)) return;

      const modelPortfolioComponentCreate: BaseModelPortfolioComponentCreate[] = [];
      (mockPortfolio?.modelPortfolioComponents as unknown as AdminModelPortfolioComponentResponseFE[])?.forEach((e) => {
        const component: any = pick(
          e,
          "amountAllocated",
          "bufferPercentage",
          "costBasis",
          "isBufferComponent",
          "vintageInterval",
          "portfolioManagerEstimate",
        );
        component.projectId = e?.project?.id;
        component.vintageId = e?.projectVintage?.id;

        modelPortfolioComponentCreate.push(component);
      });

      const payload: AdminModelPortfolioComponentRequest = {
        type: "create",
        create: modelPortfolioComponentCreate,
      };

      try {
        await api.patch<AdminModelPortfolioComponentResponse>(`admin/model-portfolios/${id}/components`, payload);
        enqueueSuccess("Portfolio content was created");
        replacePathFromSegment(2, `/${id}/edit-portfolio-sandbox`);
      } catch (error: any) {
        if (error?.response?.data?.message) {
          logger.error(`Failed to add portfolio content: ${error?.response?.data?.message}`, {});
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else {
          enqueueError(`Unable to fill portfolio content`);
          logger.error(`Failed to add portfolio content`, {});
        }
      }
    },
    [api, logger, enqueueError, enqueueSuccess, mockPortfolio, replacePathFromSegment],
  );

  const duplicatePortfolioHandler = useCallback(async (): Promise<void> => {
    const newPortfolioPayload: any = pick(mockPortfolio, "memo", "includeRiskAdjustment", "organizationId");
    newPortfolioPayload.groupingId = mockPortfolio?.groupingId;
    newPortfolioPayload.status = ModelPortfolioStatus.EXPIRED_REPLACED;
    try {
      const newPortfolioresponse = await api.post<AdminModelPortfolioComponentResponse>(
        `admin/model-portfolios`,
        newPortfolioPayload,
      );
      enqueueSuccess("Successfully created portfolio");
      copyPortfolioContent(newPortfolioresponse.data.id);
    } catch (error: any) {
      if (error?.response?.data?.message) {
        logger.error(`Failed to create new portfolio: ${error?.response?.data?.message}`, {});
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else {
        enqueueError("Unable to create portfolio");
        logger.error(`Failed to areate portfolio`, {});
      }
    } finally {
      setIsUpdateComposition(false);
    }
  }, [api, enqueueError, enqueueSuccess, copyPortfolioContent, mockPortfolio, logger]);

  const dialogDeleteButtons: ButtonDef[] = useMemo(
    () => [
      {
        label: "Yes, proceed",
        variant: "contained",
        onClickHandler: () => onConfirmDeleteVintage(selectedVintage ? [selectedVintage] : []),
        tooltip: "proceed with delete",
      },
    ],
    [onConfirmDeleteVintage, selectedVintage],
  );

  const dialogDupPortfolioButtons: ButtonDef[] = useMemo(
    () => [
      {
        label: "Yes, proceed",
        variant: "contained",
        onClickHandler: duplicatePortfolioHandler,
        tooltip: "begin a new portfolio request",
      },
    ],
    [duplicatePortfolioHandler],
  );

  const startEditingHandler = (idx: uuid): void => {
    if (hasEstimate) {
      setIsUpdateComposition(true);
    } else {
      setEditIdx(idx);
      onEditAction(true);
    }
  };

  const stopEditingHandler = (): void => {
    setEditIdx(null);
    onEditAction(false);
  };

  const deleteHandler = (id: uuid): void => {
    if (hasEstimate) {
      setIsUpdateComposition(true);
    } else {
      setSelectedVintage(id);
      setIsDeleteConfirmationOpen(true);
    }
  };

  const cancelNewRowHandler = (): void => {
    setShowNewPortfolioRow(false);
    onEditAction(false);
  };

  const addToPortfolioHandler = (): void => {
    if (hasEstimate) {
      setIsUpdateComposition(true);
    } else {
      setShowNewPortfolioRow(true);
      onEditAction(true);
    }
  };

  const createBaseRequest = useCallback(
    (row: PortfolioRow): Nullable<BaseModelPortfolioComponentCreate> => {
      if (!!mockPortfolio?.id && isValidRow(row)) {
        const bufferPercentage = row?.bufferPercentage?.value
          ? new Decimal(convertStringToNumber(row?.bufferPercentage?.value)!)
          : undefined;

        const isCustomVintage = !!!row.vintageInterval?.value?.id;
        const baseRequest: BaseModelPortfolioComponentCreate = {
          amountAllocated: convertStringToNumber(row.amountAllocated.value),
          bufferPercentage,
          costBasis: row?.costBasis?.value ? new Decimal(convertStringToNumber(row?.costBasis?.value)!) : undefined,
          isBufferComponent: false,
          registryProjectId: row.registryProjectId?.value?.registryProjectId ?? undefined,
          projectId: row.registryProjectId?.value?.id ?? undefined,
          vintageInterval: row.vintageInterval?.value?.name ?? undefined,
          vintageId: row.vintageInterval?.value?.id ?? undefined,
          portfolioManagerEstimate:
            !row.vintageInterval?.value?.id && !!row?.portfolioManagerEstimate?.value
              ? new Decimal(convertStringToNumber(row?.portfolioManagerEstimate?.value)!)
              : undefined,
          bufferCategoryId: isCustomVintage ? row?.bufferCategory?.value?.id : undefined,
          overrideMTM: !!row?.overrideMTM?.value ? new Decimal(convertStringToNumber(row?.overrideMTM?.value)) : null,
        };

        return baseRequest;
      }
      return null;
    },
    [mockPortfolio?.id],
  );

  const submitEditRow = useCallback(
    async (updatedRow: PortfolioRow) => {
      const baseRequest = createBaseRequest(updatedRow);
      if (baseRequest) {
        const payload: AdminModelPortfolioComponentRequest = {
          type: "update",
          update: [
            {
              id: updatedRow.id as uuid,
              ...baseRequest,
            },
          ],
        };

        try {
          await api.patch<AdminModelPortfolioComponentResponse>(
            `admin/model-portfolios/${mockPortfolio?.id}/components`,
            payload,
          );
          enqueueSuccess("Successfully updated vintage");
          setEditIdx(null);
          onEditAction(false);
          refreshPortfolio();
        } catch (error: any) {
          if (error?.response?.data?.message) {
            logger.error(`Failed to update vintage: ${error?.response?.data?.message}`, {});
            enqueueError(
              Array.isArray(error.response.data.message)
                ? error.response.data.message.join(", ")
                : error.response.data.message,
            );
          } else enqueueError("Unable to update vintage ");
        }
      }
    },
    [mockPortfolio?.id, api, refreshPortfolio, enqueueError, enqueueSuccess, createBaseRequest, logger, onEditAction],
  );

  const addNewPortfolioRow = useCallback(
    async (newPortfolioRow: PortfolioRow) => {
      const baseRequest = createBaseRequest(newPortfolioRow);
      if (baseRequest) {
        const payload: AdminModelPortfolioComponentRequest = {
          type: "create",
          create: [
            {
              ...baseRequest,
            },
          ],
        };

        try {
          await api.patch<AdminModelPortfolioComponentResponse>(
            `admin/model-portfolios/${mockPortfolio?.id}/components`,
            payload,
          );
          enqueueSuccess("Vintage was added successfully");
          setShowNewPortfolioRow(false);
          onEditAction(false);
          refreshPortfolio();
        } catch (error: any) {
          if (error?.response?.data?.message) {
            logger.error(`Failed to add vintage: ${error.response.data.message}`, {});
            enqueueError(
              Array.isArray(error.response.data.message)
                ? error.response.data.message.join(", ")
                : error.response.data.message,
            );
          } else enqueueError(`Unable to add vintage`);
        }
      }
    },
    [mockPortfolio?.id, api, refreshPortfolio, createBaseRequest, enqueueError, enqueueSuccess, onEditAction, logger],
  );

  const filterHandler = (selection: FilterData): void => {
    setSearchFilter(selection);
  };

  return (
    <>
      <Box>
        <Maybe condition={!!bufferCategories}>
          <Paper sx={{ width: "100%", overflow: "hidden", marginTop: "20px" }}>
            <TableContainer>
              <Table aria-label="sandbox table">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ backgroundColor: "rgba(238, 238, 238, 1)" }} colSpan={2}>
                      <Typography variant="body2" component="h4" fontWeight="500" fontSize="18px">
                        {title}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ backgroundColor: "rgba(238, 238, 238, 1)" }} colSpan={portfolioColumns.length - 2}>
                      <Box sx={{ alignContent: "right", margin: "right", textAlign: "right", paddingRight: "10px" }}>
                        <ActionButton
                          onClickHandler={addToPortfolioHandler}
                          isDisabled={isDisabled}
                          startIcon={<AddIcon />}
                          requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE}
                          style={{ textTransform: "none", marginTop: -1, marginBottom: -1, marginRight: "-15px" }}
                        >
                          Add to portfolio
                        </ActionButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    {portfolioColumns?.map((column, idx) => (
                      <TableCell key={`${column}-${idx}`}>
                        <Typography variant="body2" component="h4" fontWeight="700">
                          {column}
                        </Typography>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  <Maybe condition={showNewPortfolioRow}>
                    <TableRow sx={{ verticalAlign: "top" }}>
                      <TableCell colSpan={portfolioColumns?.length}>
                        <PortfolioSandboxFilter
                          onFilterHandler={filterHandler}
                          isDisabled={false}
                          initialSelection={searchFilter}
                          filterType={
                            projectsPopulationType === ProjectPopulationType.OTHER
                              ? FilterType.OTHER_PROJECTS
                              : FilterType.AVAILABLE_PROJECTS
                          }
                        />
                      </TableCell>
                    </TableRow>
                    <NewRow
                      availableComponents={availableComponents}
                      bufferCategories={bufferCategories}
                      submitRow={addNewPortfolioRow}
                      cancelAdd={cancelNewRowHandler}
                      isRestricted={isRestricted}
                      projectsPopulationType={projectsPopulationType}
                      searchFilter={searchFilter}
                    />
                  </Maybe>
                  {availableComponents?.map((row: any) => (
                    <Row
                      key={row.id}
                      row={row}
                      idx={row.id}
                      editIdx={editIdx}
                      startEditing={startEditingHandler}
                      stopEditing={stopEditingHandler}
                      deleteHandler={deleteHandler}
                      submitEditRow={submitEditRow}
                      availableProjects={availableProjects}
                      bufferCategories={bufferCategories}
                      isActionDisabled={isDisabled}
                      isRestricted={isRestricted}
                      projectsPopulationType={projectsPopulationType}
                      hasEstimate={hasEstimate}
                    />
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <Maybe condition={!showNewPortfolioRow}>
              <Box sx={{ height: "50px" }}>
                <Typography variant="body2" component="p" sx={{ marginTop: "30px", textAlign: "center" }}>
                  Get started by clicking “Add to portfolio” to add projects
                </Typography>
              </Box>
            </Maybe>
            <ConfirmationModal
              isOpen={isDeleteConfirmationOpen}
              onClose={() => setIsDeleteConfirmationOpen(false)}
              title={"Please confirm"}
              dialogButtons={dialogDeleteButtons}
              dialogTheme={DialogTheme.INFO}
            >
              <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
                You are about to delete the vintage.
              </Typography>
              <Maybe condition={hasEstimate}>
                <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
                  {"Updating the portfolio composition will erase the price estimate."}
                  <br /> {"Do you want to continue?"}
                </Typography>
              </Maybe>
            </ConfirmationModal>
            <ConfirmationModal
              isOpen={isUpdateComposition}
              onClose={() => setIsUpdateComposition(false)}
              title={"Please confirm"}
              dialogButtons={dialogDupPortfolioButtons}
              dialogTheme={DialogTheme.INFO}
            >
              <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
                You can’t change the composition because a price was assigned. <br />
                Do you want to begin a new portfolio request using the composition below?
              </Typography>
            </ConfirmationModal>
          </Paper>
        </Maybe>
      </Box>
    </>
  );
}
