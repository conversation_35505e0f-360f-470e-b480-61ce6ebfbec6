import React, { type JSX, useMemo } from "react";
import { Typography } from "@mui/material";
import { PortfolioRow } from "./portfolio-sandbox-model";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

export default function SellPriceConfirmation(props: {
  portfolioRow: PortfolioRow;
  onSubmit: (updatedRow: PortfolioRow) => void;
  onClose: () => void;
}): JSX.Element {
  const { portfolioRow, onSubmit, onClose } = props;

  const dialogSellPriceConfirmButtons: ButtonDef[] = useMemo(
    () => [
      {
        label: "Yes, proceed",
        variant: "contained",
        onClickHandler: (): void => {
          onSubmit(portfolioRow);
          onClose();
        },
      },
    ],
    [onClose, portfolioRow, onSubmit],
  );

  return (
    <React.Fragment>
      <ConfirmationModal
        isOpen={true}
        onClose={onClose}
        title={"Please confirm"}
        dialogButtons={dialogSellPriceConfirmButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          Sell price is above / below the system calculated price. Do you wish to continue?
        </Typography>
      </ConfirmationModal>
    </React.Fragment>
  );
}
