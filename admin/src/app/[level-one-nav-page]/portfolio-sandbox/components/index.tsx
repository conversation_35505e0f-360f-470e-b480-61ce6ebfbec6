"use client";;
import Page from "@components/layout/containers/page";
import PortfolioSandboxComponent from "./portfolios-list/sandbox";
import { AdminModelPortfolioQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const PortfolioSandbox = ({
  modelPortfoliosResponse,
}: {
  modelPortfoliosResponse: AdminModelPortfolioQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <PortfolioSandboxComponent modelPortfoliosResponse={modelPortfoliosResponse} />
    </Page>
  );
};

export default PortfolioSandbox;
