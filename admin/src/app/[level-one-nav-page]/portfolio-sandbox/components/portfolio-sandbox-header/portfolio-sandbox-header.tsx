import React, { useState, useEffect, useCallback, useContext, useMemo, type JSX } from "react";
import {
  AdminOrganizationQueryResponse,
  AdminModelPortfolioResponse,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { Box, Stack, Typography, TextField, Tooltip } from "@mui/material";
import { isEmpty } from "lodash";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AxiosContext } from "@providers/axios-provider";
import useSWR, { KeyedMutator } from "swr";
import ShareIcon from "@mui/icons-material/Share";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { Maybe, Undefinable } from "@rubiconcarbon/frontend-shared";
import ShareQuoteModal from "./share-quote-modal";
import Decimal from "decimal.js";
import { useMediaQuery } from "@mui/material";
import useNavigation from "@hooks/use-navigation";
import InfoIcon from "@mui/icons-material/Info";
import PortfolioStatus from "../portfolios-list/portfolio-status";
import { MISSING_DATA } from "@constants/constants";
import OrganizationName from "@components/ui/organization-name/organization-name";
import useAuth from "@providers/auth-provider";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import PortfolioSandboxPrice from "../../[portfolio-id]/edit-portfolio-sandbox/components/portfolio-sandbox-price/sandbox-price";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

import classes from "./portfolio-sandbox-header.module.scss";


const headerBtnStyle = {
  width: "120px",
  marginRight: "-12px",
  borderColor: "rgba(2, 136, 209, 0.12)",
  color: "rgba(0, 0, 0, 0.87)",
  align: "right",
  textTransform: "none",
  backgroundColor: "rgba(2, 136, 209, 0.12)",
  fontWeight: 500,
  "&:hover": {
    backgroundColor: "rgba(2, 136, 209, 0.12)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 1px 1px",
  },
};

const updateBtnStyle = {
  marginTop: "3px",
  borderColor: "rgba(224, 224, 224, 1)",
  color: "rgba(0, 0, 0, 0.87)",
  backgroundColor: "rgba(224, 224, 224, 1)",
  fontWeight: 500,
  textTransform: "none",
  "&:hover": {
    backgroundColor: "rgba(224, 224, 224, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 1px 1px",
  },
};
interface PortfolioNameField {
  value: string;
  message: string;
}

export default function PortfolioSandboxHeader(props: {
  mockPortfolio: AdminModelPortfolioResponse;
  isRestricted?: boolean;
  isEditInProgress: boolean;
  isAmountAvailableShortage?: boolean;
  refreshPortfolio: KeyedMutator<AdminModelPortfolioResponse>;
}): JSX.Element {
  const { mockPortfolio, refreshPortfolio, isAmountAvailableShortage, isEditInProgress, isRestricted = false } = props;
  const [portfolioName, setPortfolioName] = useState<PortfolioNameField>({ value: "", message: "" });
  const { user: loginUser } = useAuth();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [isConfirmationOpen, setIsConfirmationOpen] = useState<boolean>(false);
  const [isShareConfirmationOpen, setIsShareConfirmationOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const [isShareQuoteDialogOpen, setIsShareQuoteDialogOpen] = useState<boolean>(false);

  const isAllowSalesOrder: boolean = useMemo(
    () => !isRestricted && loginUser.hasPermission(PermissionEnum.CUSTOMER_SALES_CREATE),
    [loginUser, isRestricted],
  );

  const { data: organizationsData } = useSWR<AdminOrganizationQueryResponse>("/admin/organizations", {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  const isSmallScreen = useMediaQuery("(max-width: 1300px)");
  const { replacePathFromSegment } = useNavigation();

  useEffect(() => {
    if (mockPortfolio?.name) {
      setPortfolioName({ value: mockPortfolio.name, message: "" });
    }
  }, [mockPortfolio?.name]);

  const totalQuantity: number = useMemo(
    () =>
      !!mockPortfolio?.modelPortfolioComponents
        ? mockPortfolio?.modelPortfolioComponents?.reduce(
            (accumulator, currentValue) =>
              accumulator + (!!currentValue && currentValue?.amountAllocated ? currentValue.amountAllocated : 0),
            0,
          )
        : 0,
    [mockPortfolio?.modelPortfolioComponents],
  );

  const estPricePerTonne: Undefinable<Decimal> = useMemo(
    () =>
      !!mockPortfolio?.priceEstimate && totalQuantity > 0
        ? new Decimal(mockPortfolio?.priceEstimate).dividedBy(totalQuantity)
        : undefined,
    [mockPortfolio?.priceEstimate, totalQuantity],
  );

  const portfolioNameChangeHandler = useCallback((event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = event.target.value;
    setPortfolioName({ value, message: "" });
  }, []);

  const isNameChanged: boolean = useMemo(
    () => !(mockPortfolio?.name === portfolioName?.value),
    [mockPortfolio, portfolioName],
  );

  const portfolioNameUpdateHandler = useCallback((): void => {
    setIsConfirmationOpen(true);
  }, [setIsConfirmationOpen]);

  const onConfirmUpdate = useCallback(
    async (id: string) => {
      setIsConfirmationOpen(false);
      const payload = {
        name: portfolioName?.value,
      };

      try {
        await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${id}`, payload);
        enqueueSuccess("Successfully updated portfolio");
        refreshPortfolio();
      } catch (error: any) {
        if (error?.response?.data?.message) {
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else enqueueError("Unable to update portfolio name");
      }
    },
    [portfolioName, setIsConfirmationOpen, enqueueSuccess, enqueueError, api, refreshPortfolio],
  );

  const onConfirmShare = useCallback(async () => {
    setIsShareConfirmationOpen(false);
    const payload = {
      showCustomer: false,
    };

    try {
      await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      enqueueSuccess("Operation completed successfully");
      refreshPortfolio();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable complete operation");
    }
  }, [mockPortfolio?.id, enqueueSuccess, enqueueError, api, refreshPortfolio]);

  const onShareCustomerHandler = (): void => {
    setIsShareQuoteDialogOpen(true);
  };

  const onCreateSaleOrderHandler = (): void => {
    replacePathFromSegment(4, `/trading/transactions/new?type=sale&id=${mockPortfolio.id}`);
  };

  const closeShareCustomerHandler = (): void => {
    setIsShareQuoteDialogOpen(false);
  };

  const saveShareCustomerHandler = (): void => {
    setIsShareQuoteDialogOpen(false);
    refreshPortfolio();
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmUpdate(mockPortfolio?.id),
    },
  ];

  const shareDialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmShare(),
    },
  ];

  const updateBtnMessage: string = useMemo(() => {
    if (!isNameChanged) {
      return "Portfolio details haven't changed";
    }

    if (isEmpty(portfolioName?.value)) return "Portfolio name can't be empty";

    return "";
  }, [isNameChanged, portfolioName]);

  const onPriceSuccessHandler = useCallback(() => {
    refreshPortfolio();
    if (mockPortfolio?.showCustomer === true) {
      onConfirmShare();
    }
  }, [mockPortfolio?.showCustomer, onConfirmShare, refreshPortfolio]);

  return (
    <>
      <Stack sx={{ width: "100%" }}>
        <Maybe condition={mockPortfolio?.showCustomer === true}>
          <Box mb={4} sx={{ padding: "10px" }} className={classes.stopSharingBox}>
            <Box mt={0} sx={{ width: "100%", display: "flex", justifyContent: "space-between" }}>
              <Stack direction="row" gap={1} mt={1.5}>
                <InfoOutlinedIcon
                  sx={{
                    color: "rgba(2, 136, 209, 1)",
                  }}
                />
                <Typography
                  variant="body1"
                  component="p"
                  sx={{ fontWeight: 400, paddingTop: "0px", color: "rgba(0, 0, 0, 0.87)" }}
                >
                  The following portfolio has been shared with <b>{mockPortfolio?.organization?.name}</b> through the
                  customer portal.
                </Typography>
              </Stack>
              <Box sx={{ paddingLeft: "5px", paddingTop: "5px", marginBottom: "5px" }}>
                <ActionButton
                  onClickHandler={() => setIsShareConfirmationOpen(true)}
                  isDisabled={mockPortfolio?.showCustomer === false}
                  style={headerBtnStyle}
                  variant="text"
                >
                  Stop Sharing
                </ActionButton>
              </Box>
            </Box>
          </Box>
        </Maybe>
        <Stack direction="row" gap={1} alignItems="center">
          <Typography variant="body1" component="h4" fontWeight="500" sx={{ paddingTop: "3px" }}>
            Customer:
          </Typography>
          {!!mockPortfolio?.organization?.name ? (
            <OrganizationName
              organization={
                {
                  name: mockPortfolio?.organization?.name,
                  organization: { id: mockPortfolio?.organization?.id },
                } as any
              }
              style={{ fontSize: 14, fontWeight: 500 }}
            />
          ) : (
            MISSING_DATA
          )}
        </Stack>
        <Stack
          direction={isSmallScreen ? "column" : "row"}
          mt={2}
          gap={isSmallScreen ? 3 : 1}
          sx={{ width: "100%", justifyContent: "space-between" }}
        >
          <Stack direction="row" gap={2}>
            <Stack direction="row" gap={1} mt={1}>
              <Typography variant="body1" component="h4" fontWeight="500" sx={{ paddingTop: "3px" }}>
                Portfolio Name:
              </Typography>
              <TextField
                id="portfolioName"
                variant="standard"
                inputProps={{ maxLength: 100 }}
                value={portfolioName?.value}
                sx={{ minWidth: "300px" }}
                onChange={portfolioNameChangeHandler}
              />
            </Stack>
            <Box ml={1}>
              <ActionButton
                onClickHandler={portfolioNameUpdateHandler}
                isDisabled={!isNameChanged || !isEmpty(updateBtnMessage)}
                style={updateBtnStyle}
                variant="text"
                tooltip={updateBtnMessage}
              >
                Update
              </ActionButton>
            </Box>
          </Stack>
          <Stack direction="row" gap={2} paddingTop={0.5}>
            <Box sx={{ alignContent: "right", margin: "right", textAlign: "right" }}>
              <ActionButton
                onClickHandler={onShareCustomerHandler}
                isDisabled={mockPortfolio?.showCustomer === true || totalQuantity <= 0 || !mockPortfolio?.priceEstimate}
                startIcon={<ShareIcon />}
                requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_SHARE}
                style={{ textTransform: "none", width: "200px" }}
              >
                Share with Customer
              </ActionButton>
            </Box>
          </Stack>
        </Stack>
        <Box mt={4}>
          <PortfolioSandboxPrice
            mockPortfolio={mockPortfolio}
            totalQuantity={totalQuantity}
            onSuccessHandler={onPriceSuccessHandler}
            isRestricted={isRestricted}
            isEditInProgress={isEditInProgress}
          />
        </Box>

        <Box mt={3}>
          <Stack direction="row" gap={isSmallScreen ? 2 : 1}>
            <Typography variant="body1" component="h4" fontWeight="500" sx={{ paddingTop: "17px" }}>
              Status:
            </Typography>
            <PortfolioStatus
              portfolioId={mockPortfolio?.id}
              currentStatus={mockPortfolio?.status}
              onSuccess={() => refreshPortfolio()}
            />
            <Maybe condition={isAllowSalesOrder}>
              <Stack
                direction="row"
                sx={{ alignContent: "right", margin: "right", textAlign: "right", paddingTop: "10px" }}
              >
                <ActionButton
                  onClickHandler={onCreateSaleOrderHandler}
                  requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_ADVANCED_VIEW}
                  isDisabled={isAmountAvailableShortage || !mockPortfolio?.organization?.id}
                  style={{ textTransform: "none", width: "170px" }}
                >
                  Create Sales Order
                </ActionButton>
                <Maybe condition={!!isAmountAvailableShortage}>
                  <Tooltip title={"Order cannot be fulfilled due to insufficient credits."}>
                    <InfoIcon />
                  </Tooltip>
                </Maybe>
              </Stack>
            </Maybe>
          </Stack>
        </Box>
      </Stack>
      <ConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500, width: 450, marginBottom: "5px" }}>
          You are about to change portfolio name to <b>{portfolioName.value}</b>.
        </Typography>
      </ConfirmationModal>
      <ShareQuoteModal
        mockPortfolio={mockPortfolio}
        organizationsData={organizationsData?.data}
        totalQuantity={totalQuantity}
        originalPricePerTonne={estPricePerTonne}
        isOpen={isShareQuoteDialogOpen}
        onSave={saveShareCustomerHandler}
        onClose={closeShareCustomerHandler}
      />
      <ConfirmationModal
        isOpen={isShareConfirmationOpen}
        onClose={() => setIsShareConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={shareDialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          You are about to stop sharing the portfolio with <b>{mockPortfolio?.organization?.name}</b>.
        </Typography>
      </ConfirmationModal>
    </>
  );
}
