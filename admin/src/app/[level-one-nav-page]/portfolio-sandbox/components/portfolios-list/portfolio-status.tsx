import React, { useCallback, useContext, useEffect, useMemo, useState, type JSX } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { FormControl, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";
import { NO_STATUS, PortfolioStatusMapping } from "@mappers/portfolio-status-mapper";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AdminModelPortfolioResponse, ModelPortfolioStatus, PermissionEnum } from "@rubiconcarbon/shared-types";
import { Undefinable } from "@rubiconcarbon/frontend-shared";
import useAuth from "@providers/auth-provider";

interface StatusOption {
  optionName: string;
  isDisabled: boolean;
}

const SUB_STATUS_COUNT = 3;
const getEnumKeyByValue = (enumerated: any, value: string): string => {
  return Object.keys(enumerated)[Object.values(enumerated).indexOf(value as typeof enumerated)];
};

const getStatusKeyByDisplayValue = (statusDisplayValue: string): Undefinable<string> => {
  return Object.keys(PortfolioStatusMapping)?.find((key) => PortfolioStatusMapping[key as keyof typeof PortfolioStatusMapping] === statusDisplayValue);
};

export default function PortfolioStatus(props: {
  portfolioId: string;
  currentStatus?: ModelPortfolioStatus;
  onSuccess: () => void;
}): JSX.Element {
  const { portfolioId, currentStatus, onSuccess } = props;

  const [activeStatus, setActiveStatus] = useState<number>(0);
  const [isStatusConfirmationOpen, setIsStatusConfirmationOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const { user } = useAuth();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const statusOptions: StatusOption[] = useMemo(() => {
    const statusArr: StatusOption[] = Object.values(ModelPortfolioStatus).map((v) => {
      return {
        optionName: PortfolioStatusMapping[v],
        isDisabled: false,
      };
    });
    statusArr.find((s) => s.optionName === PortfolioStatusMapping[ModelPortfolioStatus.ORDER_CREATED]).isDisabled =
      true;
    statusArr.unshift({ optionName: NO_STATUS, isDisabled: false });
    return statusArr;
  }, []);

  const hasWritePermission = useMemo(() => user?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_UPDATE), [user]);

  const resetStatus = useCallback((): void => {
    if (currentStatus) {
      const index = Object.keys(ModelPortfolioStatus).indexOf(getEnumKeyByValue(ModelPortfolioStatus, currentStatus));
      setActiveStatus(index + 1);
    } else {
      setActiveStatus(0);
    }
  }, [currentStatus]);

  useEffect(() => {
    resetStatus();
  }, [currentStatus, resetStatus]);

  const statusHandler = (index: number): void => {
    setActiveStatus(index);
    setIsStatusConfirmationOpen(true);
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmUpdate(portfolioId),
    },
  ];

  const onConfirmUpdate = useCallback(
    async (id: string) => {
      const newStatus = getStatusKeyByDisplayValue(statusOptions[activeStatus].optionName);
      const payload = {
        status: newStatus !== NO_STATUS ? newStatus : null,
      };

      try {
        await api.patch<AdminModelPortfolioResponse>(`admin/model-portfolios/${id}`, payload);
        enqueueSuccess("Successfully updated portfolio");
        setIsStatusConfirmationOpen(false);
        onSuccess();
      } catch (error: any) {
        if (error?.response?.data?.message) {
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else enqueueError("Unable to update portfolio");
      }
    },
    [enqueueSuccess, enqueueError, api, onSuccess, activeStatus, statusOptions],
  );

  const onCancelStatusUpdate = (): void => {
    setIsStatusConfirmationOpen(false);
    resetStatus();
  };

  const getStatusLabel = useCallback(
    (index: number, label: string): string => {
      switch (label) {
        case PortfolioStatusMapping[ModelPortfolioStatus.EXPIRED_REPLACED]:
          return `${statusOptions?.length - SUB_STATUS_COUNT}c - ${label}`;
        case PortfolioStatusMapping[ModelPortfolioStatus.LOST]:
          return `${statusOptions?.length - SUB_STATUS_COUNT}b - ${label}`;
        case PortfolioStatusMapping[ModelPortfolioStatus.ORDER_CREATED]:
          return `${statusOptions?.length - SUB_STATUS_COUNT}a - ${label}`;
        default:
          return `${index} - ${label}`;
      }
    },
    [statusOptions?.length],
  );

  return (
    <>
      <FormControl sx={{ m: 1, width: 240, backgroundColor: "white" }} size="small">
        <Select
          value={activeStatus?.toString() ?? "0"}
          onChange={(event: SelectChangeEvent): void => {
            statusHandler(Number(event.target.value));
          }}
          disabled={!hasWritePermission}
        >
          {statusOptions.map((status, index) => (
            <MenuItem key={index} value={index} sx={{ height: "35px" }} disabled={status.isDisabled}>
              {getStatusLabel(index, status.optionName)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <ConfirmationModal
        isOpen={isStatusConfirmationOpen}
        onClose={onCancelStatusUpdate}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500, width: 450, marginBottom: "5px" }}>
          You are about to change portfolio status to{" "}
          <b>{PortfolioStatusMapping[getStatusKeyByDisplayValue(statusOptions[activeStatus]?.optionName) as keyof typeof PortfolioStatusMapping] ?? ""}</b>.
        </Typography>
      </ConfirmationModal>
    </>
  );
}
