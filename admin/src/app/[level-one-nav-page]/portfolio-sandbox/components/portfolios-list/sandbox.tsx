import React, { useState, useEffect, useCallback, useMemo, type JSX } from "react";
import { AdminModelPortfolioQueryResponse, ModelPortfolioStatus, PermissionEnum, uuid } from "@rubiconcarbon/shared-types";
import { stringComparator } from "@utils/comparators/comparator";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { Stack } from "@mui/material";
import { SandboxPortfolio, mapSandboxPortfolio } from "@models/sandbox-portfolio";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import PortfolioSandboxActions from "./sandbox-actions";
import NewPortfolioModal from "../portfolio-sandbox-header/new-portfolio-modal";
import useNavigation from "@hooks/use-navigation";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import PortfolioStatus from "./portfolio-status";
import OrganizationName from "@components/ui/organization-name/organization-name";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { NO_STATUS, PortfolioStatusMapping } from "@mappers/portfolio-status-mapper";
import usePerformantEffect from "@hooks/use-performant-effect";

export default function PortfolioSandbox({
  modelPortfoliosResponse: serverModelPortfoliosResponse,
  projectId,
}: {
  modelPortfoliosResponse?: AdminModelPortfolioQueryResponse;
  projectId?: uuid;
}): JSX.Element {
  const { pushToPath } = useNavigation();
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [portfolios, setPortfolios] = useState<SandboxPortfolio[]>();
  const [isNewPortfolioDialogOpen, setIsNewPortfolioDialogOpen] = useState<boolean>(false);

  const { data: modelPortfoliosResponse, trigger: refreshModelPortfolios, isMutating: loading } =
    useTriggerRequest<AdminModelPortfolioQueryResponse>({
      url: `/admin/model-portfolios`,
      queryParams: {
        limit: SERVER_PAGINATION_LIMIT,
        projectId,
      },
      optimisticData: serverModelPortfoliosResponse,
      swrOptions: {
        onError: (error: any): void => {
          enqueueError("Unable to load model portfolios");
          logger.error(`Unable to load model portfolios: ${error?.message}`, {});
        },
      },
    });

  usePerformantEffect(() => {
    if (!!projectId && !modelPortfoliosResponse && !loading) setTimeout(async () => await refreshModelPortfolios()); 
  }, [projectId, modelPortfoliosResponse, loading]);

  const modelPortfolios = useMemo(() => modelPortfoliosResponse?.data, [modelPortfoliosResponse]);

  const getActions = useCallback(
    (inputMap: Map<string, string | boolean>): JSX.Element => {
      return (
        <PortfolioSandboxActions
          id={inputMap.get("id")?.toString() ?? ""}
          name={inputMap.get("name")?.toString() ?? ""}
          allowExportPDF={inputMap.get("allowExportPDF") === true ? true : false}
          refreshPortfolios={refreshModelPortfolios}
        />
      );
    },
    [refreshModelPortfolios],
  );

  const getCustomer = useCallback((inputMap: Map<string, string>): JSX.Element => {
    return (
      <OrganizationName
        organization={{ name: inputMap.get("organizationName"), id: inputMap.get("organizationId") } as any}
        style={{ fontSize: 14 }}
      />
    );
  }, []);

  const getStatus = useCallback(
    (inputMap: Map<string, string>): JSX.Element => {
      const id = inputMap.get("id")?.toString() ?? "";
      const status = inputMap.get("status");
      return (
        <PortfolioStatus
          portfolioId={id}
          currentStatus={status as ModelPortfolioStatus}
          onSuccess={async () => {
            await refreshModelPortfolios();
          }}
        />
      );
    },
    [refreshModelPortfolios],
  );

  const columnsDef: ColDef[] = [
    {
      columnName: "uiKey",
      displayName: "Portfolio Id",
      comparator: stringComparator as any,
      style: { width: "200px" },
    },
    {
      columnName: "name",
      displayName: "Portfolio Name",
      comparator: stringComparator as any,
    },
    {
      columnName: "organizationName",
      displayName: "Customer",
      comparator: stringComparator as any,
      formatter: {
        func: getCustomer as any,
        inputFields: ["organizationName", "organizationId"],
      },
    },
    {
      columnName: "status",
      displayName: "Status",
      sortable: false,
      style: { width: "150px" },
      formatter: {
        func: getStatus as any,
        inputFields: ["status", "id"],
        overrideMissingDataDisplay: true,
      },
    },
    {
      columnName: "lastUpdated",
      displayName: "Last Updated",
      formatter: { func: dateFormatterEST as any },
      exportFormatter: { func: dateFormatterEST as any },
    },
    {
      columnName: "createdBy",
      displayName: "Created By",
      comparator: stringComparator as any,
    },
    {
      columnName: "updatedBy",
      displayName: "Updated By",
      comparator: stringComparator as any,
    },
    {
      columnName: "id",
      displayName: "Actions",
      formatter: {
        func: getActions as any,
        inputFields: ["id", "name", "allowExportPDF"],
      },
      exportable: false,
      sortable: false,
    },
  ];

  useEffect(() => {
    if (modelPortfolios) {
      modelPortfolios.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
      setPortfolios(mapSandboxPortfolio(modelPortfolios));
    }
  }, [modelPortfolios]);

  const isStatusMatch = useCallback((input: string, status: ModelPortfolioStatus): boolean => {
    if (
      (status === undefined && NO_STATUS.toUpperCase()?.includes(input)) ||
      PortfolioStatusMapping[status]?.toUpperCase()?.includes(input)
    )
      return true;

    return false;
  }, []);

  const getFilteredData = useCallback(
    (input: string): void => {
      const searchString = input.toUpperCase();
      const filteredData = modelPortfoliosResponse?.data.filter(
        (model) =>
          model?.uiKey?.toUpperCase().includes(searchString) ||
          model.name.toUpperCase().includes(searchString) ||
          model.createdBy.name.toUpperCase().includes(searchString) ||
          isStatusMatch(searchString, model?.status) ||
          model.updatedBy.name.toUpperCase().includes(searchString),
      );
      setPortfolios(mapSandboxPortfolio(filteredData));
    },
    [modelPortfoliosResponse?.data, isStatusMatch],
  );

  const newPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(true);
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <Stack direction="row" gap="15px">
        <ActionButton
          onClickHandler={newPortfolioHandler}
          requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_CREATE}
          style={{ width: "190px" }}
        >
          Create Portfolio
        </ActionButton>
      </Stack>
    );
  };

  const saveNewPortfolioHandler = (id: uuid): void => {
    setIsNewPortfolioDialogOpen(false);
    if (id) {
      pushToPath(`${id}/edit-portfolio-sandbox`);
    }
  };

  const closeNewPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(false);
  };

  return (
    <>
      <TableBox>
        {portfolios && (
          <EnhancedTable
            name={"portfolios_info"}
            columnsDef={columnsDef}
            exportable={true}
            data={portfolios}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "lastUpdated", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <NewPortfolioModal
        isOpen={isNewPortfolioDialogOpen}
        onSave={saveNewPortfolioHandler}
        onClose={closeNewPortfolioHandler}
      />
    </>
  );
}
