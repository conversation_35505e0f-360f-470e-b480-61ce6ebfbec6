import { AuthorizeServer } from "@app/authorize-server";
import { AdminModelPortfolioQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import PortfolioSandbox from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Portfolio Sandbox Page
 *
 * This is a server component that renders the Portfolio Sandbox page
 */
export default async function PortfolioSandboxPage(): Promise<JSX.Element> {
  const modelPortfoliosResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminModelPortfolioQueryResponse>(
      `admin/model-portfolios?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(modelPortfoliosResponse)) return modelPortfoliosResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.MODEL_PORTFOLIOS_READ]}>
      <PortfolioSandbox modelPortfoliosResponse={modelPortfoliosResponse as AdminModelPortfolioQueryResponse} />
    </AuthorizeServer>
  );
}
