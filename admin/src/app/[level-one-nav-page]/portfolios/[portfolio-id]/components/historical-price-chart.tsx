import React, { type JSX } from "react";
import { Box } from "@mui/material";
import ReactEcharts from "echarts-for-react";

export interface HistPriceChartData {
  pricesWithoutBuffer: (string | number)[][];
  pricesWithBuffer: (string | number)[][];
}

export interface HistPriceChartDataProps {
  histPriceChartData: HistPriceChartData;
}

export default function HistoricalPriceChart(props: HistPriceChartDataProps): JSX.Element {
  const { histPriceChartData } = props;

  const option = {
    grid: {
      left: "1%", // Push the chart content to the very left edge
      right: "1%", // Push the chart content to the very right edge
      bottom: "15%", // Push the chart content to the very bottom edge
      top: "5%", // Push the chart content to the very top edge
      containLabel: true,
    },
    // Tooltip Configuration
    tooltip: {
      trigger: "axis",
      // Custom formatter to show only the date
      formatter: function (params: any): string {
        // params[0].axisValueLabel provides the formatted date from the x-axis
        let result = params[0]?.axisValueLabel.slice(0, -10) + "<br/>";
        // add name and value

        result = params[0]?.marker + " " + params[0]?.seriesName + ": " + params[0]?.value[1];
        if (params?.length === 2) {
          if (params[1]?.seriesName !== params[0]?.seriesName) {
            result += "<br/>" + params[1]?.marker + " " + params[1]?.seriesName + ": " + params[1]?.value[1];
          }
        }

        return result;
      },
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#283b56",
        },
      },
    },
    // Legend for the series
    legend: {
      data: ["Price", "Price with Buffer"],
      bottom: 10,
    },
    // X-Axis Configuration (Time-based)
    xAxis: {
      type: "time",
      boundaryGap: false,
      /* 
      splitLine: {
        // This adds vertical grid lines
        show: true,
        lineStyle: {
          color: "#e0e0e0",
          type: "dashed",
        },
      },*/
    },
    // Y-Axis Configuration (Single axis)
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}",
      },
      scale: true,
      //min: 0, // Set the y-axis to start at 0
    },
    // Series Data
    series: [
      {
        name: "Price",
        type: "line",
        connectNulls: true,
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: "#008000",
        },
        emphasis: {
          focus: "series",
        },
        data: histPriceChartData.pricesWithoutBuffer,
      },
      {
        name: "Price with Buffer",
        type: "line",
        connectNulls: true,
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: "#0000FF",
        },
        emphasis: {
          focus: "series",
        },
        data: histPriceChartData.pricesWithBuffer,
      },
    ],
  };

  return (
    <Box>
      <ReactEcharts style={{ height: "400px", width: "100%" }} option={option} />
    </Box>
  );
}
