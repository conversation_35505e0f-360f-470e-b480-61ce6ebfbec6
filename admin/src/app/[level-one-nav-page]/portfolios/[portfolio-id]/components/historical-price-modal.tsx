import React, { useMemo, useCallback, useState, type JSX } from "react";
import { Box, Typo<PERSON>, Stack, ToggleButtonGroup, ToggleButton, Modal, IconButton } from "@mui/material";
import { uuid } from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";
import { MISSING_DATA } from "@constants/constants";
import currencyFormat from "@utils/formatters/currency-format";
import { Maybe, useRequest } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import CloseIcon from "@mui/icons-material/Close";
import { BaseDialogProps } from "@models/dialogs";
import HistoricalPriceChart, { HistPriceChartData } from "./historical-price-chart";
import HistPriceExport from "./historical-price-export";

export enum BookPriceType {
  BUYBACK_PRICE = "buyback_price",
  PURCHASE_PRICE_WITHOUT_BUFFER = "purchase_price_without_buffer",
  PURCHASE_PRICE_WITH_BUFFER = "purchase_price_with_buffer",
}

export interface PriceData {
  book_id: uuid;
  timestamp: string; // Date;
  new_price: Decimal;
  type: BookPriceType;
}

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "90%", // Default width for smaller screens, also ensures it doesn't hit screen edges
  maxWidth: "false", // Set to false to remove the maximum width constraint, allowing it to be as wide as 90% of the viewport
  bgcolor: "background.paper",
  border: "2px solid #000",
  boxShadow: 24,
  p: 4, // Padding
  borderRadius: 2, // Rounded corners
};

const toggleButtonStyle = {
  "&.MuiButtonBase-root": {
    color: "#094436",
    backgroundColor: "white",
  },
  "&.MuiButtonBase-root: hover": {
    backgroundColor: "#D1E1CB",
  },
  "&.Mui-selected": {
    color: "white",
    backgroundColor: "#094436",
  },
  "&.Mui-selected: hover": {
    backgroundColor: "rgb(0 78 60)",
  },
};

enum FilterEnum {
  "1M" = "1 Month",
  "3M" = "3 Months",
  "1Y" = "1 Year",
}

interface HistoricalPriceModalProps extends BaseDialogProps {
  bookId: uuid;
  bookName: string;
  price?: Decimal;
}

export default function HistoricalPriceModal({
  isOpen,
  onClose,
  price,
  bookId,
  bookName,
}: HistoricalPriceModalProps): JSX.Element | null {
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const [filter, setFilter] = useState<string>("1 Month");
  const [chartData, setChartData] = useState<HistPriceChartData>(null);

  const { data: historicalPrices } = useRequest<PriceData[]>({
    url: `/reporting/historical-book-prices/${bookId}`,
    queryParams: {
      date_filter: filter,
    },
    swrOptions: {
      onSuccess: (data: PriceData[]) => {
        buildChartData(data);
      },
      onError: (error: any) => {
        enqueueError("Unable to fetch historical prices.");
        logger.error(`Unable to fetch historical prices. Error: ${error?.message}`, {});
      },
    },
  });

  const hasData = useMemo(
    () => chartData?.pricesWithBuffer?.length > 0 || chartData?.pricesWithoutBuffer?.length > 0,
    [chartData],
  );

  const buildChartData = (data: PriceData[]): void => {
    const pricesWithBuffer: (string | number)[][] = [];
    const pricesWithoutBuffer: (string | number)[][] = [];

    data.forEach((price) => {
      if (price.type === BookPriceType.PURCHASE_PRICE_WITHOUT_BUFFER) {
        pricesWithoutBuffer.push([new Date(price.timestamp).toISOString().slice(0, 10), +price.new_price]);
      } else {
        pricesWithBuffer.push([new Date(price.timestamp).toISOString().slice(0, 10), +price.new_price]);
      }
    });
    setChartData({ pricesWithoutBuffer, pricesWithBuffer });
  };

  const handleChange = useCallback((event: React.MouseEvent<HTMLElement>, newFilter: string): void => {
    event.preventDefault();
    if (!!newFilter) {
      setFilter((FilterEnum as any)[newFilter]);
    }
  }, []);

  const onCloseHandler = useCallback((): void => {
    onClose();
  }, [onClose]);

  const dialogHead = useMemo(
    () => (
      <Box mt={-2}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          Customer Portal Historical Price
        </Typography>
      </Box>
    ),
    [],
  );

  const toggleOptions = useMemo(() => ["1M", "3M", "1Y"], []);

  return (
    <Modal open={isOpen} onClose={onCloseHandler} aria-labelledby="modal-title" aria-describedby="modal-description">
      {/* Box component to contain the modal's content and apply styling */}
      <Box sx={style}>
        {/* Close button positioned at the top right of the modal */}
        <IconButton
          aria-label="close"
          onClick={onCloseHandler}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>

        {/* Modal Title */}
        {dialogHead}

        {/* Modal Description/Content */}
        <Stack sx={{ minHeight: 470 }}>
          <Box>
            <Typography
              variant="body2"
              component="p"
              sx={{
                color: "rgba(0, 0, 0, 1)",
                backgroundColor: "white",
                fontWeight: 500,
                fontSize: "24px",
                width: "501px",
                border: "none",
              }}
            >
              {!!price ? `${currencyFormat(+price)} per tonne` : MISSING_DATA}
            </Typography>
          </Box>
          <Maybe condition={hasData}>
            <Box sx={{ textAlign: "right" }}>
              <Stack direction="row" gap={1} sx={{ float: "right" }}>
                <ToggleButtonGroup
                  color="primary"
                  value={Object.keys(FilterEnum).find((key) => (FilterEnum as any)[key] === filter)}
                  exclusive
                  onChange={handleChange}
                  aria-label="Platform"
                  sx={{ height: "35px" }}
                >
                  {toggleOptions.map((option) => (
                    <ToggleButton key={option} value={option} sx={toggleButtonStyle}>
                      {option}
                    </ToggleButton>
                  ))}
                </ToggleButtonGroup>
                <Box>
                  <HistPriceExport historicalPrices={historicalPrices} bookName={bookName} />
                </Box>
              </Stack>
            </Box>
          </Maybe>

          <Maybe condition={hasData}>
            <HistoricalPriceChart histPriceChartData={chartData} />
          </Maybe>
          <Maybe condition={historicalPrices?.length === 0}>
            <Box sx={{ textAlign: "center", paddingTop: 20 }}>NO DATA</Box>
          </Maybe>
        </Stack>
      </Box>
    </Modal>
  );
}
