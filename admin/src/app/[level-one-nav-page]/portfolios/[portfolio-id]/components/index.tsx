"use client";;
import Page from "@components/layout/containers/page";
import PortfolioDetails from "./portfolio-details";
import { AdminBookResponse, AdminBookQueryResponse, AdminTransactionQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const PortfolioDetail = ({
  portfolio,
  booksResponse,
  completedTransactionsResponse,
}: {
  portfolio: AdminBookResponse;
  booksResponse: AdminBookQueryResponse;
  completedTransactionsResponse: AdminTransactionQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <PortfolioDetails
        portfolio={portfolio}
        booksResponse={booksResponse}
        completedTransactionsResponse={completedTransactionsResponse}
      />
    </Page>
  );
};

export default PortfolioDetail;
