import { uuid } from "@rubiconcarbon/shared-types";
import { useState, type JSX } from "react";
import Decimal from "decimal.js";
import BarChartIcon from "@mui/icons-material/BarChart";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import CustomButton from "@components/ui/custom-button/custom-button";
import COLORS from "@components/ui/theme/colors";
import HistoricalPriceModal from "./historical-price-modal";

const btnStyle = {
  borderRadius: 2,
  backgroundColor: COLORS.white,
  fontSize: "14px",
  fontWeight: 600,
  color: "rgba(30, 70, 57, 1)",
  marginTop: "-3px",
  "&:hover": {
    backgroundColor: COLORS.white,
  },
  minWidth: "90px",
  padding: "5px",
  border: "solid",
  borderWidth: "1px",
  borderColor: "rgba(0, 0, 0, 0.23)",
  boxShadow: "none",
  textTransform: "capitalize",
};

interface PortfolioHistPriceProps {
  bookId: uuid;
  bookName: string;
  price?: Decimal;
}

export default function PortfolioHistPrice(props: PortfolioHistPriceProps): JSX.Element {
  const { bookId, price, bookName } = props;

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  return (
    <>
      <CustomButton onClickHandler={() => setIsModalOpen(true)} style={{ ...btnStyle, minWidth: "40px" }}>
        <BarChartIcon />
      </CustomButton>
      <Maybe condition={isModalOpen}>
        <HistoricalPriceModal
          bookId={bookId}
          bookName={bookName}
          price={price}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      </Maybe>
    </>
  );
}
