import { Box } from "@mui/material";
import React, { useState, useEffect, useMemo, type JSX } from "react";
import { AdminBookResponse, BookType } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import COLORS from "@components/ui/theme/colors";
import StackedBar, { ProgressBarChartData } from "@components/ui/charts/stacked-bar";

interface PortfolioMgtChartsProps {
  portfolio: AdminBookResponse;
}
export default function PortfolioMgtCharts(props: PortfolioMgtChartsProps): JSX.Element {
  const { portfolio } = props;
  const [chartsCollection, setChartsCollection] = useState<ProgressBarChartData[]>([]);

  const bookGeneratedRcts = portfolio?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_DEFAULT);
  const customerHeldRcts = portfolio?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_CUSTOMER);

  const totalBarValue = bookGeneratedRcts?.totalAmountAllocated ?? 0;

  // @kofi why does this need to be useMemo?
  const pendingPurchasePercent = useMemo(
    () => ((bookGeneratedRcts?.totalAmountPendingPurchase ?? 0) * 100) / totalBarValue,
    [bookGeneratedRcts?.totalAmountPendingPurchase, totalBarValue],
  );

  useEffect(() => {
    if (portfolio) {
      setChartsCollection([
        {
          name: "available to sell",
          value: bookGeneratedRcts?.totalAmountAvailable ?? 0,
          title: "Available to Sell",
          emptyValuesPlaceholder: false,
          style: {
            barColor: COLORS.chartOrange,
            title: {
              fontSize: "14px",
              padding: [0, 0, 70, 0],
              align: "left",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, 50, 0],
              align: "left",
            },
          },
        },
        {
          name: "pending purchase",
          value: bookGeneratedRcts?.totalAmountPendingPurchase ?? 0,
          title: "Pending Purchase",
          emptyValuesPlaceholder: false,
          labelPosition: "right",
          style: {
            barColor: COLORS.chartPaleGreen,
            title: {
              fontSize: "14px",
              padding: [0, 0, -70, 0],
              align: "right",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, -14, 0],
              align: "right",
            },
          },
        },
        {
          name: "customer holding",
          value: customerHeldRcts?.totalAmountAllocated ?? 0,
          title: "Customer Holding",
          emptyValuesPlaceholder: false,
          labelPosition: "right",
          style: {
            barColor: COLORS.chartsPurple,
            title: {
              fontSize: "14px",
              padding: [0, 0, 55, 0],
              align: "right",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, 40, 0],
              align: "right",
            },
          },
        },
      ]);
    }
  }, [portfolio, bookGeneratedRcts, customerHeldRcts, pendingPurchasePercent]);

  return (
    <Box sx={{ minWidth: "200px" }}>
      <Maybe condition={!!chartsCollection}>
        <StackedBar chartsArray={chartsCollection} />
      </Maybe>
    </Box>
  );
}
