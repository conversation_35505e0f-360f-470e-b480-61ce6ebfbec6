import { Box } from "@mui/material";
import React, { useState, useEffect, type JSX } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import COLORS from "@components/ui/theme/colors";
import Bar<PERSON>hart, { BarChartData } from "@components/ui/charts/bar-chart";
import { TransactionType, AdminTransactionResponse } from "@rubiconcarbon/shared-types";

const boxStyle = {
  backgroundColor: COLORS.white,
  borderRadius: "5px",
  boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)",
};

interface TransactionsChartData extends AdminTransactionResponse {
  transactionsType: TransactionType.PURCHASE | TransactionType.RETIREMENT;
}

interface TransactionsProps {
  basketTransactions: AdminTransactionResponse[];
}

export default function TransactionsCharts(props: TransactionsProps): JSX.Element {
  const { basketTransactions } = props;
  const [chartData, setChartData] = useState<BarChartData>();

  const buildSourceData = (transactions: TransactionsChartData[]): BarChartData => {
    const source: (string | number)[][] = [["date", "Purchases", "Retirements"]];

    if (transactions !== null && transactions.length > 0) {
      let monPurchases = 0;
      let monRetirements = 0;

      const sortedTransactions = transactions.sort((a, b) => (b.createdAt > a.createdAt ? -1 : 1));
      let curMonth = new Date(sortedTransactions[0].createdAt).getMonth() + 1;
      sortedTransactions.forEach((e, index) => {
        const transDate = new Date(e.createdAt);
        const transMonth = transDate.getMonth() + 1;
        const year = transDate.getFullYear();

        if (transMonth !== curMonth) {
          source.push([`${curMonth}-${year}`, monPurchases, monRetirements]);

          curMonth = transMonth;
          monPurchases = 0;
          monRetirements = 0;
        }

        if (e.transactionsType === TransactionType.PURCHASE) monPurchases += e?.totalQuantity ?? 0;
        else if (e.transactionsType === TransactionType.RETIREMENT) monRetirements += e?.totalQuantity ?? 0;

        if (index === transactions.length - 1) {
          source.push([`${transMonth}-${year}`, monPurchases, monRetirements]);
        }
      });
    }

    return {
      title: "Transactions",
      source: source,
      style: { colors: [COLORS.chartPaleGreen, COLORS.chartsPaleBlue] },
      selectedLegend: ["Purchases", "Retirements"],
    };
  };

  useEffect(() => {
    const transactions: TransactionsChartData[] = [];

    if (!!basketTransactions && basketTransactions?.length > 0) {
      basketTransactions?.forEach((e) => {
        transactions.push({ ...e, transactionsType: e.type as any });
      });
    }

    setChartData(buildSourceData(transactions));
  }, [basketTransactions]);

  return (
    <>
      <Maybe condition={!!chartData}>
        {() => (
          <Box mt={4} sx={boxStyle}>
            <BarChart data={chartData!} />
          </Box>
        )}
      </Maybe>
    </>
  );
}
