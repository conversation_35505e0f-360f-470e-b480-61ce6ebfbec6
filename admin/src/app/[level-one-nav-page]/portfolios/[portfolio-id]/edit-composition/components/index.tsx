"use client";;
import Page from "@components/layout/containers/page";
import BookComposition from "./portfolio-composition";
import { AdminBookResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const PortfolioComposition = ({ portfolio }: { portfolio: AdminBookResponse }): JSX.Element => {
  return (
    <Page>
      <BookComposition portfolio={portfolio} />
    </Page>
  );
};

export default PortfolioComposition;
