import React, { useEffect, useState, type JS<PERSON> } from "react";
import { Box, Grid, Typography } from "@mui/material";
import ProjectVintage from "@models/project-vintage";
import COLORS from "@components/ui/theme/colors";
import { MISSING_DATA } from "@constants/constants";
import ChangeDirectionIcon from "@components/ui/change-direction-icon/change-direction-icon";
import integerFormat from "@utils/formatters/integer-format";
import { isNil } from "lodash";
import { Nullable } from "@rubiconcarbon/frontend-shared";
import SingleValueChart, { SingleValueChartData } from "./single-value-chart";

const MISSING_BUFFER_MSG = "One or more buffer values are missing";
const NO_VINTAGES_MSG = "Vintages collection is empty";

const getBufferTooltipMsg = (collection: ProjectVintage[], buffer: Nullable<number>): string => {
  if (collection && collection.length === 0) return NO_VINTAGES_MSG;
  return buffer === null ? MISSING_BUFFER_MSG : "";
};

const getAverageBuffer = (data: ProjectVintage[]): Nullable<number> => {
  if (data && data.length > 0 && !data.some((e) => e["buffer"] === null)) {
    return Math.round(data.reduce((acc, e) => acc + (e["buffer"] ? (e["buffer"] * e["quantity"]) / 100 : 0), 0));
  }

  return null;
};

const sectionStyle = {
  fontWeight: 500,
  color: COLORS.pureBlack,
  fontSize: "20px",
  lineHeight: "60px",
  paddingLeft: "30px",
};

interface PortfolioBufferChartsProps {
  currentAllocation: ProjectVintage[];
  projectedAllocation: ProjectVintage[];
}

export default function PortfolioBufferCharts(props: PortfolioBufferChartsProps): JSX.Element {
  const { currentAllocation, projectedAllocation } = props;
  const [currentBuffer, setCurrentBuffer] = useState<Nullable<number>>(null);
  const [newBuffer, setNewBuffer] = useState<Nullable<number>>(null);

  useEffect(() => {
    if (currentAllocation) {
      setCurrentBuffer(getAverageBuffer(currentAllocation));
    }
  }, [currentAllocation]);

  useEffect(() => {
    if (projectedAllocation) {
      setNewBuffer(getAverageBuffer(projectedAllocation));
    }
  }, [projectedAllocation]);

  const currectBufferChartData: SingleValueChartData = {
    mainLabel: "Current",
    value: !isNil(currentBuffer) ? `${integerFormat(currentBuffer)}` : MISSING_DATA,
    backColor: COLORS.paleBlue,
    tooltip: getBufferTooltipMsg(currentAllocation, currentBuffer),
  };

  const newBufferChartData: SingleValueChartData = {
    mainLabel: "New",
    value: !isNil(newBuffer) ? `${integerFormat(newBuffer)}` : MISSING_DATA,
    backColor: COLORS.paleGreen,
    icon:
      currentBuffer !== null && newBuffer !== null ? (
        <ChangeDirectionIcon currentValue={currentBuffer} newValue={newBuffer} />
      ) : (
        <></>
      ),
    tooltip: getBufferTooltipMsg(projectedAllocation, newBuffer),
  };

  return (
    <Grid
      container
      item
      xs={12}
      mt={2}
      sx={{
        display: "flex",
        border: "1px solid rgba(0, 0, 0, 0.23)",
        paddingBottom: "40px",
      }}
    >
      <Grid item xs={12}>
        <Typography variant="body1" component="span" sx={sectionStyle}>
          Recommended Buffer
        </Typography>
      </Grid>
      <Grid item xs={6} mt={-1}>
        <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
          <SingleValueChart singleValueChartData={currectBufferChartData} />
        </Box>
      </Grid>
      <Grid item xs={6} mt={-1}>
        <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
          <SingleValueChart singleValueChartData={newBufferChartData} />
        </Box>
      </Grid>
    </Grid>
  );
}
