import React, { useState, useCallback, useMemo, type JSX } from "react";
import { Box, Grid, Typography } from "@mui/material";
import useS<PERSON> from "swr";
import ProjectVintage, { mapBookVintagesToProjectVintages } from "@models/project-vintage";
import COLORS from "@components/ui/theme/colors";
import { Maybe, pickFromArrayOfRecords, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import PortfolioCompositionTable from "./portfolio-composition-table";
import PortfolioDataCharts, { DataChartComponent } from "./portfolio-data-charts";
import ChartsBtnGroup, { FilterBtns, FilterBtnsEnum } from "./charts-btn-group";
import PortfolioPriceCharts from "./portfolio-price-charts";
import PortfolioBufferCharts from "./portfolio-buffer-charts";
import Decimal from "decimal.js";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { PriceSourceEnum } from "@constants/state";
import ProjectValidation from "@components/alerts/project-validation/project-validation";
import {
  AdminBookResponse,
  AdminProjectQueryResponse,
  AdminAllocationResponse,
  BookRelations,
  AdminGroupedAllocationWithNestedResponse,
  AdminProjectGroupedAllocationResponse,
  ProjectRelations,
  uuid,
  AdminVintagePricingResponse,
} from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import PortfolioName from "@components/ui/portfolio-name/portfolio-name";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import usePerformantEffect from "@hooks/use-performant-effect";


interface ButtonGroupDisplay {
  all: boolean;
  buffer: boolean;
  price: boolean;
  project: boolean;
  country: boolean;
  type: boolean;
  vintage: boolean;
}

const defaultBtnDisplay: ButtonGroupDisplay = {
  all: false,
  buffer: false,
  price: false,
  project: false,
  country: false,
  type: false,
  vintage: false,
};

const curAllocationColorScale = [
  "#0D330A",
  "#244720",
  "#3B5D35",
  "#52734B",
  "#6A8963",
  "#82A17B",
  "#9BB894",
  "#B5D1AE",
  "#CFEAC8",
  "#DFFAD8",
];

const projectedColorScale = [
  "#3F51B5",
  "#0288D1",
  "#880E4F",
  "#FFE0B2",
  "#F57C00",
  "#F5AE80",
  "#F9BD96",
  "#FDCBAC",
  "#FFDAC3",
  "#F7F2E0",
];

const subTitleStyle = {
  fontWeight: 400,
  color: COLORS.pureBlack,
  fontSize: "15px",
  lineHeight: "24px",
};

interface PortfolioCompositionProps {
  portfolio: AdminBookResponse;
}

export default function BookComposition({ portfolio: serverPortfolio }: PortfolioCompositionProps): JSX.Element {
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [buttonsDisplay, setButtonsDisplay] = useState<ButtonGroupDisplay>({
    all: true,
    buffer: false,
    price: false,
    project: false,
    country: false,
    type: false,
    vintage: false,
  });

  const { data: portfolio, trigger: refreshPortfolio } = useTriggerRequest<AdminBookResponse>({
    url: `/admin/books/${serverPortfolio?.id}`,
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeRelations: [
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        BookRelations.ORGANIZATION,
        BookRelations.PRICES,
      ],
    },
    optimisticData: serverPortfolio,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load portfolio");
        logger.error(`Unable to load portfolio: ${error?.message}`, {});
      },
    },
  });

  const bookAllocations = portfolio?.ownerAllocations as AdminGroupedAllocationWithNestedResponse;

  const basketVintageIds: uuid[] = useMemo(
    () =>
      bookAllocations?.allocations
        ? pickFromArrayOfRecords<AdminAllocationResponse>(bookAllocations.allocations, ["asset"]).map(
            (id) => id.asset.id,
          ) as uuid[]
        : [],
    [bookAllocations?.allocations],
  );

  const bookProjectIds: uuid[] = useMemo(
    () =>
      portfolio?.ownerAllocationsByProject
        ? pickFromArrayOfRecords<AdminProjectGroupedAllocationResponse>(
            portfolio.ownerAllocationsByProject,
            ["project"],
          ).map(({ project }) => project?.id) as uuid[]
        : [],
    [portfolio?.ownerAllocationsByProject],
  );

  const { data: projectsQueryResponse } = useSWR<AdminProjectQueryResponse>(
    !isEmpty(bookProjectIds)
      ? `/admin/projects?ids=${bookProjectIds.join(",")}&includeRelations=${ProjectRelations.BUFFER_CATEGORY}&limit=${SERVER_PAGINATION_LIMIT}`
      : null,
  );

  const totalQuantity = portfolio?.ownerAllocations?.totalAmountAllocated;

  const { data: traderPricings, trigger: refreshTraderPricings } = useTriggerRequest<AdminVintagePricingResponse[]>({
    url: "reporting/vintage-pricing",
    method: "post",
    cancelable: false,
    queryParams: {
      offset: 0,
      limit: basketVintageIds.length,
      source: PriceSourceEnum.RUBICON,
    },
    requestBody: {
      vintage_ids: basketVintageIds,
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch trader prices.");
        logger.error(`Unable to fetch trader prices: ${error?.message}`, {});
      },
    },
  });

  const tradersToLatestPrices: Record<string, Decimal> = useMemo(
    () =>
      traderPricings?.length
        ? traderPricings.reduce(
            (accum, record) => ({
              ...accum,
              [record.vintage_id]: record.price,
            }),
            {},
          )
        : {},
    [traderPricings],
  );

  const [projectedAllocation, setProjectedAllocation] = useState<ProjectVintage[]>([]);
  const [currentAllocation, setCurrentAllocation] = useState<ProjectVintage[]>([]);
  const [basketVintages, setBasketVintages] = useState<ProjectVintage[]>([]);

  usePerformantEffect(() => {
    if (!traderPricings && basketVintageIds.length > 0) {
      refreshTraderPricings();
    }
  }, [basketVintageIds, traderPricings, refreshTraderPricings]);

  usePerformantEffect(() => {
    if (portfolio && !isEmpty(projectsQueryResponse?.data)) {
      const result = mapBookVintagesToProjectVintages(
        portfolio,
        tradersToLatestPrices,
        totalQuantity,
        // projectsQueryResponse?.data, // todo : @kofi to confirm
      );
      setBasketVintages(result);
      setProjectedAllocation(result);
      setCurrentAllocation(result);
    }
  }, [portfolio, projectsQueryResponse, tradersToLatestPrices, totalQuantity]);

  const projectedAllocationHandler = (projectedData: ProjectVintage[]): void => {
    setProjectedAllocation(projectedData);
  };

  const resetVintagesHandler = (): void => {
    setProjectedAllocation(mapBookVintagesToProjectVintages(portfolio!));
  };

  const dataComponents: DataChartComponent[] = [
    {
      title: "Project",
      component1Title: "Current allocation by project",
      description1: "This chart displays the current project allocation of the selected portfolio.",
      component2Title: "New allocation by project",
      description2: "This table displays the projected allocation of the selected portfolio based on changes entered.",
      groupBy: "projectName",
      colorScale1: curAllocationColorScale,
      colorScale2: projectedColorScale,
      basketComponents1: currentAllocation,
      basketComponents2: projectedAllocation,
      groupTitle: "Project",
      show: buttonsDisplay.all || buttonsDisplay.project,
      isCap: false,
    },
    {
      title: "Country",
      component1Title: "Current allocation by country",
      description1: "This table displays the current project allocation by country of the selected portfolio.",
      component2Title: "New allocation by country",
      description2:
        "This table displays the projected allocation by country of the selected portfolio based on changes entered above.",
      groupBy: "projectLocation",
      colorScale1: curAllocationColorScale,
      colorScale2: projectedColorScale,
      basketComponents1: currentAllocation,
      basketComponents2: projectedAllocation,
      groupTitle: "Country",
      show: buttonsDisplay.all || buttonsDisplay.country,
    },
    {
      title: "Project type",
      component1Title: "Current allocation by type",
      description1: "This table displays the current project allocation by type of the selected portfolio.",
      component2Title: "New allocation by type",
      description2:
        "This table displays the projected allocation by type of the selected portfolio based on changes entered above.",
      groupBy: "projectType",
      colorScale1: curAllocationColorScale,
      colorScale2: projectedColorScale,
      basketComponents1: currentAllocation,
      basketComponents2: projectedAllocation,
      groupTitle: "Type",
      show: buttonsDisplay.all || buttonsDisplay.type,
    },
    {
      title: "Vintage",
      component1Title: "Current allocation by vintage",
      description1: "This table displays the current project allocation by vintage of the selected portfolio.",
      component2Title: "New allocation by vintage",
      description2:
        "This table displays the projected allocation by vintage of the selected portfolio based on changes entered above.",
      groupBy: "vintageName",
      colorScale1: curAllocationColorScale,
      colorScale2: projectedColorScale,
      basketComponents1: currentAllocation,
      basketComponents2: projectedAllocation,
      groupTitle: "Vintage",
      show: buttonsDisplay.all || buttonsDisplay.vintage,
    },
  ];

  const filterBtnsHandler = useCallback(
    (id: string) => {
      setButtonsDisplay({ ...defaultBtnDisplay, [id]: true });
    },
    [setButtonsDisplay],
  );

  const filterBtns: FilterBtns[] = [
    {
      label: "All",
      filter: FilterBtnsEnum.ALL,
      selected: buttonsDisplay.all,
    },
    {
      label: "Buffer",
      filter: FilterBtnsEnum.BUFFER,
      selected: buttonsDisplay.buffer,
    },
    {
      label: "Price",
      filter: FilterBtnsEnum.PRICE,
      selected: buttonsDisplay.price,
    },
    {
      label: "Project",
      filter: FilterBtnsEnum.PROJECT,
      selected: buttonsDisplay.project,
    },
    {
      label: "Country",
      filter: FilterBtnsEnum.COUNTRY,
      selected: buttonsDisplay.country,
    },
    {
      label: "Type",
      filter: FilterBtnsEnum.TYPE,
      selected: buttonsDisplay.type,
    },
    {
      label: "Vintage",
      filter: FilterBtnsEnum.VINTAGE,
      selected: buttonsDisplay.vintage,
    },
  ].filter((btn) => !!btn);

  return (
    <>
      <ProjectValidation
        extendedAllocations={
          (portfolio?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations as any
        }
      />
      <Grid container spacing={0}>
        <Grid item xs={12} mt={1}>
          <Typography variant="body2" component="span" sx={subTitleStyle}>
            Use the table below to add or remove project vintages.
          </Typography>
        </Grid>
        <Grid item xs={12} mt={2}>
          <Box sx={{ color: COLORS.black }}>
            <PortfolioName
              name={portfolio!.name}
              includeColorChip
              color={getPortfolioColor(portfolio!.id)}
              sx={{ fontSize: "20px" }}
            />
          </Box>
        </Grid>
        <Grid item xs={12} mt={3} mb={3}>
          <PortfolioCompositionTable
            portfolioId={portfolio?.id}
            rows={basketVintages}
            allowedProjectTypes={portfolio?.ownerAllocationsByProjectType?.map((m) => m.projectType) ?? []}
            onProjectedAllocationChanged={projectedAllocationHandler}
            onVintagesReset={resetVintagesHandler}
            onRefresh={refreshPortfolio}
          />
        </Grid>
        <ChartsBtnGroup filterBtns={filterBtns} filterBtnsHandler={filterBtnsHandler} />
        <Maybe condition={buttonsDisplay.all || buttonsDisplay.buffer}>
          <PortfolioBufferCharts currentAllocation={currentAllocation} projectedAllocation={projectedAllocation} />
        </Maybe>
        <Maybe condition={buttonsDisplay.all || buttonsDisplay.price}>
          <PortfolioPriceCharts currentAllocation={currentAllocation} projectedAllocation={projectedAllocation} />
        </Maybe>
        <PortfolioDataCharts dataComponents={dataComponents} />
      </Grid>
    </>
  );
}
