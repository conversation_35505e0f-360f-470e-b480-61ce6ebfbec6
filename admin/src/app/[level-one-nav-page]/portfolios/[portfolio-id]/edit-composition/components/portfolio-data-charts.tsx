import React, { type JSX } from "react";
import { Box, Grid, Typography } from "@mui/material";
import ProjectVintage from "@models/project-vintage";
import COLORS from "@components/ui/theme/colors";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ComponentBreakdown from "./component-breakdown-chart";

export interface DataChartComponent {
  title: string;
  component1Title: string;
  description1: string;
  component2Title: string;
  description2: string;
  groupBy: string;
  colorScale1: string[];
  colorScale2: string[];
  basketComponents1: ProjectVintage[];
  basketComponents2: ProjectVintage[];
  groupTitle: string;
  show: boolean;
  isCap?: boolean;
}

interface PortfolioDataChartsProps {
  dataComponents: DataChartComponent[];
}

export default function PortfolioDataCharts(props: PortfolioDataChartsProps): JSX.Element {
  const { dataComponents } = props;

  const sectionStyle = {
    fontWeight: 500,
    color: COLORS.pureBlack,
    fontSize: "20px",
    lineHeight: "60px",
    paddingLeft: "30px",
  };

  const componentStyle = {
    padding: 1.5,
    borderRadius: "4px",
    height: "100%",
  };

  return (
    <>
      {dataComponents.map((component) => (
        <Maybe key={component.groupBy} condition={component.show}>
          <Grid container item xs={12} mt={2} sx={{ display: "flex", border: "1px solid rgba(0, 0, 0, 0.23)" }}>
            <Grid item xs={12}>
              <Typography variant="body1" component="span" sx={sectionStyle}>
                {component.title}
              </Typography>
            </Grid>
            <Grid item xs={6} mt={-1}>
              <Box sx={componentStyle}>
                <ComponentBreakdown
                  title={component.component1Title}
                  description={component.description1}
                  basketComponents={component.basketComponents1}
                  colorScale={component.colorScale1}
                  backColor={COLORS.paleBlue}
                  groupField={component.groupBy}
                  groupTitle={component.groupTitle}
                  isCap={component.isCap}
                />
              </Box>
            </Grid>
            <Grid item xs={6} mt={-1}>
              <Box sx={componentStyle}>
                <ComponentBreakdown
                  title={component.component2Title}
                  description={component.description2}
                  basketComponents={component.basketComponents2}
                  colorScale={component.colorScale2}
                  backColor={COLORS.paleGreen}
                  groupField={component.groupBy}
                  groupTitle={component.groupTitle}
                  isCap={component.isCap}
                />
              </Box>
            </Grid>
          </Grid>
        </Maybe>
      ))}
    </>
  );
}
