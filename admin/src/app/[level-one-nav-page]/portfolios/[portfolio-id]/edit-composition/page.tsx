import { AuthorizeServer } from "@app/authorize-server";
import { AdminBookResponse, BookRelations, PermissionEnum } from "@rubiconcarbon/shared-types";
import PortfolioComposition from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Portfolio Composition Page
 *
 * This is a server component that renders the Portfolio Composition page
 */
export default async function PortfolioCompositionPage({
  params,
}: {
  params: Promise<{ "portfolio-id": string }>;
}): Promise<JSX.Element> {
  const { "portfolio-id": id } = await params;

  const portfolioResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookResponse>(
      `admin/books/${id}?${generateQueryParams({
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT,
          BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
          BookRelations.ORGANIZATION,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(portfolioResponse)) return portfolioResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_READ]}>
      <PortfolioComposition portfolio={portfolioResponse as AdminBookResponse} />
    </AuthorizeServer>
  );
}
