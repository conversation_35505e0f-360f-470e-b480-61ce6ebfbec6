import { ChangeEvent, useCallback, useEffect, useMemo, useState, type JSX } from "react";
import {
  AdminBookResponse,
  AdminGroupedAllocationWithNestedResponse,
  AdminProjectTypeResponse,
  AllRubiconHoldingBookTypes,
} from "@rubiconcarbon/shared-types";
import EnhancedTable, { ColDef } from "@components/ui/table/enhanced-table";
import { FormControlLabel, Grid, Stack, Tooltip, Typography } from "@mui/material";
import ActionButton from "@components/ui/action-button/action-button";
import StatusChip from "@/components/ui/status-chip/StatusChip";
import {
  deepClone,
  pickFromArrayOfRecords,
  searchByKeys,
  toTitleCase,
  uniqueByKeys,
} from "@rubiconcarbon/frontend-shared";
import RowCheckbox from "./row-checkbox";
import Decimal from "decimal.js";

type ProjectTypeItem = {
  checked: boolean;
  allocated?: boolean;
  updateType?: "added" | "removed";
} & AdminProjectTypeResponse;

type ProjectCategoryItem = {
  id: string;
  checked: boolean;
  hasAllocated?: boolean;
  category: string;
  totalAvailable: number;
  types: ProjectTypeItem[];
};

const holdingAmount = (type: Partial<ProjectTypeItem>): number => {
  return Decimal.sum(
    ...(type?.assetAllocationsByBookType || [])
      .filter((f) => AllRubiconHoldingBookTypes.includes(f.bookType))
      .map((m) => m.totalAmountAllocated),
    0,
  ).toNumber();
};

const EligibleProjectTypes = ({
  portfolio,
  projectTypes,
}: {
  portfolio: AdminBookResponse;
  projectTypes: AdminProjectTypeResponse[];
}): JSX.Element => {
  //   const [confirmationDialogOpened, setConfirmationDialogOpened] = useState<boolean>(false);
  const [allSelected, setAllSelected] = useState<boolean>(false);
  const [someSelected, setSomeSelected] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [allSelectedCategory, setAllSelectedCategory] = useState<Record<string, boolean>>({});
  const [someSelectedCategory, setSomeSelectedCategory] = useState<Record<string, boolean>>({});
  const [projectCategories, setProjectCategories] = useState<ProjectCategoryItem[]>([]);
  const [filteredIds, setFilteredIds] = useState<string[]>([]);

  const original = useMemo(
    () => Object.freeze(portfolio?.ownerAllocationsByProjectType || []),
    [portfolio?.ownerAllocationsByProjectType],
  );
  // todo : @kofi to check, I think you can just look in ownerAllocationsByProjectTypeId
  const originalIds = useMemo(() => Object.freeze(original.map(({ projectType }) => projectType.id)), [original]);
  const allocatedIds = useMemo(() => {
    return (portfolio?.ownerAllocations as AdminGroupedAllocationWithNestedResponse)?.allocations?.reduce(
      (accum: number[], component) => {
        const id = component.asset.projectTypeId;
        if (!accum.includes(id)) accum.push(id);
        return accum;
      },
      [],
    );
  }, [portfolio?.ownerAllocations]);

  const originalCategories = useMemo(() => {
    const responseData = projectTypes || [];
    const uniqueCategories = uniqueByKeys(responseData, ["category"]).map(({ category }) => category);

    // todo : @kofi I think this whole part needs to be rewritten too, it should be ablet o use ownerAllocationsByProjectType
    const categories: ProjectCategoryItem[] = uniqueCategories.reduce((accum, current) => {
      let types = pickFromArrayOfRecords<AdminProjectTypeResponse>(
        responseData.filter(({ category }) => category === current),
        ["id", "name", "type", "assetAllocationsByBookType"],
      ) as ProjectTypeItem[];

      types = types.map((projectType: ProjectTypeItem) => ({
        allocated: allocatedIds?.includes(projectType.id),
        ...projectType,
        checked: originalIds.includes(projectType.id),
      }));

      accum.push({
        id: current,
        hasAllocated: types?.some(({ allocated }) => !!allocated),
        checked: types?.every(({ checked }) => !!checked),
        category: current,
        totalAvailable: types.reduce((sum, type) => sum + holdingAmount(type), 0),
        types,
      });

      return accum;
    }, [] as ProjectCategoryItem[]);
    return categories;
  }, [allocatedIds, originalIds, projectTypes]);

  useEffect(() => {
    setProjectCategories(
      Object.assign<ProjectCategoryItem[], ProjectCategoryItem[]>([], originalCategories).sort((a, b) =>
        a.hasAllocated ? -1 : b.hasAllocated ? 1 : 0,
      ),
    );
    setFilteredIds(originalCategories.map(({ id }) => id));
  }, [originalCategories]);

  useEffect(() => {
    setAllSelectedCategory(
      projectCategories.reduce((accum, { category, types }) => {
        Object.assign(accum, { [category]: types.every(({ checked }) => !!checked) });
        return accum;
      }, {}),
    );
  }, [projectCategories]);

  useEffect(() => {
    setSomeSelectedCategory(
      projectCategories.reduce((accum, { category, types }) => {
        Object.assign(accum, { [category]: !allSelectedCategory[category] && types.some(({ checked }) => !!checked) });
        return accum;
      }, {}),
    );
  }, [projectCategories, allSelectedCategory]);

  useEffect(() => {
    setAllSelected(Object.values(allSelectedCategory).every((checked) => !!checked));
  }, [allSelectedCategory]);

  useEffect(() => {
    setSomeSelected(
      !allSelected &&
        (Object.values(someSelectedCategory).some((checked) => !!checked) ||
          Object.values(allSelectedCategory).some((checked) => !!checked)),
    );
  }, [allSelected, allSelectedCategory, someSelectedCategory]);

  useEffect(() => {
    setHasChanges(projectCategories.some(({ types }) => types.some(({ updateType }) => !!updateType)));
  }, [projectCategories]);

  const COLUMNS: ColDef[] = [
    {
      columnName: "checked",
      displayName: "",
      displayComponent: (
        <RowCheckbox
          name="global"
          checked={allSelected}
          indeterminate={someSelected}
          onChange={(event: ChangeEvent<HTMLInputElement>) => handleCheckboxCheck(event)}
        />
      ),
      formatter: {
        func: (data: any) => (
          <RowCheckbox
            value={data.get("id") as number}
            checked={allSelectedCategory[data.get("category") as string]}
            indeterminate={someSelectedCategory[data.get("category") as string]}
            disabled={data.get("allocated") as boolean}
            onChange={handleCheckboxCheck}
          />
        ),
        inputFields: ["id", "checked", "allocated", "category"],
      },
      sortable: false,
    },
    {
      columnName: "category",
      displayName: "Category (Total Holding)",
      formatter: {
        func: (data: any) => (
          <Typography>
            {data.get("category") as string}
            {"  "}
            <strong>({data.get("totalAvailable") as string})</strong>
          </Typography>
        ),
        inputFields: ["category", "totalAvailable"],
      },
    },
    {
      columnName: "types",
      displayName: "Types (Total Holding)",
      formatter: {
        func: (data: any): JSX.Element => (
          <Stack>
            {(data.get("types") as ProjectTypeItem[]).map(({ id, checked, type, allocated, updateType, ...rest }) => (
              <Grid key={type} container gap={1} justifyContent="space-between" alignItems="center">
                <Grid item sm={8} md={8} lg={9} xl={9.5}>
                  <Tooltip title={allocated ? "Credits are allocated to the book" : ""}>
                    <FormControlLabel
                      control={
                        <RowCheckbox
                          name={data.get("category") as string}
                          value={id}
                          checked={checked}
                          disabled={allocated}
                          onChange={handleCheckboxCheck}
                        />
                      }
                      label={
                        <>
                          {toTitleCase(type, null, /(and|to|in)/i)} <strong>({holdingAmount(rest)})</strong>
                        </>
                      }
                    />
                  </Tooltip>
                </Grid>
                <Grid item sm={3} md={3} lg={2} xl={1.5}>
                  <StatusChip allocated={allocated} updateType={updateType} />
                </Grid>
              </Grid>
            ))}
          </Stack>
        ),
        inputFields: ["category", "totalAvailable", "types"],
      },
    },
  ];

  const handleCheckboxCheck = (event: ChangeEvent<HTMLInputElement>): void => {
    event.preventDefault();

    const { checked, value, name } = event.currentTarget;
    const clone: ProjectCategoryItem[] = deepClone(projectCategories);

    if (name === "global") {
      setProjectCategories((previous) =>
        previous.map(({ types, ...rest }) => ({
          ...rest,
          checked,
          types: types.map(({ id, updateType, allocated, ...rest }) => ({
            id,
            allocated,
            ...rest,
            checked: !allocated ? checked : rest.checked,
            updateType: !allocated
              ? originalIds.includes(id)
                ? !checked
                  ? "removed"
                  : undefined
                : !originalIds.includes(id)
                  ? checked
                    ? "added"
                    : undefined
                  : updateType
              : undefined,
          })),
        })),
      );
    } else {
      const checkValue = parseInt(value);

      if (isNaN(checkValue)) {
        const catIndex = clone.findIndex((cat) => cat.category === value);
        const types = clone[catIndex].types;

        clone[catIndex].types = types.map(({ id, updateType, allocated, ...rest }) => ({
          id,
          allocated,
          ...rest,
          checked: !allocated ? checked : rest.checked,
          updateType: !allocated
            ? originalIds.includes(id)
              ? !checked
                ? "removed"
                : undefined
              : !originalIds.includes(id)
                ? checked
                  ? "added"
                  : undefined
                : updateType
            : undefined,
        }));

        setProjectCategories(clone);
      } else {
        const catIndex = clone.findIndex((cat) => cat.category === name);
        const types = clone[catIndex].types;
        const typeIndex = types.findIndex((type) => type.id === checkValue);
        const type = clone[catIndex].types[typeIndex];

        type.checked = type.id === checkValue ? checked : type.checked;
        type.updateType =
          type.id === checkValue
            ? originalIds.includes(checkValue)
              ? !checked
                ? "removed"
                : undefined
              : !originalIds.includes(checkValue)
                ? checked
                  ? "added"
                  : undefined
                : type.updateType
            : type.updateType;

        setProjectCategories(clone);
      }
    }
  };

  const handleProjectTypesSearch = (input: string): void => {
    const filteredIds = searchByKeys(input, projectCategories, ["category", "types.type"]).map(({ id }) => id);
    setFilteredIds(filteredIds);
  };

  const handleChangesReset = useCallback(() => {
    setProjectCategories(originalCategories.sort((a, b) => (a.hasAllocated ? -1 : b.hasAllocated ? 1 : 0)));
  }, [originalCategories]);

  const SearhBarContent = useMemo(
    (): JSX.Element => (
      <ActionButton onClickHandler={() => handleChangesReset()} isDisabled={!hasChanges}>
        Reset
      </ActionButton>
    ),
    [handleChangesReset, hasChanges],
  );
  return (
    <EnhancedTable
      name="projectCategories"
      columnsDef={COLUMNS}
      data={projectCategories.filter(({ id }) => filteredIds.includes(id))}
      elevation={0}
      getFilteredData={handleProjectTypesSearch}
      searchBarContent={() => SearhBarContent}
    />
  );
};

export default EligibleProjectTypes;
