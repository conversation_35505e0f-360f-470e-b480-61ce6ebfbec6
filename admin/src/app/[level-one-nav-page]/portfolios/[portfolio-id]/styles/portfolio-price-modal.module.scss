.stepsContainer {
    position: relative;
    flex-grow: 100%;
    padding-left: 70px;
    padding-right: 70px;
}

.step {
    border-radius: 8;
    background-color: green;
    width: 20px !important;
    padding: 0px;
    min-width: 20px;
    min-height: 20px;
}

.stepTitle {
    font-size: 14px !important;
    font-weight: 500 !important;
    line-height: 22px !important;
    letter-spacing: 0.10000000149011612px !important;
    text-align: left !important;
    padding-left: 10px;
    word-wrap: break-word;
    display: inline-flex;
}

.buttonStepNormal {
    background-color: rgba(9, 68, 54, 1) !important;
    color: white !important;
    width: 20px !important;
    padding: 0px !important;
    padding-left: 1px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    height: 20px !important;
    text-align: center !important;
}

.buttonStepDisabled {
    background-color: rgba(0, 0, 0, 0.38) !important;
    color: white !important;
    width: 20px !important;
    padding: 0px !important;
    padding-left: 1px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    height: 20px !important;
    text-align: center !important;
}

.filterTitle {
    display: inline-flex;
    padding: 4px;
    text-Align: left;

}