"use client";;
import Page from "@components/layout/containers/page";
import { AdminOrganizationResponse } from "@rubiconcarbon/shared-types";
import NewCustomPortfolioComponent from "./new-custom-portfolio";

import type { JSX } from "react";

const NewCustomPortfolio = ({ organizations }: { organizations: AdminOrganizationResponse[] }): JSX.Element => {
  return (
    <Page>
      <NewCustomPortfolioComponent organizations={organizations} />
    </Page>
  );
};

export default NewCustomPortfolio;
