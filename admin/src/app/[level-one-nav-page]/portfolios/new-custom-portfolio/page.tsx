import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, AdminOrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import NewCustomPortfolio from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * New Custom Portfolio Page
 *
 * This is a server component that renders the New Custom Portfolio page
 */
export default async function NewCustomPortfolioPage(): Promise<JSX.Element> {
  const organizationsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationQueryResponse>(`admin/organizations`),
  );
  // Check if the result is a server error
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.TRADES_READ, PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS]}>
      <NewCustomPortfolio organizations={(organizationsResponse as AdminOrganizationQueryResponse)?.data as AdminOrganizationResponse[]} />
    </AuthorizeServer>
  );
}
