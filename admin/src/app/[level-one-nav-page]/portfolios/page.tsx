import { AuthorizeServer } from "@app/authorize-server";
import { AdminBookQueryResponse, BookRelations, BookType, PermissionEnum } from "@rubiconcarbon/shared-types";
import Portfolios from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * Portfolios Page
 *
 * This is a server component that renders the Portfolios page
 */
export default async function PortfoliosPage(): Promise<JSX.Element> {
  const booksResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        types: [BookType.PORTFOLIO_CUSTOM, BookType.PORTFOLIO_PUBLIC],
        includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.OWNER_ALLOCATIONS_BY_PROJECT],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(booksResponse)) return booksResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_READ]}>
      <Portfolios booksResponse={booksResponse as AdminBookQueryResponse} />
    </AuthorizeServer>
  );
}
