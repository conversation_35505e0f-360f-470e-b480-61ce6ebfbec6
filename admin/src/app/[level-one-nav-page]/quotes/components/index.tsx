"use client";;
import Page from "@components/layout/containers/page";
import QuotesComponent from "./quotes";
import { QuoteQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const Quotes = ({ quotesResponse }: { quotesResponse: QuoteQueryResponse }): JSX.Element => {
  return (
    <Page>
      <QuotesComponent quotesResponse={quotesResponse} />
    </Page>
  );
};

export default Quotes;
