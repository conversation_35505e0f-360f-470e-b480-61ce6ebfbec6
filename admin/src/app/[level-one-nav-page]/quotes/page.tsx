import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, QuoteQueryResponse } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { isValidElement, type JSX } from "react";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import Quotes from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";

/**
 * Quotes Page
 *
 * This is a server component that renders the Quotes page
 */
export default async function QuotesPage(): Promise<JSX.Element> {
  const quotesResponse = await withErrorHandling(async () =>
    baseApiRequest<QuoteQueryResponse>(
      `admin/quotes?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(quotesResponse)) return quotesResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.QUOTES_READ]}>
      <Quotes quotesResponse={quotesResponse as QuoteQueryResponse} />
    </AuthorizeServer>
  );
}
