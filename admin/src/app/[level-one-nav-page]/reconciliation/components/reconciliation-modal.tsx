import { useState, useEffect, useContext, useC<PERSON>back, type JSX } from "react";
import {
  Modal,
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  CircularProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { useLogger } from "@providers/logging";
import { AxiosContext } from "@providers/axios-provider";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import MaterialReactTable from "material-react-table";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";

import classes from "../styles/styles.module.scss";

interface ReconciliationModalProps {
  open: boolean;
  onClose: () => void;
  data: any[];
  reconType: string;
  autoReconcile?: boolean;
  onSubmit: (reconciliationData: any) => void;
}

interface RegistryItem {
  source: string;
  transaction_type: string;
  internal_id?: string;
  credits: number;
  registry: string;
  registry_project_id: string;
  vintage: string;
  transaction_key?: string;
  transaction_id?: string;
  date_acquired?: number;
}

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "80%",
  maxHeight: "80vh",
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: 1,
  overflow: "auto",
};

export default function ReconciliationModal({
  open,
  onClose,
  data,
  reconType,
  autoReconcile,
  onSubmit,
}: ReconciliationModalProps): JSX.Element {
  const { api } = useContext(AxiosContext);
  const [reconciliationData, setReconciliationData] = useState<any[]>([]);
  const [registryItems, setRegistryItems] = useState<RegistryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { logger } = useLogger();
  const [itemSelectionOpen, setItemSelectionOpen] = useState(false);
  const [currentRowIndex, setCurrentRowIndex] = useState<number | null>(null);

  const fetchRegistryItems = useCallback(
    async (registry?: string): Promise<void> => {
      setLoading(true);
      try {
        const endpoint = registry
          ? `reporting/recon/${reconType}?registry=${encodeURIComponent(registry.toLowerCase())}`
          : `reporting/recon/${reconType}`;

        const response = await api.get(endpoint);

        setRegistryItems(response.data || []);
      } catch (error: any) {
        logger.error(`Failed to fetch registry items: ${error?.message}`, {});
        setRegistryItems([]);
      } finally {
        setLoading(false);
      }
    },
    [api, reconType, logger],
  );

  useEffect(() => {
    if (open) {
      if (autoReconcile) {
        setLoading(true);
        api
          .get(`reporting/recon/${reconType}/recommendations`)
          .then((response) => {
            if (response.data && Array.isArray(response.data)) {
              const recommendedReconciliations = response.data.map((item) => ({
                line_item_transaction_id: item.line_item_transaction_id || "",
                transaction_key: item.transaction_key || "",
                registry: item.registry || "",
                registry_item_id: item.registry_item_id || "",
                match: true, // Recommended matches are assumed to be true
                reconciled: false, // User needs to confirm reconciliation
                line_item_transaction_amount: item.line_item_transaction_amount || 0,
                registry_item_amount: item.registry_item_amount || 0,
                notes: "",
                transaction_type: item.transaction_type || "",
                source: item.source || "",
              }));
              setReconciliationData(recommendedReconciliations);
            } else {
              setReconciliationData([]);
            }
            setLoading(false);
          })
          .catch((error) => {
            logger.error("Failed to fetch reconciliation recommendations:", error);
            setReconciliationData([]);
            setLoading(false);
          });
      } else {
        setReconciliationData(
          data.map((item) => ({
            line_item_transaction_id: item.internal_id || "",
            transaction_key: item.transaction_key || "",
            registry: item.registry || "",
            registry_item_id: "",
            match: false,
            reconciled: false,
            line_item_transaction_amount: item.credits || 0,
            registry_item_amount: 0,
            notes: "",
            transaction_type: item.transaction_type || "",
            source: item.source || "",
          })),
        );
      }

      // Always fetch registry items when modal opens
      fetchRegistryItems();
    } else {
      setReconciliationData([]);
    }
  }, [open, data, logger, autoReconcile, reconType, api, fetchRegistryItems]);

  // Filter registry items when registry changes
  const handleRegistryChange = (index: number, value: string): void => {
    handleInputChange(index, "registry", value);
    handleInputChange(index, "registry_item_id", ""); // Reset selected item when registry changes
    fetchRegistryItems(value); // Fetch items for the selected registry
  };

  const handleInputChange = (index: number, field: string, value: any): void => {
    const updatedData = [...reconciliationData];
    updatedData[index] = { ...updatedData[index], [field]: value };
    setReconciliationData(updatedData);
  };

  const handleAddRow = (): void => {
    if (reconciliationData.length === 0) {
      // If there are no rows yet, add an empty one
      const emptyRow = {
        line_item_transaction_id: "",
        registry: "",
        registry_item_id: "",
        match: false,
        reconciled: false,
        notes: "",
        line_item_transaction_amount: 0,
        registry_item_amount: 0,
        transaction_type: "",
        source: "",
      };
      setReconciliationData([emptyRow]);
    } else {
      // Get the last row and duplicate it
      const lastRow = reconciliationData[reconciliationData.length - 1];

      // Clone the last row but clear certain fields that should be unique
      const duplicatedRow = {
        ...lastRow,
        registry_item_id: "",
        registry_item_amount: 0,
      };

      setReconciliationData([...reconciliationData, duplicatedRow]);
    }
  };

  const handleSubmit = (): void => {
    onSubmit(reconciliationData);
    onClose();
  };

  const handleSelectRegistryItem = (index: number): void => {
    setCurrentRowIndex(index);
    setItemSelectionOpen(true);
  };

  const handleItemSelected = (item: RegistryItem): void => {
    if (currentRowIndex !== null) {
      // Create a new copy of the reconciliation data
      const updatedData = [...reconciliationData];

      // Update all the relevant fields from the selected item in one update
      updatedData[currentRowIndex] = {
        ...updatedData[currentRowIndex],
        registry_item_id: item.internal_id || "",
        registry_item_amount: item.credits || 0,
        registry: item.registry || updatedData[currentRowIndex].registry,
        vintage: item.vintage || updatedData[currentRowIndex].vintage,
      };

      setReconciliationData(updatedData);

      // Close the dialog
      setItemSelectionOpen(false);
      setCurrentRowIndex(null);
    }
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="reconciliation-modal-title">
      <Box sx={modalStyle}>
        <Typography id="reconciliation-modal-title" variant="h6" component="h2" gutterBottom>
          Reconcile Transactions
        </Typography>

        {autoReconcile && (
          <Box sx={{ mb: 2, p: 1, bgcolor: "warning.light", borderRadius: 1 }}>
            <Typography variant="body2">
              <strong>Auto-reconciliation mode:</strong> The system has suggested potential matches between internal
              records and registry entries. Review the suggestions, make any necessary changes, and submit to confirm
              these reconciliations.
            </Typography>
          </Box>
        )}

        {loading && autoReconcile && (
          <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        <TableContainer component={Paper} sx={{ maxHeight: "calc(80vh - 200px)" }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell>Rubicon Line Item</TableCell>
                <TableCell>Registry</TableCell>
                <TableCell>Registry Item</TableCell>
                <TableCell>Match</TableCell>
                <TableCell>Reconciled</TableCell>
                <TableCell>Notes</TableCell>
                <TableCell>Credits (Rubicon)</TableCell>
                <TableCell>Credits (Registry)</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {reconciliationData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={row.transaction_key || ""}
                      onChange={(e) => handleInputChange(index, "transaction_key", e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={row.registry || ""}
                      onChange={(e) => handleRegistryChange(index, e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleSelectRegistryItem(index)}
                      startIcon={loading ? <CircularProgress size={16} /> : null}
                      disabled={loading}
                      fullWidth
                    >
                      {row.registry_item_id ? `Selected: ${row.registry_item_id.slice(0, 4)}...` : "Select"}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <FormControl fullWidth size="small">
                      <Select
                        value={row.match === true ? "true" : "false"}
                        onChange={(e) => handleInputChange(index, "match", e.target.value === "true")}
                      >
                        <MenuItem value="true">True</MenuItem>
                        <MenuItem value="false">False</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <FormControl fullWidth size="small">
                      <Select
                        value={row.reconciled === true ? "true" : "false"}
                        onChange={(e) => handleInputChange(index, "reconciled", e.target.value === "true")}
                      >
                        <MenuItem value="true">True</MenuItem>
                        <MenuItem value="false">False</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={row.notes || ""}
                      onChange={(e) => handleInputChange(index, "notes", e.target.value)}
                      placeholder="Add notes"
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      value={row.line_item_transaction_amount || 0}
                      disabled={true}
                      onChange={(e) => handleInputChange(index, "line_item_transaction_amount", e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      value={row.registry_item_amount || 0}
                      disabled={true}
                      onChange={(e) => handleInputChange(index, "registry_item_amount", e.target.value)}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
          <Button startIcon={<AddIcon />} variant="outlined" onClick={handleAddRow} disabled={autoReconcile}>
            Add Row
          </Button>

          <Box sx={{ display: "flex", gap: 2 }}>
            <Button variant="outlined" onClick={onClose}>
              Cancel
            </Button>
            <Button variant="contained" color="primary" onClick={handleSubmit}>
              Submit Reconciliation
            </Button>
          </Box>
        </Box>

        {currentRowIndex !== null && (
          <Dialog open={itemSelectionOpen} onClose={() => setItemSelectionOpen(false)} fullWidth maxWidth="lg">
            <DialogTitle>Select Registry Item</DialogTitle>
            <DialogContent dividers>
              <MaterialReactTable
                data={registryItems
                  .filter(
                    (item) =>
                      !reconciliationData[currentRowIndex]?.registry ||
                      item.registry === reconciliationData[currentRowIndex]?.registry,
                  )
                  .map((item, idx) => ({ ...item, id: idx }))}
                columns={[
                  { accessorKey: "transaction_type", header: "Type" },
                  { accessorKey: "registry_project_id", header: "Project ID" },
                  { accessorKey: "credits", header: "Credits" },
                  { accessorKey: "vintage", header: "Vintage" },
                  {
                    accessorFn: (row) => (row.date_acquired ? new Date(row.date_acquired).toDateString() : "-"),
                    header: "Date",
                  },
                  {
                    accessorFn: (row) => row.transaction_key,
                    header: "Select",
                    Cell: ({ row }) => (
                      <Box
                        className={classes.Link}
                        sx={{ cursor: "pointer" }}
                        onClick={() => handleItemSelected(row.original)}
                      >
                        <CheckCircleIcon fontSize="small" />
                      </Box>
                    ),
                  },
                ]}
                enableColumnActions={false}
                enableColumnFilters={false}
                enablePagination={true}
                enableSorting={false}
                enableBottomToolbar={true}
                enableTopToolbar={true}
                enableGlobalFilter={true}
                muiTableContainerProps={{ sx: { boxShadow: "none", maxHeight: "400px" } }}
                muiTableProps={{
                  sx: {
                    padding: "10px",
                    boxShadow: "none",
                    border: "0px",
                    width: "100%",
                  },
                }}
                muiTableHeadRowProps={{ sx: { boxShadow: "none", border: "0px" } }}
                muiTablePaperProps={{
                  sx: {
                    boxShadow: "none",
                    width: "100%",
                    height: "100%",
                    padding: "0 5px",

                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                  },
                }}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setItemSelectionOpen(false)}>Cancel</Button>
            </DialogActions>
          </Dialog>
        )}
      </Box>
    </Modal>
  );
}
