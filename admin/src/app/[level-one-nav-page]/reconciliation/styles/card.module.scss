.Link {
  display: flex;
  column-gap: 5px;
  font-size: 14px;
  font-weight: bold;
  text-decoration: none;
  text-transform: uppercase;
  color: #094436;
  padding: 5px;
}

.container {
  background-color: white;
  border-radius: 15px;
  height: 100%;
  z-index: 1;
}

.outer {
  padding: 10px;
}

.metadata {
  float: right;
  position: relative;
  right: 15px;
  bottom: 25px;
  font-size: 80%;
  color: rgb(1, 0, 0, 0.2);
  z-index: 2;
}

.toolbox {
  float: right;
  position: relative;
  right: 10px;
  top: 5px;
  font-size: 80%;
  color: rgb(1, 0, 0, 0.3);
}

.toolboxButton {
  cursor: pointer;
  margin-left: 5px;

  &:hover {
    color: rgb(1, 0, 0, 0.75);
  }
}

.toolboxButtonInactive {
  cursor: unset;
  opacity: 30%;
}

.title {
  padding: 15px 20px 0;
}

.subtitle {
  padding: 15px 21px 0;
}
