import { Box, Paper, Skeleton, Stack, SxProps, Typography } from "@mui/material";
import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";

import type { JSX } from "react";

type ProjectVintageAllocationItemProps = {
  label: string;
  value: number;
  thSx?: SxProps;
  tdSx?: SxProps;
  loading: boolean;
};

type ProjectVintageAllocationProps = {
  reservesAmount: number;
  allocatedToPortfolio: number;
  unallocated: number;
  tradeSellAmount: number;
  tradeBuyAmount: number;
  loading: boolean;
};

const ProjectVintageAllocationItem = ({
  label,
  value,
  thSx = {},
  tdSx = {},
  loading = false,
}: ProjectVintageAllocationItemProps): JSX.Element => {
  return (
    <Stack width="20%">
      <Box
        sx={{
          padding: 1,
          backgroundColor: "#E0E0E0",
          ...thSx,
        }}
      >
        <Typography variant="body2">{label}</Typography>
      </Box>
      <Box
        sx={{
          padding: 1.5,
          ...tdSx,
        }}
      >
        <Maybe condition={loading}>
          <Skeleton variant="text" />
        </Maybe>
        <Maybe condition={!loading}>
          <Typography variant="body2" fontWeight={200}>
            {numberFormat(value)}
          </Typography>
        </Maybe>
      </Box>
    </Stack>
  );
};

const ProjectVintageAllocation = (props: ProjectVintageAllocationProps): JSX.Element => {
  const {
    reservesAmount = 0,
    allocatedToPortfolio = 0,
    unallocated = 0,
    tradeSellAmount = 0,
    tradeBuyAmount = 0,
    loading = false,
  } = props;

  return (
    <Box padding={2} bgcolor={"rgb(247, 245, 245)"}>
      <Stack component={Paper} elevation={1} borderRadius={1}>
        <Box padding={1.5}>
          <Typography fontWeight={600}>Project Vintage Allocation</Typography>
        </Box>
        <Stack direction="row">
          <ProjectVintageAllocationItem label="Reserved" value={reservesAmount} loading={loading} />
          <ProjectVintageAllocationItem label="Allocated to Portfolio" value={allocatedToPortfolio} loading={loading} />
          <ProjectVintageAllocationItem label="Unallocated" value={unallocated} loading={loading} />
          <ProjectVintageAllocationItem label="Pending Buy" value={tradeBuyAmount} loading={loading} />
          <ProjectVintageAllocationItem
            label="Pending Sell"
            value={tradeSellAmount}
            tdSx={{ backgroundColor: "rgb(245, 245, 245)" }}
            loading={loading}
          />
        </Stack>
      </Stack>
    </Box>
  );
};

export default ProjectVintageAllocation;
