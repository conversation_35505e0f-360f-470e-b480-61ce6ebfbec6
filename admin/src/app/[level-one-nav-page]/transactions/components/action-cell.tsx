import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { TransactionStatus } from "@constants/transaction-status";
import { AuthContext } from "@providers/auth-provider";
import useNavigation from "@hooks/use-navigation";
import { ModeEditRounded } from "@mui/icons-material";
import { Tooltip, IconButton, Box } from "@mui/material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { useContext, useMemo, type JSX } from "react";
import { RowOnlyProps } from "../types/columns";
import { AllTransactionType, TransactionModel } from "@models/transaction";

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useContext(AuthContext);
  const { pushToPath } = useNavigation();
  const { disabled } = useGenericTableRowState<TransactionModel>(row);

  const isSale = useMemo(() => row?.type === AllTransactionType.PURCHASE, [row?.type]);

  const hasPermissionToEdit = user?.hasSomePermissions([
    PermissionEnum.TRADES_UPDATE,
    PermissionEnum.TRADES_UPDATE_AMOUNTS,
  ]);

  return (
    <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : ""}>
      <Box>
        <IconButton
          color="primary"
          disabled={
            !hasPermissionToEdit ||
            disabled ||
            [TransactionStatus.CANCELED, TransactionStatus.SETTLED].includes(row?.currentStatus)
          }
          onClick={() => pushToPath(`/${row?.id}/edit?type=${isSale ? "sale" : "trade"}`)}
        >
          <ModeEditRounded />
        </IconButton>
      </Box>
    </Tooltip>
  );
};

export default ActionsCell;
