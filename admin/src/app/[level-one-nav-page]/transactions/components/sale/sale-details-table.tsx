import GenericTable from "@components/ui/generic-table";
import { uuid } from "@rubiconcarbon/shared-types";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { FileDownloadRounded } from "@mui/icons-material";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { SALE_DETAIL_COLUMNS } from "../../constants/sale-detail-columns";
import { AssetOrder } from "@models/transaction";
import { SaleDetailModel } from "../../models/sale-details";
import { getProductNameFromAssetResponse } from "@utils/helpers/product/get-product-name";

import type { JSX } from "react";

type SaleDetailTableProps = {
  assets: AssetOrder[];
};

const SaleDetailTable = ({ assets }: SaleDetailTableProps): JSX.Element => {
  const { table } = useGenericTableUtility<SaleDetailModel>({});

  const toRowModel = (row: AssetOrder): GenericTableRowModel<SaleDetailModel> => {
    const x = {
      id: uuid(),
      ...row,
      productF: getProductNameFromAssetResponse([row as any]),
      bookF: row?.source?.name,
    };

    return x;
  };

  return (
    <GenericTable
      id="Sale Details"
      columns={SALE_DETAIL_COLUMNS}
      toRowModel={toRowModel}
      pageableData={{
        data: assets,
        page: {
          offset: 0,
          limit: assets?.length,
          size: assets?.length,
        },
      }}
      globalSearch={{
        searchKeys: ["productF", "bookF", "unitPrice", "amount", "rawPrice"],
      }}
      export={{
        filename: `Sale-Details-${new Date()}`,
        setClientCanExport: table?.setClientCanExport,
        bindClientExport: table?.bindClientExport,
      }}
      toolbarActionButtons={[
        {
          children: "Export",
          startIcon: <FileDownloadRounded />,
          style: DEFAULT_EXPORT_STYLE,
          isDisabled: !assets?.length,
          onClickHandler: table?.handleClientExport,
        },
      ]}
    />
  );
};

export default SaleDetailTable;
