import InteractiveProgress from "@components/ui/interactive-progress";
import { Box, Divider, Stack, Typography } from "@mui/material";
import { adjustShade } from "@rubiconcarbon/frontend-shared";
import { PropsWithChildren, type JSX } from "react";
import { useMeasure } from "react-use";

type StatusLabelProps = {
  width?: number | "100%" | "fit-content";
  height?: number | "100%";
};

const StatusLabel = ({ width = "100%", height = 22, children }: PropsWithChildren<StatusLabelProps>): JSX.Element => (
  <Typography component="span" variant="body2" sx={{ width, height, backgroundColor: "white", padding: "0 4px" }}>
    {children}
  </Typography>
);

const StatusColumnLabel = (): JSX.Element => {
  const [ref, { width }] = useMeasure();

  return (
    <Stack ref={ref} component={Box} direction="row" alignItems="center">
      <InteractiveProgress
        length={width}
        separator={0}
        baseStepSize={22}
        steps={[
          {
            value: 1.5,
            node: <StatusLabel width={35}>Firm</StatusLabel>,
          },
          {
            value: 20,
            node: <StatusLabel width={55}>Binding</StatusLabel>,
          },
          {
            value: 40,
            node: <StatusLabel width={60}>Executed</StatusLabel>,
          },
          {
            value: 60,
            node: <StatusLabel width={130}>Payment/Delivered</StatusLabel>,
          },
          {
            value: 94,
            node: <StatusLabel width={60}>Settled</StatusLabel>,
          },
        ]}
        connectorColor={adjustShade("gray", 100)}
      />
      <Divider sx={{ width: 10, height: 24, m: 1 }} orientation="vertical" />
      <StatusLabel width="fit-content">Canceled</StatusLabel>
    </Stack>
  );
};

export default StatusColumnLabel;
