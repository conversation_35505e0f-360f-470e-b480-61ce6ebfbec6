import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Stack, Typography } from "@mui/material";
import { AdminTradeConfirmationRequest, AdminTradeConfirmationResponse, uuid } from "@rubiconcarbon/shared-types";
import { useMemo, useState, type JSX } from "react";
import AutomatedEmailConfirmation from "./automated-email-confirmation";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import usePerformantEffect from "@hooks/use-performant-effect";
import { useDebounce, usePrevious } from "react-use";

type AutomatedEmailSelectionConfirmationProps = {
  id: uuid;
  emails?: string[];
  action: (...args: any[]) => void;
};

const AutomatedEmailSelectionConfirmation = ({
  id,
  emails = [],
  action,
}: AutomatedEmailSelectionConfirmationProps): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const [debounceMs, setDebouceMs] = useState<number>(0);
  const [deliveryTerms, updateDeliveryTerms] = useState<string>("");

  const previousDeliveryTerms = usePrevious(deliveryTerms);

  const isMultipleEmails = useMemo(() => emails?.length > 1, [emails?.length]);

  const {
    data: confirmation,
    isMutating: generating,
    error: generateDraftError,
    trigger: generateDraft,
  } = useTriggerRequest<AdminTradeConfirmationResponse, AdminTradeConfirmationRequest, { id: uuid }>({
    method: "post",
    url: "admin/trades/{id}/confirmation-draft",
    pathParams: { id },
    requestBody: {
      recipientEmails: emails,
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to generate confirmation draft.");
        logger.error(`Unable to generate confirmation drafts: ${error?.message}`, {});
      },
    },
  });

  useDebounce(
    async () => {
      if (!generating && !generateDraftError && deliveryTerms !== previousDeliveryTerms) {
        await generateDraft({
          requestBody: {
            deliveryTerms,
            recipientEmails: emails,
          },
        });
      }

      if (!debounceMs) setDebouceMs(500);
    },
    debounceMs,
    [deliveryTerms],
  );

  usePerformantEffect(() => {
    action({
      deliveryTerms: confirmation?.deliveryTerms,
      paymentTerms: confirmation?.paymentTerms,
      recipientEmails: confirmation?.recipientEmails,
    });
  }, [confirmation]);

  return (
    <Stack gap={2}>
      <Typography variant="body2" fontWeight={100} color="black">
        Please review and confirm the email details and receipient{isMultipleEmails ? "(s)" : ""}.
      </Typography>
      <AutomatedEmailConfirmation
        loading={!confirmation && generating}
        confirmation={confirmation}
        sx={{
          ">.section-override": {
            paddingLeft: "10px",
          },
        }}
        updateDeliveryTerms={updateDeliveryTerms}
      />
    </Stack>
  );
};

export default AutomatedEmailSelectionConfirmation;
