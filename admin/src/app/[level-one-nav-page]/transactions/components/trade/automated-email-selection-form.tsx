import MatIcon from "@components/ui/mat-icon/mat-icon";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import usePerformantEffect from "@hooks/use-performant-effect";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControlLabel,
  IconButton,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { Maybe, Nullable, useRequest, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { AdminOrganizationResponse, AdminOrganizationCreateRequest, uuid } from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { ValidateNested, IsNotEmpty, IsEmail } from "class-validator";
import { useEffect, useMemo, useRef, useState, type JSX } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import Awaited from "@components/ui/await/components/awaited";

import classes from "../../styles/automated-email-selection-form.module.scss";

class Email {
  constructor() {}

  @IsEmail({}, { message: "Must be in email format" })
  @IsNotEmpty({ message: "Required" })
  value: string;

  source?: "client" | "server";

  checked?: boolean;
}

class EmailListModel {
  constructor() {}

  @ValidateNested()
  @Type(() => Email)
  list: Email[];
}

type AutomatedEmailSelectionFormProps = {
  organizationId: uuid;
  previouslySelectedEmails: string[];
  action: (...args: any[]) => void;
};

const EmailListModelResolver = classValidatorResolver(EmailListModel);

const AutomatedEmailSelectionForm = ({
  organizationId,
  previouslySelectedEmails,
  action,
}: AutomatedEmailSelectionFormProps): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const checkedEmails = useRef<Record<string, boolean>>({});
  const unsavedClientEmails = useRef<Email[]>([]);
  const [patchingIndex, setPatchingIndex] = useState<Nullable<number>>();

  const {
    data,
    isLoading: loading,
    error: counterpartyDataError,
    mutate: getCounterpartyData,
  } = useRequest<AdminOrganizationResponse>({
    url: `/admin/organizations/${organizationId}`,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to load counterparty emails.");
        logger.error(`Unable to load counterparty emails: ${error?.message}`, {});
      },
    },
  });

  const { trigger: patchCounterparty, isMutating: patching } = useTriggerRequest<
    { data: AdminOrganizationResponse },
    AdminOrganizationCreateRequest
  >({
    url: `/admin/organizations/${organizationId}`,
    method: "patch",
    swrOptions: {
      onSuccess: async (): Promise<void> => {
        setPatchingIndex(null);
        enqueueSuccess("Successfully added counterparty email.");
      },
      onError: (error: any) => {
        enqueueError("Unable to update counterparty emails.");
        logger.error(`Unable to update counterparty emails: ${error?.message}`, {});
      },
    },
  });

  const baseEmailList = useMemo(
    () =>
      (data?.counterparty?.tradeConfirmEmails ?? [])?.map((email) => ({
        value: email,
        source: "server",
        checked: checkedEmails?.current?.[email?.toLocaleLowerCase()] || previouslySelectedEmails?.includes(email),
      })) as Email[],
    [data?.counterparty?.tradeConfirmEmails, previouslySelectedEmails],
  );

  const { control, formState, watch, reset, trigger } = useForm<EmailListModel>({
    mode: "onBlur",
    resolver: EmailListModelResolver,
  });

  const { errors } = formState;

  const {
    fields: emailList,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "list",
  });

  const watched = watch();

  const serverEmails = useMemo(() => emailList?.filter(({ source }) => source === "server"), [emailList]);
  const clientEmails = useMemo(() => emailList?.filter(({ source }) => source === "client"), [emailList]);
  const selectedEmails = useMemo(
    () => watched?.list?.filter(({ checked }) => !!checked)?.map(({ value }) => value),
    [watched],
  );
  const hasServerEmails = useMemo(() => !!serverEmails?.length, [serverEmails?.length]);
  const hasClientEmails = useMemo(() => !!clientEmails?.length, [clientEmails?.length]);

  usePerformantEffect(() => {
    let timeoutId: Nullable<NodeJS.Timeout>;
    if (!loading && !counterpartyDataError && !data) {
      timeoutId = setTimeout(async () => await getCounterpartyData());
    }

    if (timeoutId) return clearTimeout(timeoutId);
  }, [loading, counterpartyDataError, data]);

  useEffect(() => {
    reset({ list: [...baseEmailList, ...unsavedClientEmails.current] });
  }, [baseEmailList, reset]);

  useEffect(() => {
    const updated = watched?.list?.reduce(
      (record, { value, checked }) => {
        if (checked)
          record = {
            ...record,
            [value?.toLocaleLowerCase()]: checked,
          };
        return record;
      },
      {} as Record<string, boolean>,
    );
    checkedEmails.current = updated;
  }, [watched]);

  usePerformantEffect(() => {
    if (!hasClientEmails) action(selectedEmails);
    else action([]);
  }, [hasClientEmails, selectedEmails]);

  const addLineItem = (): void => {
    const email = new Email();
    email.source = "client";
    append(email);
  };

  const handleSubmit = async (index: number, value: string): Promise<void> => {
    if (await trigger(`list.${index}.value`, { shouldFocus: true })) {
      const tradeConfirmEmails = [...baseEmailList.map((email) => email.value), value];

      unsavedClientEmails.current = watched?.list?.filter(
        ({ value: v, source }, i) => source === "client" && v !== value && i + serverEmails?.length !== index,
      );

      setPatchingIndex(index);
      await patchCounterparty({ requestBody: { counterparty: { tradeConfirmEmails } } as any });
    }
  };

  return (
    <Stack gap={2}>
      <Maybe condition={hasServerEmails}>
        <Typography variant="body2" fontWeight={100} color="black">
          Choose email address(es) to send or add a new one.
        </Typography>
      </Maybe>
      <Stack gap={1}>
        <Maybe
          condition={!loading}
          fallback={<Awaited variant="rounded" repeat={2} sx={{ gap: 1 }} itemSx={{ height: 46 }} />}
        >
          {serverEmails?.map(({ id, value }, index) => (
            <Controller
              key={id}
              control={control}
              name={`list.${index}.checked`}
              render={({ field: { value: checked, ...otherProps } }) => (
                <FormControlLabel label={value} control={<Checkbox checked={checked} {...otherProps} />} />
              )}
            />
          ))}
        </Maybe>
        <Stack gap={2}>
          <Maybe condition={hasServerEmails}>
            <Divider />
          </Maybe>

          {clientEmails?.map(({ id }, index) => (
            <Controller
              key={id}
              name={`list.${index + serverEmails?.length}.value`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
                const _index = index + serverEmails?.length;
                const isPatchingIndex = patchingIndex === _index && patching;

                return (
                  <Stack direction="row" gap={0.5}>
                    <TextField
                      label={`Enter ${index > 0 ? "additional " : ""} email`}
                      value={value ?? ""}
                      InputProps={{
                        ref,
                        endAdornment: (
                          <Stack direction="row" gap={0.2}>
                            <Box sx={{ position: "relative" }}>
                              <IconButton
                                className={classes.IconButton}
                                disabled={loading || isPatchingIndex}
                                onClick={() => handleSubmit(_index, value)}
                              >
                                <MatIcon
                                  value="check"
                                  variant="round"
                                  color={isPatchingIndex ? "disabled" : "primary"}
                                />
                              </IconButton>
                              <Maybe condition={isPatchingIndex}>
                                <CircularProgress
                                  sx={{
                                    position: "absolute",
                                    top: 3,
                                    left: 3,
                                  }}
                                />
                              </Maybe>
                            </Box>
                            <Divider orientation="vertical" sx={{ height: 46 }} />
                            <IconButton
                              className={classes.IconButton}
                              disabled={loading || isPatchingIndex}
                              onClick={() => remove(_index)}
                            >
                              <MatIcon value="delete" variant="round" color={isPatchingIndex ? "disabled" : "action"} />
                            </IconButton>
                          </Stack>
                        ),
                        readOnly: isPatchingIndex,
                        classes: {
                          root: classes.TextInputRoot,
                        },
                      }}
                      {...otherProps}
                      error={!!errors?.list?.at?.(_index)?.value}
                      helperText={errors?.list?.at?.(_index)?.value?.message}
                      fullWidth
                    />
                  </Stack>
                );
              }}
            />
          ))}
          <Box display="flex" justifyContent={hasServerEmails || hasClientEmails ? "flex-end" : "center"}>
            <Button
              variant="text"
              startIcon={<MatIcon value="add_circle" variant="round" size={40} />}
              disabled={loading || patching}
              sx={{ padding: "20px 10px" }}
              onClick={addLineItem}
            >
              <Typography variant="body2" textTransform="none">
                {hasServerEmails || hasClientEmails ? "Add more" : "Click to add"} emails
              </Typography>
            </Button>
          </Box>
        </Stack>
      </Stack>
    </Stack>
  );
};

export default AutomatedEmailSelectionForm;
