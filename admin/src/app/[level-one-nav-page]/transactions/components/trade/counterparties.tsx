import { CounterpartyRoleOptions } from "@constants/counterparty-ui-role.enum";
import { TradeCounterparty, TransactionModel } from "@models/transaction";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import {
  Alert,
  Autocomplete,
  Box,
  Divider,
  Grid,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { AdminCounterpartyResponse, uuid } from "@rubiconcarbon/shared-types";
import {
  Controller,
  Control,
  FieldErrors,
  FieldArrayWithId,
  UseFormResetField,
  UseFormSetValue,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
} from "react-hook-form";
import { Maybe, NO_OP, px } from "@rubiconcarbon/frontend-shared";
import { Fragment, useCallback, useMemo, type JSX } from "react";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { useToggle } from "react-use";
import usePerformantEffect from "@hooks/use-performant-effect";
import { TableCellStatusChip } from "@components/ui/table-cell-status-chip/table-cell-status-chip";

import classes from "../../styles/counterparties.module.scss";

type LineItemProps = {
  index: number;
  data: FieldArrayWithId<TransactionModel, "trade.counterparties", "id">;
  options: UseAutoCompleteOptionsReturnEntry<any>[];
} & Pick<CounterpartiesProps, "control" | "errors" | "smartSetValue" | "resetField" | "remove" | "onDelete">;

type CounterpartyTableProps = {
  offset?: number; // where intermediaries should start indexing from. defaults to 0 for trade counterparties
  type: "trade" | "intermediaries";
  options: UseAutoCompleteOptionsReturnEntry<any>[];
  lineItems: FieldArrayWithId<TransactionModel, "trade.counterparties", "id">[];
} & Pick<CounterpartiesProps, "control" | "errors" | "smartSetValue" | "resetField" | "remove" | "onDelete">;

type CounterpartiesProps = {
  counterpartyOptions: (AdminCounterpartyResponse & { organizationId: uuid; name: string })[];
  control: Control<TransactionModel, any>;
  errors: FieldErrors<TransactionModel>;
  lineItems: FieldArrayWithId<TransactionModel, "trade.counterparties", "id">[];
  smartSetValue: UseFormSetValue<TransactionModel>;
  resetField: UseFormResetField<TransactionModel>;
  append: UseFieldArrayAppend<TransactionModel, "trade.counterparties">;
  remove: UseFieldArrayRemove;
  onDelete: () => void;
};

const LineItem = ({
  index,
  data,
  options,
  control,
  errors,
  smartSetValue,
  resetField,
  remove,
  onDelete,
}: LineItemProps): JSX.Element => {
  const serverComments = useMemo(() => data?.comments, [data?.comments]);

  const [hasCommentField, toggleCommentField] = useToggle(!!serverComments);

  usePerformantEffect(() => {
    if (!hasCommentField) smartSetValue(`trade.counterparties.${index}.comments`, undefined);
  }, [hasCommentField]);

  usePerformantEffect(() => {
    if (hasCommentField && !!data?.counterparty?.organizationId && !!serverComments)
      resetField(`trade.counterparties.${index}.comments`);
  }, [hasCommentField, data?.counterparty?.organizationId, serverComments]);

  const deleteEntry = (): void => {
    remove(index);
    onDelete();
  };

  return (
    <Grid className={classes.BodyRow} container item alignItems="center" gap={0.5}>
      <Grid container item xs={10.5} direction="column" gap={1}>
        <Grid container item xs={12} padding="0 5px" gap={2}>
          <Grid className={classes.BodyCell} item xs={7.4}>
            <Controller
              name={`trade.counterparties.${index}.counterparty.organizationId`}
              control={control}
              render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
                const selectedOption = options.find((entry) => entry?.value?.organizationId === value) ?? null;

                return (
                  <Autocomplete
                    id="counterparty-name"
                    options={options}
                    value={selectedOption}
                    onChange={(_, selection) => onChange(selection?.value?.organizationId)}
                    getOptionKey={(option: UseAutoCompleteOptionsReturnEntry) => option?.value?.organizationId}
                    getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry) => option?.label}
                    ListboxProps={{
                      style: {
                        maxHeight: 300,
                      },
                    }}
                    renderInput={({ InputProps, ...params }) => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...InputProps,
                          endAdornment: (
                            <Stack direction="row" alignItems="center">
                              <Maybe condition={!!selectedOption}>
                                <TableCellStatusChip
                                  data={{
                                    ...selectedOption?.value,
                                    onboardingStatusF: selectedOption?.value?.isOnboarded
                                      ? "Onboarded"
                                      : "Not Onboarded",
                                  }}
                                  path={{ value: "isOnboarded", label: "onboardingStatusF" }}
                                />
                              </Maybe>
                              {InputProps?.endAdornment}
                            </Stack>
                          ),
                        }}
                        label="Counterparty"
                        {...otherProps}
                        error={!!errors?.trade?.counterparties?.[index]?.counterparty?.organizationId}
                        helperText={errors?.trade?.counterparties?.[index]?.counterparty?.organizationId?.message}
                        fullWidth
                      />
                    )}
                    renderOption={(props, option) => (
                      <li
                        {...props}
                        key={option.value.id}
                        {...px(
                          {
                            className: option?.internal ? "" : null,
                            style: option?.internal ? {} : null,
                            onClick: option?.internal ? NO_OP : null,
                            onMouseMove: option?.internal ? NO_OP : null,
                            onTouchStart: option?.internal ? NO_OP : null,
                          },
                          [false, null, undefined],
                        )}
                      >
                        {option?.displayLabel || option?.label}
                      </li>
                    )}
                    fullWidth
                  />
                );
              }}
            />
          </Grid>
          <Grid className={classes.BodyCell} item xs={4.3}>
            <Controller
              name={`trade.counterparties.${index}.role`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  select
                  label="Counterparty Role"
                  value={value ?? ""}
                  InputProps={{
                    ref,
                  }}
                  SelectProps={{
                    MenuProps: {
                      style: {
                        maxHeight: 300,
                      },
                    },
                  }}
                  {...otherProps}
                  error={!!errors?.trade?.counterparties?.[index]?.role}
                  helperText={errors?.trade?.counterparties?.[index]?.role?.message}
                  fullWidth
                >
                  {CounterpartyRoleOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        </Grid>
        <Grid container item xs={12} padding="0 5px" justifyContent={`flex-${hasCommentField ? "start" : "end"}`}>
          <Maybe condition={hasCommentField}>
            <Controller
              name={`trade.counterparties.${index}.comments`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
                return (
                  <TextField
                    label="Comments"
                    value={value ?? ""}
                    InputProps={{ ref }}
                    minRows={2}
                    multiline
                    {...otherProps}
                    fullWidth
                  />
                );
              }}
            />
          </Maybe>
        </Grid>
      </Grid>
      <Grid
        className={classes.BodyCell}
        container
        item
        xs={1}
        paddingTop="8px"
        height="100%"
        alignItems="start"
        justifyContent={data?.isPrimary ? "center" : "start"}
      >
        <Stack direction="row" gap={0.5}>
          <Maybe condition={!data?.isPrimary}>
            <Tooltip title="Delete entire entry">
              <IconButton onClick={deleteEntry}>
                <MatIcon value="delete" variant="round" color="action" />
              </IconButton>
            </Tooltip>
            <Divider orientation="vertical" sx={{ height: 46 }} />
          </Maybe>
          <Tooltip title={`${hasCommentField ? "Remove" : "Add"} comment field`}>
            <IconButton onClick={() => toggleCommentField()}>
              <MatIcon
                value={hasCommentField ? "comments_disabled" : "insert_comment"}
                variant="round"
                color="action"
              />
            </IconButton>
          </Tooltip>
        </Stack>
      </Grid>
    </Grid>
  );
};

const CounterpartyTable = ({
  offset = 0,
  type,
  options,
  lineItems,
  control,
  errors,
  smartSetValue,
  remove,
  resetField,
  onDelete,
}: CounterpartyTableProps): JSX.Element => {
  return (
    <Grid className={classes.Table} container>
      <Grid className={classes.Header} container item gap={0.5}>
        <Grid className={classes.HeaderCell} item xs={6.6}>
          <Typography variant="body2">Counterparty</Typography>
        </Grid>
        <Grid className={classes.HeaderCell} item xs={4}>
          <Typography variant="body2">Role</Typography>
        </Grid>
        <Grid className={classes.HeaderCell} item xs={1}>
          <Typography variant="body2">Actions</Typography>
        </Grid>
      </Grid>

      <Grid className={classes.Body} container item justifyContent={"center"} gap={1}>
        <Maybe condition={!!lineItems?.length}>
          {lineItems?.map((lineItem, index) => (
            <Fragment key={lineItem?.id}>
              <LineItem
                index={index + offset}
                data={lineItem}
                options={options}
                control={control}
                errors={errors}
                smartSetValue={smartSetValue}
                remove={remove}
                resetField={resetField}
                onDelete={onDelete}
              />
              <Maybe condition={index !== lineItems?.length - 1}>
                <Divider sx={{ width: "100%" }} />
              </Maybe>
            </Fragment>
          ))}
        </Maybe>
        <Maybe condition={!lineItems?.length}>
          <Alert
            classes={{
              root: `${classes.Alert}${type === "trade" && !!errors?.trade?.counterparties ? ` ${classes.Error}` : ""}`,
              icon: classes.Icon,
              message: classes.Message,
            }}
            severity={type === "trade" && !!errors?.trade?.counterparties ? "error" : "info"}
          >
            <Typography>
              {type === "trade" ? "Please" : "You may"} add a{" "}
              {type === "trade" ? "trade counterparty" : "trade intermediary"} entry
            </Typography>
          </Alert>
        </Maybe>
      </Grid>
    </Grid>
  );
};

const Counterparties = ({
  counterpartyOptions = [],
  control,
  errors,
  lineItems,
  smartSetValue,
  resetField,
  append,
  remove,
  onDelete,
}: CounterpartiesProps): JSX.Element => {
  const tradeCounterparties = lineItems?.filter(({ isPrimary }) => isPrimary);
  const intermediaries = lineItems?.filter(({ isPrimary }) => !isPrimary);

  const options = useAutoCompleteOptions<AdminCounterpartyResponse & { organizationId: uuid; name: string }>({
    data: counterpartyOptions,
    keys: ["organizationId", "name", "isEnabled", "isOnboarded"],
    label: (entry) => entry?.name,
    displayLabel: (entry) => (
      <Stack direction="row" justifyContent="space-between" width="100%">
        <Typography>{entry?.name}</Typography>
        <TableCellStatusChip
          data={
            {
              ...entry,
              onboardingStatusF: entry.isOnboarded ? "Onboarded" : "Not Onboarded",
            } as any
          }
          path={{ value: "isOnboarded", label: "onboardingStatusF" }}
        />
      </Stack>
    ),
    value: (entry) => entry,
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  const addNewLineItem = useCallback((): void => {
    append(new TradeCounterparty());
    if (errors?.trade?.counterparties) resetField("trade.counterparties", { keepDirty: false, keepError: false });
  }, [append, errors?.trade?.counterparties, resetField]);

  return (
    <Stack gap={2}>
      <Stack gap={1}>
        <Typography className={classes.SubHeader} variant="body2">
          Trade Counterparty
        </Typography>
        <CounterpartyTable
          type="trade"
          lineItems={tradeCounterparties}
          options={options}
          control={control}
          errors={errors}
          smartSetValue={smartSetValue}
          remove={remove}
          resetField={resetField}
          onDelete={onDelete}
        />
      </Stack>

      <Stack gap={1}>
        <Typography className={classes.SubHeader} variant="body2">
          Trade Intermediaries
        </Typography>
        <CounterpartyTable
          offset={tradeCounterparties?.length}
          type="intermediaries"
          lineItems={intermediaries}
          options={options}
          control={control}
          errors={errors}
          smartSetValue={smartSetValue}
          remove={remove}
          resetField={resetField}
          onDelete={onDelete}
        />
      </Stack>

      <Box display="flex" justifyContent="flex-end">
        <IconButton color="primary" onClick={addNewLineItem}>
          <MatIcon value="add_circle" variant="round" size={34} />
        </IconButton>
      </Box>
    </Stack>
  );
};

export default Counterparties;
