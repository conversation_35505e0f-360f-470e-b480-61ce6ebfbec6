import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { <PERSON>, Divider, Grid, Stack, TextField, Toolt<PERSON>, Typography } from "@mui/material";
import { classcat, currencyFormat, isNothing, Maybe, toNumber } from "@rubiconcarbon/frontend-shared";
import { Fragment, useMemo, useState, type JSX } from "react";
import { AllTransactionType, TradeCounterparty, TransactionModel } from "@models/transaction";
import { CounterpartyFeeType, AdminCounterpartyResponse, TradeType, uuid } from "@rubiconcarbon/shared-types";
import { CounterpartyRoleToLabel } from "@constants/counterparty-ui-role.enum";
import { NumericFormat } from "react-number-format";
import { Control, Controller, FieldErrors } from "react-hook-form";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { MISSING_DATA } from "@constants/constants";
import usePerformantEffect from "@hooks/use-performant-effect";
import { FeeTypeUILabel } from "@constants/fee-type";
import { calculateFees } from "../../utilities/calculate-fees";

import classes from "../../styles/thirdparty-fee-confirmation.module.scss";
import dialogClasses from "../../styles/dialog.module.scss";

type ThirdPartyFeeConfirmationModalProps = {
  open: boolean;
  control: Control<TransactionModel, any>;
  errors: FieldErrors<TransactionModel>;
  transaction: TransactionModel;
  possibleCounterparties: (AdminCounterpartyResponse & { organizationId: uuid; name: string })[];
  onApprove: (serviceFee: string, counterparties: TradeCounterparty[]) => void;
  onClose: (counterparties: TradeCounterparty[]) => void;
};

const ThirdPartyFeeConfirmationModal = ({
  open,
  control,
  errors,
  transaction,
  possibleCounterparties,
  onApprove,
  onClose,
}: ThirdPartyFeeConfirmationModalProps): JSX.Element => {
  const [initialState, setInitialState] = useState<TradeCounterparty[]>([]);

  const counterparties = useMemo(
    () =>
      transaction?.trade?.counterparties
        ?.filter(({ counterparty }) => !!counterparty?.organizationId)
        ?.map(({ counterparty: trimmedCounterparty, ...rest }) => {
          const counterparty = possibleCounterparties?.find(
            (cp) => cp.organizationId === trimmedCounterparty.organizationId,
          );

          return {
            counterparty,
            ...rest,
            calculatedServiceFee: calculateFees(transaction, counterparty),
          } as TradeCounterparty;
        }),
    [possibleCounterparties, transaction],
  );

  const hasError = useMemo(() => {
    return errors?.trade?.counterparties?.some(({ actualServiceFee }) => !!actualServiceFee);
  }, [errors?.trade?.counterparties]);

  const hasNonFilledFees = useMemo(() => {
    return counterparties?.some(({ actualServiceFee }) => !actualServiceFee);
  }, [counterparties]);

  const total = useMemo(() => {
    return counterparties?.reduce(
      (sum, { actualServiceFee }) => sum + toNumber(actualServiceFee, { parserBlacklist: [" ", "$", ","] }),
      0,
    );
  }, [counterparties]);

  usePerformantEffect(() => {
    if (open) {
      setInitialState([...counterparties]);
    }
  }, [open]);

  return (
    <GenericDialog
      open={open}
      title="Fee Breakdown & Confirmation"
      positiveAction={{
        buttonText: "APPROVE",
        disabled: hasNonFilledFees,
      }}
      negativeAction={{
        buttonText: "CANCEL",
      }}
      onClose={() => {
        onClose(initialState);
      }}
      onPositiveClick={() => {
        if (!hasError) onApprove(total?.toString(), counterparties);
      }}
      onNegativeClick={() => {
        onClose(initialState);
      }}
      classes={{
        root: classcat([dialogClasses.Dialog, dialogClasses.FeeConfirmation]),
        title: `${dialogClasses.Title} ${dialogClasses.ClearTitle}`,
        actions: dialogClasses.Actions,
      }}
    >
      <Stack className={classes.ThirdPartyFeeConfirmation} rowGap={1}>
        <Typography className={classes.Header} component={Stack} direction="row" justifyContent="space-between">
          <Typography>Order {transaction?.type === AllTransactionType.BUY ? "Subtotal" : "Gross Revenue"}</Typography>
          <Typography>{currencyFormat(transaction?.orders?.at(0)?.rawPrice)}</Typography>
        </Typography>
        <Grid className={classes.Table} container>
          <Grid className={classes.Header} container item gap={0.5}>
            <Grid className={classes.HeaderCell} item xs={3.5}>
              <Typography variant="body2">Trade Counterparty</Typography>
            </Grid>
            <Grid className={classes.HeaderCell} item xs={2}>
              <Typography variant="body2">Type</Typography>
            </Grid>
            <Grid className={classes.HeaderCell} item xs={2}>
              <Typography variant="body2">Estimated Fee</Typography>
            </Grid>
            <Grid className={classes.HeaderCell} item xs={4}>
              <Typography variant="body2">Actual Fee</Typography>
            </Grid>
          </Grid>
          <Grid className={classes.Body} container item justifyContent={"center"} gap={1}>
            <Maybe condition={!!counterparties?.length}>
              {counterparties?.map(({ role, calculatedServiceFee, counterparty }, index) => {
                return (
                  <Fragment key={`${counterparty?.organizationId}-${index}`}>
                    <Grid className={classes.BodyRow} container item alignItems="baseline" gap={0.5}>
                      <Grid className={classcat([classes.BodyCell, classes.NonTextField])} item xs={3.5}>
                        <Typography variant="body2">{counterparty?.name}</Typography>
                      </Grid>
                      <Grid className={classcat([classes.BodyCell, classes.NonTextField])} item xs={2}>
                        <Typography variant="body2">{CounterpartyRoleToLabel[role]}</Typography>
                      </Grid>
                      <Grid
                        className={classcat([classes.BodyCell, classes.NonTextField])}
                        container
                        item
                        xs={2}
                        gap={1}
                        justifyContent="space-between"
                      >
                        <Maybe condition={!isNothing(calculatedServiceFee)}>
                          <Typography variant="body2">{currencyFormat(calculatedServiceFee)}</Typography>
                        </Maybe>
                        <Maybe condition={isNothing(calculatedServiceFee)}>
                          <Typography variant="body2">?</Typography>
                        </Maybe>
                        <Maybe condition={isNothing(calculatedServiceFee)}>
                          <Tooltip
                            className={classes.Tooltip}
                            title="No fee schedule associated with this counterparty"
                          >
                            <Box alignSelf="center" width={20} height={20}>
                              <MatIcon value="error" color="error" size={20} />
                            </Box>
                          </Tooltip>
                        </Maybe>
                        <Maybe condition={!isNothing(calculatedServiceFee)}>
                          {() => {
                            const feeType = counterparty?.defaultFees?.find(
                              (fee) => fee.type === (transaction?.type as unknown as TradeType),
                            )?.feeType;

                            return (
                              <Tooltip
                                className={classes.Tooltip}
                                title={`Using "${FeeTypeUILabel[feeType]}" fee schedule`}
                              >
                                <Box
                                  alignSelf="center"
                                  width={18}
                                  height={18}
                                  sx={{ backgroundColor: "#757575", borderRadius: "50%" }}
                                >
                                  <MatIcon
                                    value={
                                      feeType === CounterpartyFeeType.PERCENTAGE
                                        ? "percent"
                                        : feeType === CounterpartyFeeType.PER_TONNE
                                          ? "currency_exchange"
                                          : "attach_money"
                                    }
                                    size={14}
                                    sx={{
                                      margin: "2px",
                                      color: "white",
                                    }}
                                  />
                                </Box>
                              </Tooltip>
                            );
                          }}
                        </Maybe>
                      </Grid>
                      <Grid className={classes.BodyCell} item xs={4}>
                        <Controller
                          control={control}
                          name={`trade.counterparties.${index}.actualServiceFee`}
                          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                            <NumericFormat
                              allowNegative={false}
                              thousandSeparator
                              decimalScale={2}
                              fixedDecimalScale
                              prefix="$ "
                              label="Fee"
                              value={value}
                              customInput={TextField}
                              InputProps={{
                                ref,
                              }}
                              error={!!errors?.trade?.counterparties?.at(index)?.actualServiceFee}
                              helperText={errors?.trade?.counterparties?.at(index)?.actualServiceFee?.message}
                              {...otherProps}
                              fullWidth
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                    <Maybe condition={index !== counterparties?.length - 1}>
                      <Divider sx={{ width: "100%" }} />
                    </Maybe>
                  </Fragment>
                );
              })}
            </Maybe>
          </Grid>
          <Maybe condition={!!counterparties?.length}>
            <Grid className={classes.Footer} container item justifyContent={"center"} gap={0.5}>
              <Divider sx={{ width: "100%" }} />
              <Grid className={classes.FooterRow} container item alignItems="center" gap={1} justifyContent="flex-end">
                <Grid item xs={6} />
                <Grid className={classes.FooterCell} container item xs={4.7} alignItems="center">
                  <Typography className={classes.Label} variant="body2">
                    Total:
                  </Typography>
                  <Typography className={classes.Value} variant="body2">
                    {currencyFormat(total, { fallback: MISSING_DATA, symbol: "$ " as any })}
                  </Typography>
                </Grid>
                <Grid item xs={0.3} />
              </Grid>
            </Grid>
          </Maybe>
        </Grid>
      </Stack>
    </GenericDialog>
  );
};

export default ThirdPartyFeeConfirmationModal;
