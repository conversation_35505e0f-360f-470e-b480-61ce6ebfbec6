import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import ProductName from "@components/ui/product-name/product-name";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { SaleDetailModel } from "../models/sale-details";

export const SALE_DETAIL_COLUMNS: GenericTableColumn<SaleDetailModel>[] = [
  {
    field: "productF",
    label: "Product",
    width: GenericTableFieldSizeEnum.flexmedium,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
    renderDataCell: (asset: SaleDetailModel) => <ProductName assets={[asset.asset as any]} style={{ fontSize: 14 }} />,
  },
  {
    field: "bookF",
    label: "Book",
    width: GenericTableFieldSizeEnum.flexsmall,
    maxWidth: GenericTableFieldSizeEnum.medium,
  },
  {
    field: "unitPrice",
    label: "Unit Price",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "rawPrice",
    label: "Total Price",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
];
