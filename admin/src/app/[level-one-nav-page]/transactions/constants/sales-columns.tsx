import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import ProductName from "@components/ui/product-name/product-name";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import StatusColumnLabel from "../components/status-column-label";
import StatusColumnField from "../components/status-column-field";
import { TransactionStatus, TransactionStatusToLabel } from "@constants/transaction-status";
import Link from "next/link";
import COLORS from "@components/ui/theme/colors";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import GenericTableDocumentsButton from "@components/ui/generic-table/components/generic-row-document-button";
import OrganizationName from "@components/ui/organization-name/organization-name";
import { TransactionModel } from "@models/transaction";
import { useCallback, type JSX } from "react";
import { IconButton, Typography } from "@mui/material";
import PDFIcon from "@components/icons/pdf-icon";
import useFileDownloader, { DownloadResult } from "@hooks/use-file-downloader";
import useSnackbarVariants from "@hooks/use-enqueue-variant";

import statusClasses from "../styles/status-column-label.module.scss";

const ActionsCell = ({ row }: { row: GenericTableRowModel<TransactionModel> }): JSX.Element => {
  const { download } = useFileDownloader();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const downloadPDFHandler = useCallback(async () => {
    const result: DownloadResult = await download(
      `Customer_Sale_${row?.uiKey}`,
      `reporting/pdf/customer-sale?id=${row?.id}`,
    );
    if (result?.isSuccess) {
      enqueueSuccess("Customer sale exported successfully");
    } else {
      enqueueError("Unable to download customer sale");
    }
  }, [download, row?.uiKey, row?.id, enqueueSuccess, enqueueError]);

  return (
    <IconButton sx={{ color: COLORS.rubiconGreen, width: 36, height: 36 }} onClick={downloadPDFHandler}>
      <PDFIcon />
    </IconButton>
  );
};

export const SALE_COLUMNS: GenericTableColumn<TransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TransactionModel>) => (
      <Link
        href={`/trading/transactions/${row?.id}?type=${row?.type}`}
        style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}
      >
        <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "sale.organization.name",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TransactionModel): JSX.Element => (
      <OrganizationName organization={row?.sale?.organization} style={{ fontSize: 14, fontWeight: 300 }} />
    ),
  },
  {
    field: "product" as any,
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row) => <ProductName assets={row?.orders?.map(({ asset }) => asset)} style={{ fontSize: 14 }} />,
  },
  {
    field: "documents" as any,
    label: "Documents",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    exportable: false,
    sortable: false,
    renderDataCell: (row): JSX.Element => <GenericTableDocumentsButton row={row} />,
  },
  {
    field: "sale.totalValue",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "status",
    label: "Status",
    displayLabel: <StatusColumnLabel />,
    type: "custom",
    exportable: true,
    width: 650,
    maxWidth: 750,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => {
      const status = row?.currentStatus;

      if (status === TransactionStatus.EXECUTED)
        return `${row?.isDelivered ? "Delivered" : row?.isPaid ? "Paid" : TransactionStatusToLabel[status]}`;
      return TransactionStatusToLabel[status] || "";
    },
    renderDataCell: (row) => <StatusColumnField row={row} />,
  },
  {
    field: "updatedAt",
    label: "Updated Date",
    type: "date",
    width: 130,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
