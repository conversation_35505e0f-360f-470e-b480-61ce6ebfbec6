import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { AccessTimeFilledRounded } from "@mui/icons-material";
import { Box, Chip, Stack, Tooltip, Typography } from "@mui/material";
import { BookType, TradeType } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import Link from "next/link";
import COLORS from "@components/ui/theme/colors";
import GenericTableFormErrorHelperText from "@components/ui/generic-table/components/generic-table-form-error-helper-text";
import StatusColumnLabel from "../components/status-column-label";
import StatusColumnField from "../components/status-column-field";
import { TransactionStatus, TransactionStatusToLabel } from "@constants/transaction-status";
import ProjectName from "@components/ui/project-name/project-name";
import { CSSProperties, type JSX } from "react";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import { TIFEnum } from "@constants/tif.enum";
import GenericTableDocumentsButton from "@components/ui/generic-table/components/generic-row-document-button";
import { projectIsRCTApproved } from "@utils/helpers/project/project-is-rct-approved";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import OrganizationName from "@components/ui/organization-name/organization-name";
import ActionsCell from "../components/action-cell";

import statusClasses from "../styles/status-column-label.module.scss";

type RowOnlyProps = {
  row: GenericTableRowModel<TransactionModel>;
};

export const ProjectCell = ({ row, style = {} }: RowOnlyProps & { style?: CSSProperties }): JSX.Element => {
  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center" gap={1}>
        <Stack gap={1}>
          <ProjectName
            assets={row?.orders?.map(({ asset }) => asset)}
            addTags={{
              rct: false,
              suspended: true,
            }}
            style={style}
          />
        </Stack>
        <Maybe condition={!!row?.trade?.goodUntilDate && row?.status === TransactionStatus.INDICATIVE}>
          <Chip
            icon={<AccessTimeFilledRounded fontSize="small" sx={{ fill: "#E86427" }} />}
            label="EOD"
            sx={{ bgcolor: "transparent", border: "solid #E86427 1px", color: "#E86427" }}
          />
        </Maybe>
      </Stack>
    </>
  );
};

// might move it out if this representation of Book is used elsewhere
export const BookCell = ({ row }: RowOnlyProps): JSX.Element => {
  const requiresApproval =
    row?.type === AllTransactionType.BUY && row?.orders?.at?.(0)?.source?.type === BookType.PORTFOLIO_DEFAULT;
  const approved = requiresApproval && projectIsRCTApproved(row?.orders?.at?.(0)?.asset);

  return (
    <Tooltip
      title={requiresApproval && !approved ? "Project Not RCT Approved" : null}
      sx={{ cursor: requiresApproval && !approved ? "default" : "initial" }}
    >
      <Stack gap={1} direction="row">
        <Typography fontSize={14} fontWeight={300}>
          {row?.orders?.at?.(0)?.source?.name}
        </Typography>
        <Maybe condition={requiresApproval && !approved}>
          <Box>
            <MatIcon value="warning" variant="round" size={22} sx={{ color: "#E53935" }} />
          </Box>
        </Maybe>
      </Stack>
    </Tooltip>
  );
};

export const TRADE_COLUMNS: GenericTableColumn<TransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TransactionModel>) => (
      <Link
        href={`/trading/transactions/${row?.id}?type=${row?.type}`}
        style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}
      >
        <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "trade.counterparties.0.counterparty.name",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.large,
    renderDataCell: (row: TransactionModel): JSX.Element => (
      <OrganizationName
        organization={
          {
            name: row?.trade?.counterparties?.at(0)?.counterparty?.name,
            id: row?.trade?.counterparties?.at(0)?.counterparty?.organizationId,
          } as any
        }
        style={{ fontSize: 14, fontWeight: 300 }}
      />
    ),
  },
  {
    field: "product",
    label: "Product",
    type: "async-autocomplete",
    width: GenericTableFieldSizeEnum.xlarge,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row) => <ProjectCell row={row} style={{ fontSize: 14 }} />,
  },
  {
    field: "orders.0.unitPrice",
    label: "Price",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "orders.0.source.name",
    label: "Book",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
    renderDataCell: (row): JSX.Element => <BookCell row={row} />,
  },
  {
    field: "type",
    label: "Type",
    type: "select",
    valueOptions: [
      {
        label: "Buy",
        value: TradeType.BUY,
      },
      {
        label: "Sell",
        value: TradeType.SELL,
      },
    ],
    width: GenericTableFieldSizeEnum.xsmall,
    maxWidth: GenericTableFieldSizeEnum.small,
  },
  {
    field: "documents" as any,
    label: "Documents",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    exportable: false,
    sortable: false,
    renderDataCell: (row): JSX.Element => <GenericTableDocumentsButton row={row} />,
  },
  {
    field: "status",
    label: "Status",
    displayLabel: <StatusColumnLabel />,
    type: "custom",
    exportable: true,
    width: 650,
    maxWidth: 750,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => {
      const status = row?.currentStatus;

      if (status === TransactionStatus.EXECUTED)
        return `${row?.isDelivered ? "Delivered" : row?.isPaid ? "Paid" : TransactionStatusToLabel[status]}`;
      return TransactionStatusToLabel[status] || "";
    },
    renderDataCell: (row) => <StatusColumnField row={row} />,
  },
  {
    field: "updatedAt",
    label: "Updated Date",
    type: "date",
    width: 130,
    fixedWidth: true,
    hide: (rows) => rows?.at(0)?.creating || rows?.some((row) => row.editing),
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row) => <ActionsCell row={row} />,
  },

  // non table columns
  {
    field: "orders.0.source.name",
    label: "Book",
    type: "select",
    width: GenericTableFieldSizeEnum.small,
    collapse: true,
  },
  {
    field: "orders.0.unitPrice",
    label: "Price",
    type: "money",
    collapse: true,
    editable: false,
    dataTooltipContent: "Price (net of fees)",
    formHelperText: ({ row, errors }): JSX.Element => {
      const error =
        !!errors?.amends?.at(0)?.orders?.at(0)?.unitPrice?.message && row?.type === AllTransactionType.SELL
          ? "Required if Gross Revenue is empty."
          : errors?.amends?.at(0)?.orders?.at(0)?.unitPrice?.message;
      return <GenericTableFormErrorHelperText errors={[error]} />;
    },
  },
  {
    field: "orders.0.amount",
    label: "Quantity",
    type: "number",
    collapse: true,
    creatable: false,
    editable: false,
  },
  {
    field: "orders.0.rawPrice",
    label: "Sub Total",
    type: "money",
    collapse: true,
    creatable: false,
    editable: false,
  },
  {
    field: "orders.0.serviceFee",
    label: "Service Fee",
    type: "money",
    creatable: false,
    editable: false,
    collapse: true,
  },
  {
    field: "orders.0.otherFee",
    label: "Other Fee",
    type: "money",
    creatable: false,
    editable: false,
    collapse: true,
  },
  {
    field: "orders.0.feeTotal",
    label: "Fee Total",
    type: "money",
    collapse: true,
    editable: false,
    dataTooltipContent: "Service Fee + Other Fee",
  },
  {
    field: "orders.0.grandTotal",
    label: "Grand Total",
    type: "money",
    collapse: true,
    creatable: false,
    editable: false,
  },
  {
    field: "trade.tif",
    label: "TIF",
    type: "select",
    valueOptions: [
      {
        label: TIFEnum.GTC,
        value: TIFEnum.GTC,
      },
      {
        label: TIFEnum.EOD,
        value: TIFEnum.EOD,
      },
    ],
    collapse: true,
    creatable: false,
    editable: false,
    dataTooltipContent: "Time in force",
  },
  {
    field: "trade.poid",
    label: "External TXN ID",
    collapse: true,
    creatable: false,
    editable: false,
    tooltip: "External party Transaction ID",
  },
  {
    field: "orders.0.asset.projectTypeType", // todo: this might need to be removed due to multi assets
    label: "Project Type",
    collapse: true,
    creatable: false,
    editable: false,
  },
  {
    field: "dateFinished",
    label: "Settlement Date",
    type: "date",
    collapse: true,
    creatable: false,
    editable: false,
  },
  {
    field: "memo",
    label: "Memo",
    type: "textarea",
    collapse: true,
  },
];
