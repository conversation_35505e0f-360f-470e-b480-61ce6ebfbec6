import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { Typography } from "@mui/material";
import { TransactionType } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import Link from "next/link";
import COLORS from "@components/ui/theme/colors";
import ProductName from "@components/ui/product-name/product-name";
import { TransactionStatus, TransactionStatusToLabel } from "@constants/transaction-status";
import StatusChip from "@components/ui/status-chip/StatusChip";
import OrganizationName from "@components/ui/organization-name/organization-name";
import { TrimmedTransactionModel } from "@models/transaction";
import dateFormatter from "@utils/formatters/date-formatter";

import statusClasses from "../styles/status-column-label.module.scss";

import type { JSX } from "react";

export const TRANSACTION_COLUMNS: GenericTableColumn<TrimmedTransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TrimmedTransactionModel>) => (
      <Link
        href={`/trading/transactions/${row?.id}?type=${row?.type === TransactionType.TRADE ? row?.subtype : row?.type}`}
        style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}
      >
        <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "typeF",
    label: "Type",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "counterpartyName",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TrimmedTransactionModel): JSX.Element => (
      <OrganizationName
        organization={{ name: row?.counterpartyName, id: row?.counterpartyId } as any}
        style={{ fontSize: 14, fontWeight: 300 }}
      />
    ),
  },
  {
    field: "productF",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TrimmedTransactionModel): JSX.Element => {
      const assets = row?.assetFlows?.map(({ asset }) => asset);
      return <ProductName assets={assets} addTags={{ rct: false, suspended: false }} style={{ fontSize: 14 }} />;
    },
  },
  {
    field: "totalQuantity",
    label: "Volume",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "totalPrice",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "updatedAt",
    label: "Updated Date",
    type: "date",
    width: 130,
    fixedWidth: true,
    deriveDataValue: (row) => dateFormatter(row?.updatedAt?.toString(), "MM/dd/yyyy hh:mm:ss a"),
  },
  {
    field: "status",
    label: "Status",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => TransactionStatusToLabel[row?.status as TransactionStatus] || "",
    renderDataCell: (row) => <StatusChip status={row?.status} />,
  },
];
