"use client";

import { Maybe } from "@rubiconcarbon/frontend-shared";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useEffect, type JSX } from "react";
import NewPurchaseForm from "./sale-form";
import TradeForm from "../../components/trade/form";
import {
  AdminOrganizationQueryResponse,
  AdminGroupingParentQueryResponse,
  AdminModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";

export default function NewTransaction({
  type,
  organizationsResponse,
  booksByParentResponse,
  mockPortfolioResponse,
}: {
  type: string;
  organizationsResponse?: AdminOrganizationQueryResponse;
  booksByParentResponse: AdminGroupingParentQueryResponse;
  mockPortfolioResponse?: AdminModelPortfolioResponse;
}): JSX.Element {
  const { updateBreadcrumbName } = useBreadcrumbs();

  useEffect(() => {
    updateBreadcrumbName?.("New", type === "sale" ? "New Customer Sale" : "New Trade");
  }, [type, updateBreadcrumbName]);

  return (
    <>
      <Maybe condition={type === "sale"}>
        <NewPurchaseForm
          organizationsResponse={organizationsResponse}
          booksByParentResponse={booksByParentResponse}
          mockPortfolioResponse={mockPortfolioResponse}
        />
      </Maybe>
      <Maybe condition={type === "trade"}>
        <TradeForm organizationsResponse={organizationsResponse} booksByParentResponse={booksByParentResponse} />
      </Maybe>
    </>
  );
}
