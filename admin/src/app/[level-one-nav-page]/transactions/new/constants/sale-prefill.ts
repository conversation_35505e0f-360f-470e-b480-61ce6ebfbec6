import { BookType } from "@rubiconcarbon/shared-types";

export interface BookSelection {
  bookType: BookType;
  ownerType: BookType;
}

export const bookSelectionOrder: BookSelection[] = [
  {
    bookType: BookType.PORTFOLIO_DEFAULT,
    ownerType: BookType.<PERSON>OR<PERSON><PERSON><PERSON>_DEFAULT,
  },
  {
    bookType: BookType.OPPORTUNISTIC_DEFAULT,
    ownerType: BookType.OPPORTUNISTIC_DEFAULT,
  },
  {
    bookType: BookType.REHABILITATION_DEFAULT,
    ownerType: BookType.REHABILITATION_DEFAULT,
  },
  {
    bookType: BookType.AGED_DEFAULT,
    ownerType: BookType.AGED_DEFAULT,
  },
];
