import { AuthorizeServer } from "@app/authorize-server";
import {
  BookRelations,
  AdminOrganizationQueryResponse,
  AdminGroupingParentQueryResponse,
  PermissionEnum,
  AdminModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";
import NewTransaction from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * New Transaction Page
 *
 * This is a server component that renders the New Transaction page
 */
export default async function NewTransactionPage({
  searchParams,
}: {
  searchParams: Promise<{ type: string; id?: string }>;
}): Promise<JSX.Element> {
  const { type, id } = await searchParams;
  const isTrade = type === "trade";
  const isSale = type === "sale";

  const organizationsResponse = await withErrorHandling(
    async () =>
      baseApiRequest<AdminOrganizationQueryResponse>(
        `admin/organizations?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          isEnabled: true,
          includeTotalCount: false,
          isCounterparty: isTrade,
          isCustomerPortfolio: isSale,
        })}`,
      ),
  );

  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminGroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  const mockPortfolioResponse =
    isSale && id
      ? await withErrorHandling(async () => baseApiRequest<AdminModelPortfolioResponse>(`admin/books/${id}`))
      : undefined;

  // Check if the result is a server error
  if (isValidElement(organizationsResponse)) return organizationsResponse;
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;
  if (isValidElement(mockPortfolioResponse)) return mockPortfolioResponse;

  return (
    <AuthorizeServer
      permissions={[type === "sale" ? PermissionEnum.CUSTOMER_SALES_CREATE : PermissionEnum.TRADES_CONFIRM_INDICATIVE]}
    >
      <NewTransaction
        type={type}
        organizationsResponse={organizationsResponse as AdminOrganizationQueryResponse}
        booksByParentResponse={booksByParentResponse as AdminGroupingParentQueryResponse}
        mockPortfolioResponse={mockPortfolioResponse as AdminModelPortfolioResponse}
      />
    </AuthorizeServer>
  );
}
