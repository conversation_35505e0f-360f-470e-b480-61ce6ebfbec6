.Details {
    .Section {
        .SectionHeader {
            font-weight: 600;
            text-transform: uppercase;
        }

        .KeyValueText {
            display: flex;
            gap: 5px;
            font-weight: 100;

            .Key {
                font-weight: 550;
            }
        }

        .Table {
            border-radius: 2px;
            border: solid #E0E0E0 1px;
            border-left: unset;
            border-right: unset;
        
            .Header {
                background-color: #F5F5F5;
                padding: 5px;
                font-size: small;
                font-weight: 600;
            }
        
            .Body {
                padding: 5px;
                font-size: small;
        
                .BodyRow {
                    min-height: 70px;
                }
            }
        
            .Footer {
                padding: 0 5px 7px 5px;
                font-size: small;

                * > .Key {
                    font-weight: 600;
                }
            }
        }
    }
}