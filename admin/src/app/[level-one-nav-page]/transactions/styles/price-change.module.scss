.Chip {
    background-color: white;
    padding: 7px 5px !important;
    border-radius: 20px;
    width: fit-content;

    * {
        font-weight: 100;
    }

    &.Gain {
        color: #4FA54F;
    }

    &.Loss {
        color: #D46062;
    }

    .ChangeIcon {
        border-radius: 50%;

        &.Gain {
            background-color: lighten($color: #4FA54F, $amount: 40%);
            rotate: 45deg;
        }

        &.Loss {
            background-color: lighten($color: #D46062, $amount: 30%);
            rotate: 135deg;
        }
    }

    .Label {
        color: inherit;
    }
}