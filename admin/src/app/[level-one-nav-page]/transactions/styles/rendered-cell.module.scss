.Actions {
    .<PERSON><PERSON> {
        font-weight: 550;
        text-transform: capitalize;
        border-radius: 5px;
        padding: 5px 10px;
    }

    .GrayTextColor {
        color: #0000008A;

        &:disabled {
            color: #00000045;
        }
    }

    .MenuListButton {
        width: 100%;
        font-size: 16px;
        justify-content: start;
    }
}

.Badge {
    position: absolute;
    right: 10px;
    top: 4.5px;
    font-size: 10px;

    color: white;
    background-color: #166350;
}

.IconButton {
    width: 45px;
    height: 45px;

    .Badge {
        background-color: #077D55;
    }
    
    .Icon {
        color: rgba(0, 0, 0, 0.26);
    }

    .EnabledIcon {
        color: #094436b8;
    }

    .HasDocsIcon {
        color:  #094436;
    }

    .Disabled {
        background-color: rgba(0, 0, 0, 0.26);
    }
}