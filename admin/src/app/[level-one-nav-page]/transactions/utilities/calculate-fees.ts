import { TransactionModel } from "@models/transaction";
import { calculator, Nullable } from "@rubiconcarbon/frontend-shared";
import { AdminCounterpartyResponse, TradeType } from "@rubiconcarbon/shared-types";

export const calculateFees = (
  transaction: TransactionModel,
  counterparty: AdminCounterpartyResponse,
): Nullable<string> => {
  const type = transaction?.type;
  const amount = transaction?.orders?.at(0)?.amount;
  const rawPrice = transaction?.orders?.at(0)?.rawPrice;
  const defaultFee = counterparty?.defaultFees?.find((fee) => fee.type === (type as unknown as TradeType));

  if (!defaultFee) return null;

  switch (defaultFee?.feeType) {
    case "fixed":
      return defaultFee?.fixedFee?.toString();
    case "per_tonne":
      return calculator(defaultFee?.perTonneFee, { parserBlacklist: [",", "$"] })
        .multiply(amount)
        .calculate()
        .toString();
    case "percentage":
      return calculator(defaultFee?.percentageFee, { parserBlacklist: ["%", ","] })
        .multiply(rawPrice)
        .calculate()
        .toString();
    default:
      return null;
  }
};
