import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { AssetOrder, NonTradeAssetDetails } from "@models/transaction";
import {
  AdminBookResponse,
  AdminAllocationResponse,
  AdminGroupedAllocationWithNestedResponse,
  AdminModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import { bookSelectionOrder } from "../constants/sale-prefill";
import { Nullable } from "@rubiconcarbon/frontend-shared";
import { AdminModelPortfolioComponentResponseFE } from "../../portfolio-sandbox/[portfolio-id]/edit-portfolio-sandbox/components/portfolio-sandbox-table/portfolio-sandbox-model";

export function buildProduct(
  allocation: AdminAllocationResponse,
  selectedBook: AdminBookResponse,
  amount: number,
): Nullable<AssetOrder> {
  if (allocation) {
    const asset = new AssetOrder();
    asset.isAPurchase = true;
    asset.isASale = true;
    asset.amount = amount.toString();
    const selectedBookSource: PrimitiveTypeBook = {
      id: selectedBook.id,
      name: selectedBook.name,
      type: selectedBook.type,
      limit: {},
    };
    asset.source = selectedBookSource;
    const supplementaryAssetDetails = new NonTradeAssetDetails();
    supplementaryAssetDetails.id = allocation?.asset?.id;
    supplementaryAssetDetails.type = "vintage";
    supplementaryAssetDetails.name = allocation?.asset?.name;
    supplementaryAssetDetails.sourceName = allocation?.owner?.name;
    supplementaryAssetDetails.available = allocation?.amountAvailable;
    supplementaryAssetDetails.projectName = allocation?.asset?.name;
    supplementaryAssetDetails.registryProjectId = allocation?.asset?.registryProjectId;
    supplementaryAssetDetails.wholeAsset = allocation?.asset;
    supplementaryAssetDetails.sourceId = allocation?.owner?.id;
    supplementaryAssetDetails.sourceType = allocation?.owner?.type;
    supplementaryAssetDetails.sourceName = allocation?.owner?.name;
    asset.supplementaryAssetDetails = supplementaryAssetDetails;

    return asset;
  }
  return null;
}

export function buildPrefilledProducts(
  mockPortfolio: AdminModelPortfolioResponse,
  books: AdminBookResponse[],
): AssetOrder[] {
  const products: AssetOrder[] = [];
  if (!!mockPortfolio && !isEmpty(books)) {
    (mockPortfolio?.modelPortfolioComponents as unknown as AdminModelPortfolioComponentResponseFE[])?.forEach((mp) => {
      let bookSelectionIdx = 0;
      let amountToFill = mp.amountAllocated ?? 0;
      while (bookSelectionIdx < bookSelectionOrder.length && amountToFill > 0) {
        const selectedBook = books?.find(
          (book) => book.type === bookSelectionOrder[bookSelectionIdx].bookType,
        );

        const selectedAllocation = (
          selectedBook?.ownerAllocations as AdminGroupedAllocationWithNestedResponse
        ).allocations?.find(
          (allocation) =>
            allocation?.owner?.type === bookSelectionOrder[bookSelectionIdx].ownerType &&
            allocation?.asset.id === mp.projectVintage.id,
        );

        if (selectedAllocation) {
          const assetAmount =
            selectedAllocation.amountAvailable < amountToFill ? selectedAllocation.amountAvailable : amountToFill;
          amountToFill = amountToFill - selectedAllocation.amountAvailable;

          if (selectedBook) {
          const product = buildProduct(selectedAllocation, selectedBook, assetAmount);
          
          if (product) products.push(product);
          }
        }

        bookSelectionIdx++;
      }
    });
  }
  return products;
}
