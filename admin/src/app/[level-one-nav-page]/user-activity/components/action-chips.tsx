import { actionDisplay, AggregateUserActionType } from "../types/general";

import { Chip } from "@mui/material";

import type { JSX } from "react";

export default function ActionChips(props: { actions: AggregateUserActionType[] }): JSX.Element {
  if (props.actions.length == 0) return <>-</>;
  return (
    <>
      {props.actions.map((x) => {
        const defs = actionDisplay(x);
        return (
          <Chip key={defs.display} label={defs.display} sx={{ backgroundColor: defs.color, margin: "3px 5px 3px 0" }} />
        );
      })}
    </>
  );
}
