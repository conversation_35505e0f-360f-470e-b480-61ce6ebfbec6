import React, { type JSX } from "react";
import { AdminProjectVintageResponse, type UserActionDataType, uuid } from "@rubiconcarbon/shared-types";
import { IconButton, Tooltip } from "@mui/material";
import DetailsIcon from "@mui/icons-material/Info";
import ByoDetails from "./byo-details";
import PurchaseDetails from "./purchase-details";

export default function ActionInfo({
  action,
  vintages,
  projects,
}: {
  action: UserActionDataType;
  vintages: Map<uuid, AdminProjectVintageResponse>;
  projects: Map<uuid, string>;
}): JSX.Element {
  switch (action.type) {
    case "byorct_price_estimate":
    case "byorct_requested":
      return (
        <>
          {(action.data.vintageIds ?? []).length > 0 ? (
            <Tooltip title={<ByoDetails action={action} vintages={vintages} />} enterDelay={250} enterNextDelay={250}>
              <IconButton aria-label="expand row" size="small">
                <DetailsIcon />
              </IconButton>
            </Tooltip>
          ) : (
            <></>
          )}
        </>
      );
    case "rct_quote_request":
      return (
        <>
          <Tooltip
            title={<PurchaseDetails action={action} projects={projects} />}
            enterDelay={250}
            enterNextDelay={250}
          >
            <IconButton aria-label="expand row" size="small">
              <DetailsIcon />
            </IconButton>
          </Tooltip>
        </>
      );
  }
  return <></>;
}
