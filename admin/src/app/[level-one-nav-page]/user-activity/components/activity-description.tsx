import React, { type JSX } from "react";
import { AssetType, AdminTransactionResponse, type UserActionDataType, uuid } from "@rubiconcarbon/shared-types";
import { sum } from "lodash";
import Link from "next/link";
import integerFormat from "@utils/formatters/integer-format";
import { currencyFormat, numberFormat } from "@rubiconcarbon/frontend-shared";

import classes from "../styles/user-activity.module.scss";

export default function ActivityDescription({
  action,
  projects,
  products,
  retirements,
}: {
  action: UserActionDataType;
  projects: Map<uuid, string>;
  products: Map<uuid, string>;
  retirements: Map<uuid, AdminTransactionResponse>;
}): JSX.Element {
  switch (action.type) {
    case "project_viewed": {
      const project = projects.get(action.data.projectId);
      if (project === undefined) return <></>;
      return (
        <Link href={`/projects/${action.data.projectId}`} className={classes.Link}>
          {project}
        </Link>
      );
    }
    case "portfolio_viewed": {
      const product = products.get(action.data.productId);
      if (product === undefined) return <></>;
      return (
        <Link href={`/inventory-management/portfolios/${action.data.productId}`} className={classes.Link}>
          {product}
        </Link>
      );
    }
    case "byorct_price_estimate": {
      return (
        <>
          <span className={classes.Emphasis}>Requested BYO live quote</span>
          <span className={classes.Details}>
            {" "}
            ({integerFormat(sum(action.data.quantities))} tonnes
            {action.data.riskAdjusted ? ", risk adjusted" : ""}
            {action.data.forRetirement ? ", for retirement" : ", to hold"})
          </span>
        </>
      );
    }
    case "byorct_requested": {
      return (
        <>
          {/* handle old data */}
          {action.data.modelPortfolioId == null ? (
            <>Requested price quote</>
          ) : (
            <Link
              href={`/inventory-management/portfolio-sandbox/${action.data.modelPortfolioId}/edit-portfolio-sandbox`}
              className={classes.Link}
            >
              Requested BYO price quote
            </Link>
          )}
          <span className={classes.Details}>
            {" "}
            ({integerFormat(sum(action.data.quantities))} tonnes for {currencyFormat(action.data.price)}
            {action.data.riskAdjusted ? ", risk adjusted" : ""}
            {action.data.forRetirement ? ", for retirement" : ", to hold"})
          </span>
        </>
      );
    }
    case "rct_retirement_request": {
      const retirement = retirements.get(action.data.retirementId);
      if (retirement === undefined) return <></>;

      return (
        <>
          <Link href={`/customer-transactions/retirements/${action.data.retirementId}`} className={classes.Link}>
            Retirement Requested
          </Link>{" "}
          for{" "}
          {retirement.assetFlows.map((flow, i) => {
            const isPortfolioAsset = flow?.asset?.type === AssetType.RCT;
            const id = isPortfolioAsset ? flow?.asset?.id : flow?.asset?.projectId;
            const href = `${isPortfolioAsset ? "/inventory-management/portfolios/" : "/projects/"}${id}`;

            return (
              <>
                {i > 0 ? ", " : ""}
                <Link key={id} href={href} className={classes.Link}>
                  {flow?.asset?.name}
                </Link>
              </>
            );
          })}
          <span className={classes.Details}>
            {" "}
            ({numberFormat(sum(retirement.assetFlows?.map((x) => x.amount)))} tonnes)
          </span>
        </>
      );
    }
    case "rct_quote_request": {
      const product = products.get(action.data.productId);
      if (product === undefined) return <></>;
      return (
        <>
          Price quote Requested for{" "}
          <Link href={`/inventory-management/portfolios/${action.data.productId}`} className={classes.Link}>
            {product}
          </Link>
          <span className={classes.Details}>
            {" "}
            ({integerFormat(action.data.quantity)} tonnes for {currencyFormat(action.data.price * action.data.quantity)}
            {action.data.riskAdjusted ? ", risk adjusted" : ""}
            {action.data.forRetirement ? ", for retirement" : ", to hold"})
          </span>
        </>
      );
    }
  }
  return <></>;
}
