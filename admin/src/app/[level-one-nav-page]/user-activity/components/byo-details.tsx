import React, { type JSX } from "react";
import { AdminProjectVintageResponse, type UserActionDataType, uuid } from "@rubiconcarbon/shared-types";
import { sum } from "lodash";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import integerFormat from "@utils/formatters/integer-format";
import { currencyFormat } from "@rubiconcarbon/frontend-shared";

export default function ByoDetails({
  action,
  vintages,
}: {
  action: UserActionDataType;
  vintages: Map<uuid, AdminProjectVintageResponse>;
}): JSX.Element {
  switch (action.type) {
    case "project_viewed":
    case "portfolio_viewed":
      return <></>;
    case "byorct_price_estimate":
    case "byorct_requested":
      return (
        <>
          <table style={{ borderSpacing: 0, borderCollapse: "collapse" }}>
            <thead>
              <tr>
                <th align="left">Project</th>
                <th style={{ padding: "0 5px" }}>Vintage</th>
                <th align="right">Tonnes</th>
              </tr>
            </thead>
            <tbody>
              {action.data.vintageIds.map((x, i) => (
                <tr key={x}>
                  <td align="left">{vintages.get(x)?.project?.name ?? "-"}</td>
                  <td align="center" style={{ padding: "0 5px" }}>
                    {vintages.get(x)?.name}
                  </td>
                  <td align="right">{integerFormat(action.data.quantities.at(i))}</td>
                </tr>
              ))}
              <tr>
                <td></td>
                <td></td>
                <td style={{ borderTop: "white 1px solid" }} align="right">
                  {integerFormat(sum(action.data.quantities))}
                </td>
              </tr>
            </tbody>
          </table>
          <div>
            <div style={{ display: "flex", justifyContent: "space-between", marginTop: 4 }}>
              <div style={{ display: "flex", alignItems: "center" }}>
                Risk Adjusted
                {action.data.riskAdjusted ? <CheckIcon sx={{ height: 14 }} /> : <CloseIcon sx={{ height: 14 }} />}
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                For Retirement
                {action.data.forRetirement ? <CheckIcon sx={{ height: 14 }} /> : <CloseIcon sx={{ height: 14 }} />}
              </div>
            </div>
            {"price" in action.data ? (
              <div style={{ display: "flex", flexDirection: "row", marginTop: 10 }}>
                <div>
                  <div>Quoted Price:</div>
                  <div>Quoted Price per tonne:</div>
                </div>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-end" }}>
                  <div style={{ marginLeft: 10 }}>{currencyFormat((action.data as any).price)}</div>
                  <div style={{ marginLeft: 10 }}>
                    {currencyFormat((action.data as any).price / sum(action.data.quantities))}
                  </div>
                </div>
              </div>
            ) : (
              <></>
            )}
          </div>
        </>
      );
  }
  return <></>;
}
