import React, { type JSX } from "react";
import {
  AdminProjectVintageResponse,
  AdminTransactionResponse,
  type UserActionDataType,
  uuid,
} from "@rubiconcarbon/shared-types";
import dateFormatterLocalTime from "@utils/formatters/local-time-date-formatter";
import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import ActionChips from "./action-chips";
import { UserActivityTableRow } from "../types/general";
import ActionInfo from "./action-info";
import ActivityDescription from "./activity-description";

export default function ExpandedRow({
  row,
  vintages,
  projects,
  products,
  retirements,
}: {
  row: UserActivityTableRow;
  vintages: Map<uuid, AdminProjectVintageResponse>;
  projects: Map<uuid, string>;
  products: Map<uuid, string>;
  retirements: Map<uuid, AdminTransactionResponse>;
}): JSX.Element {
  return (
    <>
      <h4>Detailed Activity Log</h4>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>Timestamp</TableCell>
              <TableCell align="center">Activity</TableCell>
              <TableCell>Description</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {row.events.map((ev) => (
              <TableRow key={ev.createdAt.toString()} sx={{ "&:last-child td, &:last-child th": { border: 0 } }}>
                <TableCell>{dateFormatterLocalTime(ev.createdAt.toString())}</TableCell>
                <TableCell align="center">
                  <ActionChips actions={[ev.type]} />
                </TableCell>
                <TableCell>
                  {
                    <ActivityDescription
                      action={ev as UserActionDataType}
                      products={products}
                      projects={projects}
                      retirements={retirements}
                    />
                  }{" "}
                </TableCell>
                <TableCell>
                  {<ActionInfo action={ev as UserActionDataType} vintages={vintages} projects={projects} />}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}
