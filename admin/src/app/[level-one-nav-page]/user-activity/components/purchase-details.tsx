import React, { type JSX } from "react";
import { type UserActionDataType, uuid } from "@rubiconcarbon/shared-types";
import { orderBy } from "lodash";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import { currencyFormat } from "@rubiconcarbon/frontend-shared";

export default function PurchaseDetails({
  action,
  projects,
}: {
  action: UserActionDataType;
  projects: Map<uuid, string>;
}): JSX.Element {
  switch (action.type) {
    case "rct_quote_request":
      return (
        <>
          <table style={{ borderSpacing: 0, borderCollapse: "collapse" }}>
            <thead>
              <tr>
                <th align="left">Projects {action.data.projectIds.length > 10 ? "(Top 10)" : ""}</th>
                <th align="right">%</th>
              </tr>
            </thead>
            <tbody>
              {orderBy(
                action.data.projectIds
                  .map((x, i) => [x, action.data.percentages.at(i)])
                  .filter((item): item is [uuid, number] => Array.isArray(item) && typeof item[1] === 'number' && item[1] > 0.01),
                (a) => -a[1],
              )
                .slice(0, 10)
                .map(([x, p]: [uuid, number]) => (
                  <tr key={x}>
                    <td align="left" style={{ paddingRight: "10px" }}>
                      {projects.get(x) ?? "-"}
                    </td>
                    <td align="right">{(100 * p).toFixed(2)}</td>
                  </tr>
                ))}
            </tbody>
          </table>
          <div>
            <div style={{ display: "flex", justifyContent: "space-between", marginTop: 10 }}>
              <div style={{ display: "flex", alignItems: "center" }}>
                Risk Adjusted
                {action.data.riskAdjusted ? <CheckIcon sx={{ height: 14 }} /> : <CloseIcon sx={{ height: 14 }} />}
              </div>
              <div style={{ display: "flex", alignItems: "center" }}>
                For Retirement
                {action.data.forRetirement ? <CheckIcon sx={{ height: 14 }} /> : <CloseIcon sx={{ height: 14 }} />}
              </div>
            </div>
            <div style={{ display: "flex", flexDirection: "row", marginTop: 10 }}>
              <div>
                <div>Quoted Price:</div>
                <div>Quoted Price per tonne:</div>
              </div>
              <div style={{ display: "flex", flexDirection: "column", alignItems: "flex-end" }}>
                <div style={{ marginLeft: 10 }}>
                  {currencyFormat((action.data as any).price * (action.data as any).quantity)}
                </div>
                <div style={{ marginLeft: 10 }}>{currencyFormat((action.data as any).price)}</div>
              </div>
            </div>
          </div>
        </>
      );
  }
  return <></>;
}
