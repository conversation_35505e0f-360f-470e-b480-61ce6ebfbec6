import { Chip } from "@mui/material";
import { EventHandler, type JSX } from "react";
import { actionDisplay, AggregateUserActionType } from "../types/general";

export default function SearchChips(props: {
  actions: AggregateUserActionType[];
  onDelete: EventHandler<any>;
}): JSX.Element {
  if (props.actions.length == 0) return <>-</>;
  return (
    <>
      {props.actions.map((x) => {
        const defs = actionDisplay(x);
        return (
          <Chip
            key={defs.display}
            label={defs.display}
            sx={{ backgroundColor: defs.color, margin: "3px 5px 3px 0" }}
            size="small"
            // deleteIcon={<CancelIcon onMouseDown={(event) => event.stopPropagation()} />}
            onDelete={props.onDelete}
          />
        );
      })}
    </>
  );
}
