import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, AdminUserActionResponse } from "@rubiconcarbon/shared-types";
import UserActivity from "./components";
import { subDays } from "date-fns";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * User Activity Page
 *
 * This is a server component that renders the User Activity page
 */
export default async function UserActivityPage({
  searchParams,
}: {
  searchParams: Promise<{ since?: string }>;
}): Promise<JSX.Element> {
  const { since = "7" /* default to 7 days */ } = await searchParams;

  const useActionsDataResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminUserActionResponse[]>(
      `admin/user-actions?${generateQueryParams({
        since: subDays(new Date(), Number(since)).toISOString(),
        includeInternal: process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT !== "prod",
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(useActionsDataResponse)) return useActionsDataResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.USER_ACTIONS_VIEW_CUSTOMER]}>
      <UserActivity since={since} useActionsDataResponse={useActionsDataResponse as AdminUserActionResponse[]} />
    </AuthorizeServer>
  );
}
