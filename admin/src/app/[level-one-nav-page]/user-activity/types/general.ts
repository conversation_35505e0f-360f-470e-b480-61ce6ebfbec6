import { AdminUserActionResponse, type UserActionType, uuid } from "@rubiconcarbon/shared-types";

export type AggregateUserActionType = Omit<UserActionType, "login"> | "byo";

export interface UserActivityTableRow {
  id: string;
  user: string;
  userId: uuid;
  org: string;
  orgId?: string;
  lastAction: Date;
  lastLogin: Date;
  actions: AggregateUserActionType[];
  events: AdminUserActionResponse[];
}

export function actionDisplay(a: AggregateUserActionType): { display: string; color: string } {
  switch (a) {
    case "byo":
    case "byorct_price_estimate":
    case "byorct_requested":
      return { display: "BYO Interaction", color: "#eaf2ea" };
    case "project_viewed":
      return { display: "Project Viewed", color: "#e5f3fa" };
    case "portfolio_viewed":
      return { display: "Portfolio Viewed", color: "#fbeaea" };
    case "rct_quote_request":
      return { display: "Quote Request", color: "#fbceb1" };
    case "rct_retirement_request":
      return { display: "Retirement Request", color: "#d8bfd8" };
    default:
      return { display: "Unknown Activity", color: "lightgray" };
  }
}

export const allowedTypes: [AggregateUserActionType, string][] = [
  ["byo", "BYO Interaction"],
  ["project_viewed", "Project Viewed"],
  ["portfolio_viewed", "Portfolio Viewed"],
  ["rct_quote_request", "Quote Requested"],
  ["rct_retirement_request", "Retirement Request"],
];

export const allowedTypeByName = new Map(allowedTypes.map((x) => [x[1], x[0]]));
export const allowedTypeByCode = new Map(allowedTypes);
