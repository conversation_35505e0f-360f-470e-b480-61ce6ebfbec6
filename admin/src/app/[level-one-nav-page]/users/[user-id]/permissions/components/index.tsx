"use client";;
import Page from "@components/layout/containers/page";
import UserPermissionsComponent from "./user-permissions";
import { AdminOrganizationResponse, AdminUserResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

export default function UserPermissions({
  userResponse,
  organizationsResponse,
}: {
  userResponse: AdminUserResponse;
  organizationsResponse: AdminOrganizationResponse[];
}): JSX.Element {
  return (
    <Page>
      <UserPermissionsComponent userResponse={userResponse} organizationsResponse={organizationsResponse} />
    </Page>
  );
}
