import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, PermissionEnum, AdminUserResponse, AdminOrganizationResponse } from "@rubiconcarbon/shared-types";
import UserPermissions from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * User Permissions Page
 *
 * This is a server component that renders the User Permissions page
 */
export default async function UserPermissionsPage({
  params,
}: {
  params: Promise<{ "user-id": string }>;
}): Promise<JSX.Element> {
  const { "user-id": id } = await params;

  const userResponse = await withErrorHandling(async () => baseApiRequest<AdminUserResponse>(`admin/users/${id}`));

  const organizationsResponse = await withErrorHandling(
    async () =>
      baseApiRequest<AdminOrganizationQueryResponse>(
        `admin/organizations?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: false,
        })}`,
      ),
  );

  // Check if the result is a server error
  if (isValidElement(userResponse)) return userResponse;
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.USERS_READ, PermissionEnum.ORGANIZATIONS_MANAGE_USERS]}>
      <UserPermissions
        userResponse={userResponse as AdminUserResponse}
        organizationsResponse={(organizationsResponse as AdminOrganizationQueryResponse)?.data as AdminOrganizationResponse[]}
      />
    </AuthorizeServer>
  );
}
