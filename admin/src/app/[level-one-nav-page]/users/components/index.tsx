"use client";;
import { AdminUserQueryResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import UsersTable from "./users-table";

import type { JSX } from "react";

export default function Users({ usersResponse }: { usersResponse: AdminUserQueryResponse }): JSX.Element {
  return (
    <Page>
      <UsersTable usersResponse={usersResponse} />
    </Page>
  );
}
