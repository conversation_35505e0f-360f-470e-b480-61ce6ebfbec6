import React, {
  useState,
  BaseSyntheticEvent,
  useContext,
  useEffect,
  useMemo,
  useCallback,
  type JSX,
} from "react";
import { AxiosContext } from "@providers/axios-provider";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  DialogTitle,
  FormControlLabel,
  Radio,
  FormControl,
  RadioGroup,
} from "@mui/material";
import { AdminUserResponse, uuid } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useLogger } from "@providers/logging";
import { BaseDialogProps } from "@models/dialogs";

const cancelBtnStyle = {
  height: 35,
  color: "rgba(0, 0, 0, 0.87)",
  borderRadius: "5px",
  textTransform: "capitalize",
  fontWeight: 500,
  backgroundColor: "rgba(224, 224, 224, 1)",
  "&.Mui-disabled": {
    color: "gray !important",
  },
  "&:hover": {
    backgroundColor: "rgba(224, 224, 224, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 1px 1px",
  },
};

const disabledStyle = {
  padding: "16px",
  height: "200px",
  backgroundColor: "rgba(253, 237, 237, 1)",
};

const enabledStyle = {
  padding: "16px",
  height: "200px",
  backgroundColor: "rgba(229, 246, 253, 1)",
};

const radioLabelStyle = {
  marginTop: 1,
  marginBottom: 1,
};

export interface UserStatusInput {
  id: uuid;
  userName: string;
  isEnabled: boolean;
}

interface UserStatusModalProps extends BaseDialogProps {
  user: UserStatusInput;
  onSave: () => void;
}

export default function UserStatusModal({ user, isOpen, onClose, onSave }: UserStatusModalProps): JSX.Element | null {
  const [isConfirmation, setIsConfirmation] = useState<boolean>(false);
  const [isEnabled, setIsEnabled] = useState<string>("");
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  useEffect(() => {
    if (user) {
      setIsEnabled(user.isEnabled === true ? "true" : "false");
    }
  }, [user]);

  const onSubmitHandler = (event: BaseSyntheticEvent): void => {
    event.preventDefault();
    setIsConfirmation(true);
  };

  const onCloseHandler = useCallback((): void => {
    setIsConfirmation(false);
    onClose();
  }, [onClose]);

  const submitUserStatus = useCallback(async (): Promise<void> => {
    const payload = {
      status: isEnabled === "true" ? "enabled" : "disabled",
    };

    try {
      await api.patch<AdminUserResponse>(`admin/users/${user.id}`, payload);
      enqueueSuccess("User status was updated succsessfully");
      setIsConfirmation(false);
      onSave();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        logger.error(error.response.data.message, {});
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable update user status");
    }
  }, [isEnabled, user?.id, api, logger, enqueueError, enqueueSuccess, onSave]);

  const isInputChanged = useMemo(
    () => (user?.isEnabled === true ? "true" : "false") !== isEnabled,
    [isEnabled, user?.isEnabled],
  );
  const dialogHead = useMemo(
    () => (
      <Box mt={1}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          {isConfirmation ? "Please Confirm" : "Change User Status"}
        </Typography>
      </Box>
    ),
    [isConfirmation],
  );

  const dialogActions = useMemo(
    () => (
      <Stack direction="row" gap={3}>
        <Button variant="text" onClick={onCloseHandler} sx={cancelBtnStyle}>
          Cancel
        </Button>

        <Maybe condition={!isConfirmation}>
          <ActionButton
            type="submit"
            form="user-status-form"
            style={{ fontWeight: 500, width: "100px", textTransform: "capitalize" }}
            isDisabled={!isInputChanged}
          >
            save
          </ActionButton>
        </Maybe>

        <Maybe condition={isConfirmation}>
          <Stack direction="row" sx={{ marginRight: "-10px" }}>
            <ActionButton
              style={{
                fontWeight: 500,
                width: "100px",
                textTransform: "capitalize",
              }}
              onClickHandler={submitUserStatus}
            >
              Save
            </ActionButton>
          </Stack>
        </Maybe>
      </Stack>
    ),
    [isConfirmation, isInputChanged, onCloseHandler, submitUserStatus],
  );

  const statusChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setIsEnabled(event.target.value);
  };

  return (
    <Dialog open={isOpen} onClose={onCloseHandler} fullWidth>
      <DialogTitle sx={{ backgroundColor: "white" }}>{dialogHead}</DialogTitle>

      <DialogContent sx={{ height: "110px", overflow: "hidden" }}>
        <Box mt={-1} sx={{ width: "100%" }}>
          <Maybe condition={!isConfirmation}>
            <form id="user-status-form" onSubmit={onSubmitHandler}>
              <Stack direction="row" sx={{ width: "100%" }}>
                <FormControl fullWidth sx={{ marginTop: -3 }}>
                  <RadioGroup
                    aria-labelledby="user-status-group"
                    name="controlled-radio-buttons-group"
                    value={isEnabled}
                    onChange={statusChangeHandler}
                    sx={{ paddingTop: "7px" }}
                    row
                  >
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"true"}
                      control={<Radio />}
                      label={<Typography variant="body1">Enabled</Typography>}
                    />
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"false"}
                      control={<Radio />}
                      label={<Typography variant="body1">Disabled</Typography>}
                    />
                  </RadioGroup>
                </FormControl>
              </Stack>
            </form>
          </Maybe>
          <Maybe condition={isConfirmation}>
            <Box mt={-4} sx={isEnabled === "true" ? enabledStyle : disabledStyle}>
              <Stack direction="row" gap={1}>
                <InfoOutlinedIcon
                  sx={{ color: isEnabled === "true" ? "rgba(17, 149, 214, 1)" : "rgba(211, 47, 47, 1)" }}
                />
                {isEnabled === "true" ? (
                  <Typography>
                    You are about to enable <b>{user?.userName}&apos;s</b> access to the customer portal.
                  </Typography>
                ) : (
                  <Typography>
                    You are about to disable <b>{user?.userName}&apos;s</b> access to the customer portal.
                  </Typography>
                )}
              </Stack>
            </Box>
          </Maybe>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "right",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
