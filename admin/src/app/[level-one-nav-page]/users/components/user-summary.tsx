import { Grid, Box } from "@mui/material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import { MISSING_DATA } from "@constants/constants";
import useNavigation from "@hooks/use-navigation";
import { UserData } from "@models/user-data";
import ItemDetails from "@components/ui/details/item-details";
import ButtonGroup from "@components/ui/button-group/button-group";

import type { JSX } from "react";

interface UserSummaryProps {
  user: UserData;
}

export default function UserSummary(props: UserSummaryProps): JSX.Element {
  const { pushToPath } = useNavigation();
  const { user } = props;
  const buttons = [
    {
      id: "setPermissions",
      name: "Set organization & permissions",
      handler: setPermissionsHandler,
      tooltip: "Set organization & permissions",
      requiredPermission: PermissionEnum.ORGANIZATIONS_MANAGE_USERS,
    },
  ];

  function setPermissionsHandler(): void {
    pushToPath(`${user.id}/permissions`);
  }

  return (
    <Box>
      <Grid
        container
        spacing={0}
        flexDirection={{
          xs: "column",
          md: "row",
        }}
        alignItems={{
          xs: "flex-start",
          md: "center",
        }}
      >
        <Grid container spacing={0}>
          <Grid item xs={12}>
            <ItemDetails
              label="Date/Time Updated:"
              value={user?.updatedAt ? dateFormatterEST(user.updatedAt.toString()) : MISSING_DATA}
            />
          </Grid>
          <Grid item xs={12}>
            <ItemDetails
              label="Last Logged In:"
              value={user?.lastLogin ? dateFormatterEST(user.lastLogin.toString()) : MISSING_DATA}
            />
          </Grid>
          <Grid item xs={12} sx={{ textTransform: "capitalize" }}>
            <ItemDetails label="Roles:" value={user.roles} />
          </Grid>
          <Grid container spacing={0} mt={4} mb={2}>
            <Grid item xs={12}>
              <ButtonGroup buttons={buttons} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
