import React, { useState, useEffect, useMemo, type JS<PERSON> } from "react";
import { AdminUserQueryResponse, AdminUserQuery, OrganizationUserRole, PermissionEnum, uuid, UserRelations } from "@rubiconcarbon/shared-types";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import { searchStringInArray } from "@utils/helpers/general/general";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { stringComparator } from "@utils/comparators/comparator";
import useNavigation from "@hooks/use-navigation";
import { Box } from "@mui/material";
import useAuth from "@providers/auth-provider";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { USERS_PAGE_LABEL } from "@constants/pages-labels";
import { UserRoleLabel } from "@constants/user-role";
import { UserStatusInput } from "./user-status-modal";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import TableBox from "@components/ui/table-box/table-box";
import IconStatusChip from "@components/ui/status-chip/IconStatusChip";
import UserSummary from "./user-summary";
import UserStatusModal from "./user-status-modal";
import { UserData, mapUsersData } from "@models/user-data";
import OrganizationName from "@components/ui/organization-name/organization-name";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

export default function UsersTable({
  usersResponse: serverUsersResponse,
}: {
  usersResponse: AdminUserQueryResponse;
}): JSX.Element {
  const [users, setUsers] = useState<UserData[]>();
  const { pushToPath } = useNavigation();
  const [selectedUser, setSelectedUser] = useState<UserStatusInput>();
  const [isUserStatusModalOpen, setIsUserStatusModalOpen] = useState<boolean>(false);
  const { user: loginUser } = useAuth();
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const isAuthorizedUpdate: boolean = useMemo(
    () => !!loginUser?.hasPermission(PermissionEnum.ORGANIZATIONS_MANAGE_USERS),
    [loginUser],
  );

  const getStatusChip = (inputMap: Map<string, any>): JSX.Element => {
    const userStatus = inputMap.get("status");
    const isEnabled = userStatus === "enabled" ? true : false;
    const id = inputMap.get("id");
    const userName = inputMap.get("name");
    const onOpenModalHandler = (): void => {
      if (isAuthorizedUpdate) {
        setSelectedUser({
          id: uuid(id),
          userName,
          isEnabled,
        });
        setIsUserStatusModalOpen(true);
      }
    };

    return (
      <Box sx={{ cursor: isAuthorizedUpdate ? "pointer" : "auto", width: "100px" }} onClick={onOpenModalHandler}>
        <IconStatusChip isShowIcon={false} isSuccess={isEnabled} label={isEnabled ? "Enabled" : "Disabled"} />
      </Box>
    );
  };

  const columnsDef: ColDef[] = [
    {
      columnName: "name",
      displayName: "Name",
      comparator: stringComparator as any,
    },
    {
      columnName: "organizationName",
      displayName: "Organization",
      comparator: stringComparator as any,
      formatter: {
        func: (x: any): JSX.Element => (
          <OrganizationName
            organization={{ name: x?.get("organizationName"), id: x?.get("organizationId") } as any}
            style={{ fontSize: 14 }}
          />
        ),
        inputFields: ["organizationName", "organizationId"],
      },
    },
    {
      columnName: "organizationRole",
      displayName: "Roles",
      formatter: {
        func: (x: any): JSX.Element => <>{UserRoleLabel[x?.get("organizationRole") as OrganizationUserRole]}</>,
        inputFields: ["organizationRole"],
      },
    },
    {
      columnName: "email",
      displayName: "Email",
      comparator: stringComparator as any,
    },
    {
      columnName: "createdAt",
      displayName: "Create Date",
      formatter: { func: dateFormatterEST as any },
      exportFormatter: { func: dateFormatterEST as any },
    },
    {
      columnName: "status",
      displayName: "Status",
      formatter: {
        func: getStatusChip as any,
        inputFields: ["status", "id", "name"],
      },
    },
  ];

  const { data: usersResponse, trigger: refreshUsers } = useTriggerRequest<AdminUserQueryResponse,  object, object, AdminUserQuery>({
    url: "/admin/users",
    optimisticData: serverUsersResponse,
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeRelations: [UserRelations.ORGANIZATION],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch users.");
        logger.error(`Unable to fetch users. Error: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    updateBreadcrumbName?.("Users", USERS_PAGE_LABEL);
  }, [updateBreadcrumbName]);

  useEffect(() => {
    setUsers(mapUsersData(usersResponse?.data?.filter((u) => !!u?.organization)));
  }, [usersResponse]);

  const saveUserStatusHandler = (): void => {
    setIsUserStatusModalOpen(false);
    refreshUsers();
  };

  const closeModalHandler = (): void => {
    setIsUserStatusModalOpen(false);
  };

  const newUserHandler = (): void => {
    pushToPath("create-user");
  };

  const popExpandContent = (row: UserData): JSX.Element => {
    return <UserSummary user={row} />;
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = usersResponse?.data?.filter(
      (row) =>
        !!row?.organization &&
        (row.name?.toUpperCase().includes(searchString) ||
          row.email?.toUpperCase().includes(searchString) ||
          row.status?.toUpperCase().includes(searchString) ||
          searchStringInArray(searchString, row.permitRoles) ||
          searchStringInArray(searchString, row.organizationUserRoles) ||
          row.organization?.name?.toUpperCase().includes(searchString) ||
          row.createdAt ? (dateFormatterEST(row.createdAt.toString()) || "").includes(searchString) : false),
    );

    setUsers(mapUsersData(filteredData));
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <ActionButton onClickHandler={newUserHandler} requiredPermission={PermissionEnum.USERS_CREATE}>
        Create User
      </ActionButton>
    );
  };

  return (
    <>
      <TableBox>
        {users && (
          <EnhancedTable
            name={"users_info"}
            columnsDef={columnsDef}
            data={users}
            expandedContent={popExpandContent}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "createdAt", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <UserStatusModal
        user={selectedUser as UserStatusInput}
        isOpen={isUserStatusModalOpen}
        onSave={saveUserStatusHandler}
        onClose={closeModalHandler}
      />
    </>
  );
}
