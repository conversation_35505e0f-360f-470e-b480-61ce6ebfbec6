"use client";;
import { AdminOrganizationResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import UserInvite from "./user-invite";

import type { JSX } from "react";

export default function CreateUser({
  organizations,
}: {
  organizations: AdminOrganizationResponse[];
}): JSX.Element {
  return (
    <Page>
      <UserInvite organizations={organizations} />
    </Page>
  );
}
