import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, AdminOrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import CreateUser from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Create User Page
 *
 * This is a server component that renders the Create User page
 */
export default async function CreateUserPage(): Promise<JSX.Element> {
  const organizationsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationQueryResponse>(`admin/organizations`),
  );

  // Check if the result is a server error
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.USERS_READ, PermissionEnum.USERS_CREATE]}>
      <CreateUser organizations={(organizationsResponse as AdminOrganizationQueryResponse)?.data as AdminOrganizationResponse[]} />
    </AuthorizeServer>
  );
}
