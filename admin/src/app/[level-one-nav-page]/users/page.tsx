import { AuthorizeServer } from "@app/authorize-server";
import { AdminUserQueryResponse, PermissionEnum, UserRelations } from "@rubiconcarbon/shared-types";
import Users from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Users Page
 *
 * This is a server component that renders the Users page
 */
export default async function UsersPage(): Promise<JSX.Element> {
  const usersResponse = await withErrorHandling(async () => baseApiRequest<AdminUserQueryResponse>(`admin/users?${generateQueryParams({
    limit: SERVER_PAGINATION_LIMIT,
    includeRelations: [UserRelations.ORGANIZATION],
  })}`));

  // Check if the result is a server error
  if (isValidElement(usersResponse)) return usersResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.USERS_READ]}>
      <Users usersResponse={usersResponse as AdminUserQueryResponse} />
    </AuthorizeServer>
  );
}
