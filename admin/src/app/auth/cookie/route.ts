import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { AUTH_COOKIE_NAME } from "@constants/auth-constants";
import { gzipSync, gunzipSync } from "zlib";

/**
 * The function handles a POST request, retrieves data from the request body, sets a cookie with the data,
 * and returns a JSON response.
 * @param {NextRequest} req - The `req` parameter is an object that represents the incoming HTTP request.
 * It contains information about the request, such as the request method, headers, and body.
 * @returns a NextResponse object with a JSON response containing a success message and a cookie set with
 * the data from the request body.
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  const data = await req.json();

  const res = NextResponse.json({ success: true });

  res.cookies.set({
    name: AUTH_COOKIE_NAME,
    value: gzipSync(JSON.stringify(data)).toString("base64"),
    httpOnly: true,
    secure: true,
    path: "/",
    sameSite: "strict",
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });

  return res;
}

/**
 * The function retrieves a cookie value, decodes it, and returns a JSON response with the decoded data
 * if successful, or a JSON response with a status of 401 if unsuccessful.
 * @returns a JSON response with the decoded data if the cookie exists and is successfully decoded, or a
 * JSON response with a status of 401 if the cookie does not exist or is not successfully decoded.
 */
export async function GET(): Promise<NextResponse> {
  const cookie = (await cookies()).get(AUTH_COOKIE_NAME)?.value;

  try {
    const decoded = gunzipSync(Uint8Array.from(Buffer.from(cookie ?? "", "base64"))).toString();
    const data = JSON.parse(decoded);

    if (!data) {
      return NextResponse.json({ status: 401 });
    }

    return NextResponse.json({ ...data });
  } catch {
    return NextResponse.json({ status: 401 });
  }
}

/**
 * The DELETE function removes the "admin-auth-token" cookie and returns a JSON response with a success
 * message.
 * @returns a JSON response with a success message.
 */
export async function DELETE(): Promise<NextResponse> {
  const res = NextResponse.json({ success: true });

  res.cookies.set({
    name: AUTH_COOKIE_NAME,
    value: "",
    httpOnly: true,
    secure: true,
    path: "/",
    sameSite: "strict",
    maxAge: 0,
  });

  return res;
}
