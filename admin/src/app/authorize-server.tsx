"use client";

/**
 * AuthorizeServer
 *
 * This component is specifically designed to be used in Server Components that need authorization checks.
 * It wraps the client-side Authorize component to make it usable in server components.
 *
 * Usage example in a server component:
 * ```tsx
 * // In a server component (e.g., app/protected-route/page.tsx) or app/[path]/page.tsx
 * import { AuthorizeServer } from '@app/authorize-server';
 * import { PermissionEnum } from "@rubiconcarbon/shared-types";
 *
 * export default function ProtectedPage() {
 *   return (
 *     <AuthorizeServer permissions={[PermissionEnum.SOME_PERMISSION]}>
 *       <ProtectedContent />
 *     </AuthorizeServer>
 *   );
 * }
 * ```
 */

import { PropsWithChildren, type JSX } from "react";
import { Authorize } from "@app/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

export function AuthorizeServer({
  children,
  permissions,
  partiallyAuthorize = false,
  unauthorizedPage,
}: PropsWithChildren<{
  permissions: PermissionEnum[];
  partiallyAuthorize?: boolean;
  unauthorizedPage?: JSX.Element;
}>): JSX.Element {
  return (
    <Authorize permissions={permissions} partiallyAuthorize={partiallyAuthorize} unauthorizedPage={unauthorizedPage}>
      {children}
    </Authorize>
  );
}

export default AuthorizeServer;
