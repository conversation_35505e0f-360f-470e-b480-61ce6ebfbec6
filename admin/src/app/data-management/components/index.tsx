"use client";;
import { ShowIfAuthorized } from "@app/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import SyncWidget from "./sync-widget";

import type { JSX } from "react";

const DataManagement = (): JSX.Element => (
  <Page>
    <ShowIfAuthorized permissions={[PermissionEnum.DATA_MANAGEMENT_WRITE_SCIENCE]}>
      <div style={{ marginBottom: "10px" }}>
        <SyncWidget
          endpoint="reporting/sync/projects/science"
          title="Projects Sync: Science Team"
          spreadsheetId={process.env.NEXT_PUBLIC_GOOGLE_SHEETS_PROJECTS_SCIENCE ?? ""}
          spreadsheetName="Project Data Sync Sheet (Science)"
        />
      </div>
    </ShowIfAuthorized>

    <ShowIfAuthorized permissions={[PermissionEnum.DATA_MANAGEMENT_WRITE_TRADING]}>
      <div>
        <SyncWidget
          endpoint="reporting/sync/projects/trading"
          title="Projects Sync: Trading"
          spreadsheetId={process.env.NEXT_PUBLIC_GOOGLE_SHEETS_PROJECTS_TRADING ?? ""}
          spreadsheetName="Project Data Sync Sheet (Trading)"
        />
      </div>
    </ShowIfAuthorized>
  </Page>
);

export default DataManagement;
