import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import DataManagement from "./components";

import type { JSX } from "react";

/**
 * Data Management Page
 *
 * This is a server component that renders the Data Management page
 */
export default function DataManagementPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.REPORTING_DATA_MANAGEMENT]}>
      <DataManagement />
    </AuthorizeServer>
  );
}
