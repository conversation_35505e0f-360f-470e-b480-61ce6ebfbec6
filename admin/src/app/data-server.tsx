import "server-only";

import { GenericRecord, NonEmptyArray, Undefinable } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import ErrorComponent from "@app/error";
import { generateQueryParams, getUserPermissions, serverFetch } from "./libs/server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

type WithPermissionHandlingOptions = {
  check?: "some" | "every";
  ignoreError?: boolean;
};

/**
 * Generic function to fetch data with pagination and filtering
 *
 * @param endpoint The API endpoint
 * @param queryParams Query parameters for filtering and pagination
 * @returns The response data
 */
export const fetchPaginatedData = async <T, Q extends GenericRecord>(
  endpoint: string,
  queryParams: Q = {} as Q,
): Promise<Undefinable<T>> => {
  return serverFetch<T>(
    `${endpoint}?${generateQueryParams({
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    })}`,
  );
};

/**
 * Generic function to fetch a single item by ID
 *
 * @param endpoint The API endpoint
 * @param id The item ID
 * @param queryParams Additional query parameters
 * @returns The response data
 */
export const fetchItemById = async <T, Q extends GenericRecord>(
  endpoint: string,
  id: string,
  queryParams: Q = {} as Q,
): Promise<Undefinable<T>> => {
  return serverFetch<T>(`${endpoint}/${id}?${generateQueryParams(queryParams)}`);
};

/**
 * Wrapper for API calls that handles errors by returning an error component
 *
 * @param fetchFn The fetch function to execute
 * @returns The data or an error component
 */
export const withErrorHandling = async <T,>(
  fetchFn: () => Promise<Undefinable<T>>,
  handleError?: (error: any) => Undefinable<JSX.Element>,
): Promise<Undefinable<T | JSX.Element>> => {
  try {
    return await fetchFn();
  } catch (error: any) {
    // Return the error component directly
    return handleError ? handleError(error) : <ErrorComponent error={error} />;
  }
};

/**
 * Wrapper for API calls that handles permissions by returning an error component
 *
 * @param fetchFn The fetch function to execute
 * @param permissions The permissions required
 * @param check The check to perform (some or every)
 * @returns The data or an error component
 */
export const withPermissionHandling = async <T,>(
  fetchFn: () => Promise<Undefinable<T>>,
  permissions: NonEmptyArray<PermissionEnum>,
  options: WithPermissionHandlingOptions = {},
): Promise<Undefinable<T | JSX.Element>> => {
  try {
    const { check = "every", ignoreError = false } = options;
    let hasPermissions = false;
    const userPermissions = await getUserPermissions();

    if (userPermissions) hasPermissions = permissions?.[check]?.((p) => userPermissions?.includes(p));

    if (!hasPermissions) {
      if (ignoreError) return undefined;
      else throw new Error("forbidden");
    }

    return withErrorHandling(fetchFn);
  } catch (error: any) {
    // Return the error component directly
    return <ErrorComponent error={error} />;
  }
};
