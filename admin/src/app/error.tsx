"use client";

import React, { type JSX } from "react";
import { AxiosError } from "axios";
import ResourceNotAvailablePage from "@components/error/resource-unavailable";
import UnauthenticatedErrorPage from "@components/error/unauthenticated-err-page";
import { HttpStatusCodes, HttpStatusLabels } from "@constants/http";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import GenericErrorPage from "@components/error/generic-err-page";

/**
 * Error Component for App Router
 *
 * This component handles different types of errors and displays appropriate error pages.
 * It's used by the App Router's error boundary mechanism.
 */
export default function ErrorComponent({ error }: { error: Error | AxiosError | { message: string } }): JSX.Element {
  const status = error instanceof AxiosError ? error?.response?.status : null;
  const statusText = error?.message;

  const isUnauthorized =
    status === HttpStatusCodes.UNAUTHORIZED || statusText?.toLowerCase() === HttpStatusLabels.UNAUTHORIZED;
  const isForbidden = status === HttpStatusCodes.FORBIDDEN || statusText?.toLowerCase() === HttpStatusLabels.FORBIDDEN;
  const isNotFound =
    status === HttpStatusCodes.NOTFOUND ||
    statusText?.toLowerCase() === HttpStatusLabels.NOTFOUND ||
    statusText === HttpStatusLabels.NEXTNOTFOUND;

  return (
    <>
      <Maybe condition={isUnauthorized}>
        <UnauthenticatedErrorPage />
      </Maybe>
      <Maybe condition={isForbidden || isNotFound}>
        <ResourceNotAvailablePage />
      </Maybe>
      <Maybe condition={!isUnauthorized && !isForbidden && !isNotFound}>
        <GenericErrorPage />
      </Maybe>
    </>
  );
}
