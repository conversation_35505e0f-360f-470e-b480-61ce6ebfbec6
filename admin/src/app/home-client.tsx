"use client";

import React from "react";
import NestedMenuSection from "@components/nested-menu-section/nested-menu-section";
import useNavigationMenu from "@providers/navigation-menu/navigation-menu-provider";
import { Maybe } from "@rubiconcarbon/frontend-shared";

export default function HomeClient(): React.ReactElement<any> {
  const { permissibleMenus } = useNavigationMenu();

  return (
    <Maybe condition={(permissibleMenus?.length ?? 0) > 1}>
      <NestedMenuSection menus={permissibleMenus?.slice(1) || []} />
    </Maybe>
  );
}
