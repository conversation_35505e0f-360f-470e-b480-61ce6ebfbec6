import { Metadata, Viewport } from "next";
import { Providers } from "./providers";
import "@utils/reflect-metadata";

import "@/styles/globals.scss";

import type { JSX } from "react";

export const metadata: Metadata = {
  title: "Rubicon Carbon Admin",
  description: "Rubicon Carbon Admin Portal",
  other: {
    "git-commit": process.env.NEXT_PUBLIC_GIT_COMMIT_HASH || "",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1.0,
};

export default function RootLayout({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <html lang="en">
      <head>
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Sharp" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Two+Tone" rel="stylesheet" />
      </head>
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
