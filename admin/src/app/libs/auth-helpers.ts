import jwtDecode from "jwt-decode";
import { Permission } from "@rubiconcarbon/shared-types";

export interface APIAuthResponse {
  token: string;
  permissions: Permission[];
}

export class User {
  id!: string;
  email!: string;
  name!: string;
  picture!: string;
  permissions: Permission[] = [];

  hasPermission(permission: Permission): boolean {
    return this.permissions.includes(permission);
  }

  hasPermissions(permissions: Permission[]): boolean {
    return permissions.every((p) => this.hasPermission(p));
  }

  hasSomePermissions(permissions: Permission[]): boolean {
    return permissions.some((p) => this.hasPermission(p));
  }
}

export function decodeUser(data: APIAuthResponse): User | undefined {
  try {
    const decoded: any = jwtDecode(data.token);
    if (Date.now() / 1000 > (decoded.exp ?? 0)) return undefined;

    const user = Object.assign(new User(), decoded);
    return user;
  } catch {
    return undefined;
  }
}
