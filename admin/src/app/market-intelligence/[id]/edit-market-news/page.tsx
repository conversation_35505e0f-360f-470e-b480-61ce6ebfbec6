import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import EditMarketNews from "./components";

import type { JSX } from "react";

/**
 * Edit Market News Page
 *
 * This is a server component that renders the Edit Market News page
 */
export default async function EditMarketNewsPage({
  params,
}: {
  params: Promise<{ "market-report-id": string }>;
}): Promise<JSX.Element> {
  const { "market-report-id": id } = await params;
  return (
    <AuthorizeServer permissions={[PermissionEnum.MARKET_NEWS_UPDATE, PermissionEnum.MARKET_NEWS_TAG]}>
      <EditMarketNews id={id} />
    </AuthorizeServer>
  );
}
