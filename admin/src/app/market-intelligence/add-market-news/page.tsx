import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import AddMarketNews from "./components";

import type { JSX } from "react";

/**
 * Add Market News Page
 *
 * This is a server component that renders the Add Market News page
 */
export default function AddMarketNewsPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.MARKET_NEWS_UPDATE, PermissionEnum.MARKET_NEWS_TAG]}>
      <AddMarketNews />
    </AuthorizeServer>
  );
}
