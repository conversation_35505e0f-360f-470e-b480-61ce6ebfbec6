import { <PERSON><PERSON>, TextField, Typography, useMediaQuery } from "@mui/material";
import { SelectChangeEvent } from "@mui/material/Select";
import {
  AdminMarketNewsResponse,
  MarketNewsScope,
  AdminMarketNewsTagRequest,
  AdminMarketNewsUpdateRequest,
  TrimmedProjectResponse,
  AdminMarketNewsCreateRequest,
  uuid,
} from "@rubiconcarbon/shared-types";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useForm } from "react-hook-form";
import { AxiosContext } from "@providers/axios-provider";
import useSWR from "swr";
import {
  SyntheticEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type JSX,
} from "react";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { isEmpty } from "lodash";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import useNavigation from "@hooks/use-navigation";
import _without from "lodash/without";
import { useLogger } from "@providers/logging";
import ConfirmationModal from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import { DatePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import { Undefinable } from "@rubiconcarbon/frontend-shared";
import MarketIntelVisibility from "./market-intel-visibility";
import MarketIntelProjectTags from "./market-intel-project-tags";
import { MITagMessages, VisibilityTags } from "../constants/tags";
import { mapVisibility } from "../utilities/general";
import { MarketIntelligenceModel } from "../model/market-intel-model";
import { AxiosResponse } from "axios";

type EditMarketNewsProps = {
  id?: string;
};

const EditMarketNews = ({ id }: EditMarketNewsProps): JSX.Element => {
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { popFromPath } = useNavigation();
  const { logger } = useLogger();
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
  const isSmallerScreen = useMediaQuery("(max-width: 800px)");
  const isCreateMode: boolean = useMemo(() => (id ? false : true), [id]);

  const MarketIntelligenceRequestResolver = useMemo(() => classValidatorResolver(MarketIntelligenceModel), []);

  const { data: marketIntelligenceItem } = useSWR<AdminMarketNewsResponse>(
    !isCreateMode ? `/admin/market-news/${id}` : null,
    {
      revalidateOnFocus: false,
    },
  );

  const {
    handleSubmit,
    formState: { errors, isDirty },
    control,
    reset,
    watch,
  } = useForm<MarketIntelligenceModel>({
    resolver: MarketIntelligenceRequestResolver,
    mode: "onSubmit",
    defaultValues: new MarketIntelligenceModel(),
  });

  const data = watch();

  const [selectedVisibility, setSelectedVisibility] = useState<string[]>(mapVisibility(marketIntelligenceItem));
  const [selectedProjects, setSelectedProjects] = useState<TrimmedProjectResponse[]>([]);

  useEffect(() => {
    if (marketIntelligenceItem) {
      setSelectedVisibility(mapVisibility(marketIntelligenceItem));
      setSelectedProjects(marketIntelligenceItem?.projects);
      reset({
        title: marketIntelligenceItem?.header,
        source: marketIntelligenceItem?.source,
        url: marketIntelligenceItem?.url,
        articleDate: marketIntelligenceItem?.articleDate?.toString(),
        summary: marketIntelligenceItem?.summary,
      });
    }
  }, [marketIntelligenceItem, id, reset]);

  const validationMsg: string = useMemo(() => {
    if (selectedVisibility?.includes(VisibilityTags.NOT_RELEVANT) && selectedVisibility.length > 1)
      return "Not Relevant value should be a single selection";

    return "";
  }, [selectedVisibility]);

  const hasVisibilityChanged: boolean = useMemo(() => {
    if (
      selectedVisibility?.includes(VisibilityTags.CUSTOMER_PORTAL) !==
        marketIntelligenceItem?.scopes?.includes(MarketNewsScope.CUSTOMER_PORTAL) ||
      selectedVisibility?.includes(VisibilityTags.PLATFORM) !==
        marketIntelligenceItem?.scopes?.includes(MarketNewsScope.ADMIN_PLATFORM) ||
      selectedVisibility?.includes(VisibilityTags.NOT_RELEVANT) !== marketIntelligenceItem?.isIrrelevant
    ) {
      return true;
    }
    return false;
  }, [selectedVisibility, marketIntelligenceItem]);

  const hasProjectsChanged: boolean = useMemo(() => {
    if (selectedProjects?.length !== marketIntelligenceItem?.projects?.length) return true;

    selectedProjects?.forEach((project) => {
      if (marketIntelligenceItem?.projects?.find((p) => p.id === project.id)) return true;
    });

    return false;
  }, [selectedProjects, marketIntelligenceItem]);

  const handleVisibilityChange = (event: SelectChangeEvent<typeof selectedVisibility>): void => {
    const {
      target: { value },
    } = event;

    const selectedValue = typeof value === "string" ? value.split(",") : value;
    setSelectedVisibility(selectedValue);
  };

  const handleProjectsChange = (
    _: SyntheticEvent,
    newValue: TrimmedProjectResponse[],
  ): void => {
    setSelectedProjects(newValue);
  };

  const submitProjectsSelection = useCallback(
    async (id: uuid): Promise<void> => {
      if (!hasProjectsChanged || !id) return;
      if (!hasProjectsChanged || !id) return;

      const payload: AdminMarketNewsTagRequest = {
        projectIds: selectedProjects?.map((p) => p.id),
      };

      try {
        await api.put(`/admin/market-news/${id}/tags`, payload);
        enqueueSuccess(MITagMessages.PROJECT_TAGS_UPDATE_SUCCESS);
      } catch (error: any) {
        enqueueError(MITagMessages.PROJECT_TAGS_UPDATE_FAIL);
        logger.error(`${MITagMessages.PROJECT_TAGS_UPDATE_FAIL}: ${error?.message}`, {});
      }
    },
    [selectedProjects, api, enqueueError, enqueueSuccess, logger, hasProjectsChanged],
  );

  const submitEditRequest = useCallback(
    async (payload: AdminMarketNewsUpdateRequest): Promise<Undefinable<AxiosResponse<AdminMarketNewsResponse>>> => {
      try {
        const result = await api.patch<AdminMarketNewsResponse>(`/admin/market-news/${marketIntelligenceItem?.id}`, payload);
        enqueueSuccess(MITagMessages.VISIBILITY_TAGS_UPDATE_SUCCESS);
        return result;
      } catch (error: any) {
        enqueueError(MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL);
        logger.error(`${MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL}: ${error?.message}`, {});
      }
    },
    [api, enqueueSuccess, enqueueError, logger, marketIntelligenceItem?.id],
  );

  const submitCreateRequest = useCallback(
    async (payload: AdminMarketNewsCreateRequest): Promise<Undefinable<AxiosResponse<AdminMarketNewsResponse>>> => {
      try {
        const result = await api.post<AdminMarketNewsResponse>("/admin/market-news", payload);
        enqueueSuccess(MITagMessages.VISIBILITY_TAGS_UPDATE_SUCCESS);
        return result;
      } catch (error: any) {
        enqueueError(MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL);
        logger.error(`${MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL}: ${error?.message}`, {});
      }
    },
    [api, enqueueSuccess, enqueueError, logger],
  );

  const submitSelection = useCallback(async (): Promise<void> => {
    const scopes: MarketNewsScope[] = [];
    let isIrrelevant: boolean = false;

    if (hasVisibilityChanged) {
      if (selectedVisibility.includes(VisibilityTags.NOT_RELEVANT)) {
        isIrrelevant = true;
      }

      if (selectedVisibility.includes(VisibilityTags.CUSTOMER_PORTAL)) {
        scopes.push(MarketNewsScope.CUSTOMER_PORTAL);
        scopes.push(MarketNewsScope.ADMIN_PLATFORM);
      }
      if (selectedVisibility.includes(VisibilityTags.PLATFORM) && !scopes.includes(MarketNewsScope.ADMIN_PLATFORM)) {
        scopes.push(MarketNewsScope.ADMIN_PLATFORM);
      }
    }

    const payload: any = {
      isIrrelevant: hasVisibilityChanged ? isIrrelevant : undefined,
      scopes: hasVisibilityChanged ? scopes : undefined,
      summary: data.summary ?? undefined,
      header: data.title ?? undefined,
      source: data.source ?? undefined,
      url: data.url ?? undefined,
      articleDate: data.articleDate ? new Date(data.articleDate) : undefined,
    };

    let result: Undefinable<AxiosResponse<AdminMarketNewsResponse>> = undefined;
    if (isCreateMode) {
      result = await submitCreateRequest(payload);
    } else {
      result = await submitEditRequest(payload);
    }

    if (result) {
      await submitProjectsSelection(result?.data?.id);
    }
    if (isCreateMode) {
      popFromPath(1);
    } else {
      popFromPath(2);
    }
  }, [
    selectedVisibility,
    popFromPath,
    submitProjectsSelection,
    hasVisibilityChanged,
    data?.articleDate,
    data?.source,
    data?.summary,
    data?.title,
    data?.url,
    submitEditRequest,
    isCreateMode,
    submitCreateRequest,
  ]);

  const onSubmitHandler = useCallback(async (): Promise<void> => {
    if (hasVisibilityChanged && selectedVisibility.includes(VisibilityTags.CUSTOMER_PORTAL)) {
      setShowConfirmationModal(true);
    } else {
      await submitSelection();
    }
  }, [selectedVisibility, submitSelection, hasVisibilityChanged]);

  const onModalConfirmation = useCallback(async () => {
    await submitSelection();
  }, [submitSelection]);

  const handleDeleteVisibility = useCallback(
    (event: React.MouseEvent, value: string): void => {
      event.preventDefault();
      setSelectedVisibility((current) => _without(current, value));
    },
    [setSelectedVisibility],
  );

  const handleCancel = (): void => {
    if (isCreateMode) {
      popFromPath(1);
    } else {
      popFromPath(2);
    }
  };

  return (
    <>
      <form id="market-intelligence-form" onSubmit={handleSubmit(onSubmitHandler)}>
        <Stack direction="column" gap={4} sx={{ width: "100%" }}>
          <Stack mt={1} direction={isSmallerScreen ? "column" : "row"} gap={4}>
            <fieldset style={{ display: "contents" }}>
              <Controller
                name="title"
                control={control}
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <TextField
                    label="Title"
                    value={value ?? ""}
                    InputProps={{ ref }}
                    required
                    error={!!errors.title}
                    helperText={errors.title?.message}
                    {...otherProps}
                    sx={{ width: isSmallerScreen ? "100%" : "60%" }}
                  />
                )}
              />
            </fieldset>
            <fieldset style={{ display: "contents" }}>
              <Controller
                name="source"
                control={control}
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <TextField
                    label="Source"
                    value={value ?? ""}
                    InputProps={{ ref }}
                    required
                    error={!!errors.source}
                    helperText={errors.source?.message}
                    {...otherProps}
                    sx={{ width: isSmallerScreen ? "100%" : "40%" }}
                  />
                )}
              />
            </fieldset>
          </Stack>

          <Stack mt={1} direction={isSmallerScreen ? "column" : "row"} gap={4}>
            <fieldset style={{ display: "contents" }}>
              <Controller
                name="url"
                control={control}
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <TextField
                    label="Article Link"
                    value={value ?? ""}
                    InputProps={{ ref }}
                    required
                    error={!!errors.url}
                    helperText={errors.url?.message}
                    {...otherProps}
                    sx={{ width: isSmallerScreen ? "100%" : "60%" }}
                  />
                )}
              />
            </fieldset>
            <fieldset style={{ display: "contents" }}>
              <Controller
                control={control}
                name="articleDate"
                render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                  return (
                    <DatePicker
                      format="MM/DD/YYYY"
                      sx={{ width: isSmallerScreen ? "100%" : "40%" }}
                      value={value ? dayjs(value as any) : null}
                      disableFuture
                      onChange={(newValue) => onChange(newValue ? newValue.toDate() : null)}
                      slotProps={{
                        textField: {
                          label: "Date",
                          InputProps: {
                            inputRef: ref,
                          },
                          error: !!errors?.articleDate,
                          FormHelperTextProps: {
                            component: "div",
                          },
                          helperText: errors?.articleDate?.message,
                          ...otherProps,
                        },
                        popper: {
                          placement: "bottom-end",
                        },
                      }}
                    />
                  );
                }}
              />
            </fieldset>
          </Stack>

          <Stack mt={1} direction={isSmallerScreen ? "column" : "row"} gap={4}>
            <fieldset style={{ display: "contents" }}>
              <Controller
                name="summary"
                control={control}
                render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                  <TextField
                    label="Summary"
                    sx={{ width: isSmallerScreen ? "100%" : "61%" }}
                    value={value ?? ""}
                    required
                    error={!!errors.summary}
                    helperText={errors.summary?.message}
                    InputProps={{ ref, sx: { whiteSpace: "pre-wrap" } }}
                    minRows={8}
                    {...otherProps}
                    multiline
                  />
                )}
              />
            </fieldset>
            <Stack
              direction="column"
              gap={2}
              sx={{ width: isSmallerScreen ? "100%" : "40%", marginLeft: "-10px" }}
              mr={1}
            >
              <MarketIntelVisibility
                selectedVisibility={selectedVisibility}
                validationMsg={validationMsg}
                handleChange={handleVisibilityChange}
                handleDeleteVisibility={handleDeleteVisibility}
              />
              <MarketIntelProjectTags selectedProjects={selectedProjects} handleChange={handleProjectsChange} />
            </Stack>
          </Stack>
          <Stack mt={2} direction="row" justifyContent={"right"} ml="10px">
            <ActionButton
              style={{
                fontWeight: 500,
                width: "90px",
                textTransform: "capitalize",
                borderColor: "rgba(0,0,0,0.23)",
                backgroundColor: "rgba(245, 245 , 245,1)",
                color: "rgba(66, 66, 66, 1)",
                "&:hover": {
                  color: "rgba(66, 66, 66, 1)",
                  backgroundColor: "rgba(245, 245 , 245,1)",
                },
              }}
              onClickHandler={handleCancel}
            >
              Cancel
            </ActionButton>
            <ActionButton
              style={{ fontWeight: 500, width: "90px", textTransform: "capitalize" }}
              type="submit"
              isDisabled={!isEmpty(validationMsg) || (!(hasProjectsChanged || hasVisibilityChanged) && !isDirty)}
            >
              Save
            </ActionButton>
          </Stack>
        </Stack>
      </form>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        title="Please confirm"
        dialogTheme={DialogTheme.ERROR}
        dialogButtons={[
          {
            label: "Save",
            variant: "contained",
            style: { borderRadius: "4px" },
            onClickHandler: onModalConfirmation,
          },
        ]}
      >
        <Typography variant="body1" component="p">
          Are you sure you want to tag this article as available to the Customer Portal?
        </Typography>
        <Typography variant="body1" component="p">
          This article will be available for customers to review.
        </Typography>
      </ConfirmationModal>
    </>
  );
};
export default EditMarketNews;
