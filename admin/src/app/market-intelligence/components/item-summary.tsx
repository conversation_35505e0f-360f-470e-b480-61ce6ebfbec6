import { Stack, Typography } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";

import classes from "../styles/styles.module.scss";

import type { JSX } from "react";

type ItemSummaryProps = {
  summary: string;
};

const ItemSummary = ({ summary }: ItemSummaryProps): JSX.Element => {
  return (
    <Stack direction="row" gap={1}>
      <Maybe condition={!summary.length}>
        <Typography className={classes.SummaryText}>No description available.</Typography>
      </Maybe>
      <Maybe condition={!!summary.length}>
        <Stack gap={1}>
          <Typography className={classes.SummaryText} key={summary}>
            {summary}
          </Typography>
        </Stack>
      </Maybe>
    </Stack>
  );
};

export default ItemSummary;
