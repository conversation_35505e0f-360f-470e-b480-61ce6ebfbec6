import React, { SyntheticEvent, useEffect, useState, type JSX } from "react";
import { Autocomplete, Chip, FormControl, Stack, TextField, Typography } from "@mui/material";
import { TrimmedProjectResponse } from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import useS<PERSON> from "swr";
import { INITIAL_SEARCH_PHRASE } from "../constants/tags";

interface MarketIntelProjectTagsProps {
  selectedProjects: TrimmedProjectResponse[];
  handleChange: (event: SyntheticEvent, newValue: TrimmedProjectResponse[]) => void;
}

export default function MarketIntelProjectTags({
  selectedProjects,
  handleChange,
}: MarketIntelProjectTagsProps): JSX.Element {
  const [availableProjects, setAvailableProjects] = useState<TrimmedProjectResponse[]>();
  const [projectFilter, setProjectFilter] = useState<string>(INITIAL_SEARCH_PHRASE);
  const { data: projectsList } = useSWR<TrimmedProjectResponse[]>(
    projectFilter ? `/admin/projects/search?name=true&id=true&fuzzy=true&limit=50&q=${projectFilter}` : null,
  );

  useEffect(() => {
    if (projectsList) {
      setAvailableProjects(projectsList);
    }
  }, [projectsList]);

  const projectSelectionHandler = (
    event: SyntheticEvent,
    newValue: TrimmedProjectResponse[],
  ): void => {
    handleChange(event, newValue);
  };

  const onSearchChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (isEmpty(event.target.value)) {
      setProjectFilter(INITIAL_SEARCH_PHRASE);
    } else {
      setProjectFilter(event.target.value);
    }
    setProjectFilter(event.target.value);
  };

  return (
    <Stack direction="column" gap={2}>
      <FormControl sx={{ m: 1, width: "100%" }} required>
        <Autocomplete
          multiple
          freeSolo
          onChange={projectSelectionHandler as any}
          id="projects-outlined"
          value={selectedProjects}
          options={availableProjects ?? []}
          getOptionLabel={((option: TrimmedProjectResponse): string =>
            option?.registryProjectId ? `${option.registryProjectId} - ${option.name}` : "") as any
          }
          isOptionEqualToValue={(option: TrimmedProjectResponse, value: TrimmedProjectResponse) =>
            option.id === value.id
          }
          limitTags={2}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip
                label={
                  <Typography variant="body2" sx={{ fontSize: "13px" }}>
                    {option.registryProjectId}
                  </Typography>
                }
                {...getTagProps({ index })}
                key={option.id}
                style={{ height: "30px" }}
              />
            ))
          }
          filterSelectedOptions
          renderInput={(params) => (
            <TextField {...params} onChange={onSearchChangeHandler} label="Related Project" placeholder="Projects" />
          )}
        />
      </FormControl>
    </Stack>
  );
}
