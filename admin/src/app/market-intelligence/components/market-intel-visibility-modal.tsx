import React, {
  BaseSyntheticEvent,
  SyntheticEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type JSX,
} from "react";
import { AxiosContext } from "@providers/axios-provider";
import { Dialog, DialogActions, DialogContent, Stack, Typography } from "@mui/material";
import {
  AdminMarketNewsResponse,
  MarketNewsScope,
  AdminMarketNewsTagRequest,
  AdminMarketNewsUpdateRequest,
  TrimmedProjectResponse,
} from "@rubiconcarbon/shared-types";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { SelectChangeEvent } from "@mui/material/Select";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { isEmpty } from "lodash";
import _without from "lodash/without";
import { MITagMessages, VisibilityTags } from "../constants/tags";
import { useLogger } from "@providers/logging";
import ConfirmationModal from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import MarketIntelVisibility from "./market-intel-visibility";
import { mapVisibility } from "../utilities/general";
import MarketIntelProjectTags from "./market-intel-project-tags";

interface BaseDialogProps {
  isOpen: boolean;
  onClose: (event?: object, reason?: string) => void;
  onConfirm?: (message: string) => void;
  onError?: (message: string) => void;
  onWarning?: (message: string) => void;
}

interface VisibilityModalProps extends BaseDialogProps {
  item: AdminMarketNewsResponse;
  onSave: () => void;
}

export default function VisibilityModal({ isOpen, onClose, onSave, item }: VisibilityModalProps): JSX.Element {
  const [selectedVisibility, setSelectedVisibility] = useState<string[]>(mapVisibility(item));
  const [selectedProjects, setSelectedProjects] = useState<TrimmedProjectResponse[]>([]);
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);

  const handleVisibilityChange = (event: SelectChangeEvent<typeof selectedVisibility>): void => {
    const {
      target: { value },
    } = event;

    const selectedValue = typeof value === "string" ? value.split(",") : value;
    setSelectedVisibility(selectedValue);
  };

  const handleProjectsChange = (
    _: SyntheticEvent,
    newValue: TrimmedProjectResponse[],
  ): void => {
    setSelectedProjects(newValue);
  };

  useEffect(() => {
    if (item) {
      setSelectedProjects(item?.projects);
    }
  }, [item]);

  const hasVisibilityChanged: boolean = useMemo(() => {
    if (
      selectedVisibility?.includes(VisibilityTags.CUSTOMER_PORTAL) !==
        item?.scopes?.includes(MarketNewsScope.CUSTOMER_PORTAL) ||
      selectedVisibility?.includes(VisibilityTags.PLATFORM) !==
        item?.scopes?.includes(MarketNewsScope.ADMIN_PLATFORM) ||
      selectedVisibility?.includes(VisibilityTags.NOT_RELEVANT) !== item?.isIrrelevant
    ) {
      return true;
    }
    return false;
  }, [selectedVisibility, item]);

  const hasProjectsChanged: boolean = useMemo(() => {
    if (selectedProjects?.length !== item?.projects?.length) return true;

    selectedProjects?.forEach((project) => {
      if (item?.projects?.find((p) => p.id === project.id)) return true;
    });

    return false;
  }, [selectedProjects, item]);

  const submitProjectsSelection = useCallback(async (): Promise<void> => {
    if (!hasProjectsChanged) return;

    const payload: AdminMarketNewsTagRequest = {
      projectIds: selectedProjects?.map((p) => p.id),
    };

    try {
      await api.put(`/admin/market-news/${item?.id}/tags`, payload);
      enqueueSuccess(MITagMessages.PROJECT_TAGS_UPDATE_SUCCESS);
    } catch (error: any) {
      enqueueError(MITagMessages.PROJECT_TAGS_UPDATE_FAIL);
      logger.error(`${MITagMessages.PROJECT_TAGS_UPDATE_FAIL}: ${error?.message}`, {});
    }
  }, [selectedProjects, api, enqueueError, enqueueSuccess, item?.id, logger, hasProjectsChanged]);

  const submitSelection = useCallback(async (): Promise<void> => {
    let scopes: MarketNewsScope[] = [...(item?.scopes || [])];
    let isIrrelevant: boolean = item?.isIrrelevant || false;

    if (hasVisibilityChanged) {
      isIrrelevant = selectedVisibility.includes(VisibilityTags.NOT_RELEVANT);

      const hasCustomerPortal = selectedVisibility.includes(VisibilityTags.CUSTOMER_PORTAL);
      const hasPlatform = selectedVisibility.includes(VisibilityTags.PLATFORM);

      if (hasCustomerPortal && !scopes.includes(MarketNewsScope.CUSTOMER_PORTAL)) {
        scopes.push(MarketNewsScope.CUSTOMER_PORTAL);
        if (!scopes.includes(MarketNewsScope.ADMIN_PLATFORM)) {
          scopes.push(MarketNewsScope.ADMIN_PLATFORM);
        }
      } else if (!hasCustomerPortal && scopes.includes(MarketNewsScope.CUSTOMER_PORTAL)) {
        scopes = scopes.filter((scope) => scope !== MarketNewsScope.CUSTOMER_PORTAL);
      }

      if (hasPlatform && !scopes.includes(MarketNewsScope.ADMIN_PLATFORM)) {
        scopes.push(MarketNewsScope.ADMIN_PLATFORM);
      } else if (!hasPlatform && scopes.includes(MarketNewsScope.ADMIN_PLATFORM) && !hasCustomerPortal) {
        scopes = scopes.filter((scope) => scope !== MarketNewsScope.ADMIN_PLATFORM);
      }

      if (
        item?.scopes?.includes(MarketNewsScope.CORPORATE_WATCHLIST) &&
        !scopes.includes(MarketNewsScope.CORPORATE_WATCHLIST)
      ) {
        scopes.push(MarketNewsScope.CORPORATE_WATCHLIST);
      }

      scopes = scopes.filter((scope) => scope !== MarketNewsScope.PENDING_REVIEW);
    }

    const payload: AdminMarketNewsUpdateRequest = {
      isIrrelevant,
      scopes: scopes,
    };

    try {
      await api.patch(`/admin/market-news/${item?.id}`, payload);
      enqueueSuccess(MITagMessages.VISIBILITY_TAGS_UPDATE_SUCCESS);
    } catch (error: any) {
      enqueueError(MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL);
      logger.error(`${MITagMessages.VISIBILITY_TAGS_UPDATE_FAIL}: ${error?.message}`, {});
    }

    await submitProjectsSelection();
    onSave();
  }, [
    selectedVisibility,
    api,
    enqueueError,
    enqueueSuccess,
    item?.id,
    item?.isIrrelevant,
    item?.scopes,
    submitProjectsSelection,
    logger,
    hasVisibilityChanged,
    onSave,
  ]);

  const onSubmitHandler = useCallback(
    async (event: BaseSyntheticEvent): Promise<void> => {
      event.preventDefault();
      if (hasVisibilityChanged && selectedVisibility.includes(VisibilityTags.CUSTOMER_PORTAL)) {
        setShowConfirmationModal(true);
      } else {
        await submitSelection();
      }
    },
    [selectedVisibility, submitSelection, hasVisibilityChanged],
  );

  const handleDeleteVisibility = useCallback(
    (event: React.MouseEvent, value: string): void => {
      event.preventDefault();
      setSelectedVisibility((current) => _without(current, value));
    },
    [setSelectedVisibility],
  );

  const onCloselHandler = useCallback(() => {
    setSelectedVisibility([]);
    onClose();
  }, [onClose]);

  const validationMsg: string = useMemo(() => {
    if (selectedVisibility?.includes(VisibilityTags.NOT_RELEVANT) && selectedVisibility.length > 1)
      return "Not Relevant value should be a single selection";

    return "";
  }, [selectedVisibility]);

  const dialogActions = useMemo(
    () => (
      <>
        <ActionButton
          style={{
            fontWeight: 500,
            width: "90px",
            textTransform: "capitalize",
            borderColor: "rgba(0,0,0,0.23)",
            backgroundColor: "rgba(245, 245 , 245,1)",
            color: "rgba(66, 66, 66, 1)",
            "&:hover": {
              color: "rgba(66, 66, 66, 1)",
              backgroundColor: "rgba(245, 245 , 245,1)",
            },
          }}
          onClickHandler={onClose as any}
        >
          Cancel
        </ActionButton>
        <ActionButton
          style={{ fontWeight: 500, width: "90px", textTransform: "capitalize" }}
          onClickHandler={onSubmitHandler as any}
          isDisabled={!isEmpty(validationMsg) || !(hasProjectsChanged || hasVisibilityChanged)}
        >
          Save
        </ActionButton>
      </>
    ),
    [onClose, onSubmitHandler, validationMsg, hasProjectsChanged, hasVisibilityChanged],
  );

  const onModalConfirmation = useCallback(async () => {
    await submitSelection();
  }, [submitSelection]);

  return (
    <>
      <Dialog
        open={isOpen}
        onClose={onCloselHandler}
        sx={{
          background: "transparent",
          "& .MuiPaper-root": {
            boxShadow: 4,
          },
          "& .MuiBackdrop-root": {
            backgroundColor: "rgb(128, 128, 128)",
            opacity: "0.4 !important",
          },
          opacity: "1 !important",
        }}
      >
        <DialogContent sx={{ height: "250px", overflowY: "hidden", backgroundColor: "white" }}>
          <Stack gap={2} direction="column" sx={{ marginTop: -3, width: "430px" }} mr={2}>
            <MarketIntelVisibility
              selectedVisibility={selectedVisibility}
              validationMsg={validationMsg}
              handleChange={handleVisibilityChange}
              handleDeleteVisibility={handleDeleteVisibility}
            />
            <MarketIntelProjectTags selectedProjects={selectedProjects} handleChange={handleProjectsChange} />
          </Stack>
        </DialogContent>
        <DialogActions
          sx={{
            backgroundColor: "rgba(250, 250, 250, 1)",
            paddingRight: "24px",
            justifyContent: "space-between",
            border: "none",
          }}
        >
          {dialogActions}
        </DialogActions>
      </Dialog>
      <ConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        title="Please confirm"
        dialogTheme={DialogTheme.ERROR}
        dialogButtons={[
          {
            label: "Save",
            variant: "contained",
            style: { borderRadius: "4px" },
            onClickHandler: onModalConfirmation,
          },
        ]}
      >
        <Typography variant="body1" component="p">
          Are you sure you want to tag this article as available to the Customer Portal?
        </Typography>
        <Typography variant="body1" component="p">
          This article will be available for customers to review.
        </Typography>
      </ConfirmationModal>
    </>
  );
}
