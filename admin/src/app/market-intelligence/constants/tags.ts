import { MarketNewsScope } from "@rubiconcarbon/shared-types";

export enum VisibilityTags {
  CUSTOMER_PORTAL = "Customer Portal",
  PLATFORM = "Platform",
  NOT_RELEVANT = "Not Relevant",
}

export enum MITagMessages {
  PROJECT_TAGS_UPDATE_SUCCESS = "Successfully updated project tags",
  PROJECT_TAGS_UPDATE_FAIL = "Unable to update project tags",
  VISIBILITY_TAGS_UPDATE_SUCCESS = "Successfully updated visibility",
  VISIBILITY_TAGS_UPDATE_FAIL = "Unable to update project visibility",
}

export const INITIAL_SEARCH_PHRASE = "a";
export const DD_ITEM_HEIGHT = 48;
export const DD_ITEM_PADDING_TOP = 8;
export const DDMenuProps = {
  PaperProps: {
    style: {
      maxHeight: DD_ITEM_HEIGHT * 4.5 + DD_ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

export const SCOPES_MAPPING = {
  [MarketNewsScope.ADMIN_PLATFORM]: "Platform",
  [MarketNewsScope.CUSTOMER_PORTAL]: "Customer Portal",
  [MarketNewsScope.CORPORATE_WATCHLIST]: "Corporate Watchlist",
  irrelevant: "Not Relevant",
};

export enum TagType {
  portal,
  platform,
  corporate,
  project,
  irrelevant,
}

export const COLOR_MAPPING = {
  [TagType.portal]: "rgba(16, 58, 117, 1)",
  [TagType.platform]: "rgba(30, 70, 57, 1)",
  [TagType.corporate]: "rgba(24, 106, 222, 1)",
  [TagType.project]: "rgba(76, 39, 125, 1)",
  [TagType.irrelevant]: "rgba(0, 0, 0, 0.6)",
};
