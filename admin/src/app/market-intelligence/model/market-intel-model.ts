import { IsDate, <PERSON>NotEmpty, IsUrl } from "class-validator";
import { Type } from "class-transformer";

export class MarketIntelligenceModel {
  @IsNotEmpty({ message: "Required" })
  title: string;

  @IsNotEmpty({ message: "Required" })
  source: string;

  @IsNotEmpty({ message: "Required" })
  @IsUrl(undefined, { message: "Link is not valid" })
  url: string;

  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  articleDate: string = ""; // date;

  @IsNotEmpty({ message: "Required" })
  summary: string;
}
