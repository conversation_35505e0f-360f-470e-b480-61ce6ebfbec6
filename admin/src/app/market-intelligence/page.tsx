import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import MarketIntelligence from "./components";

import type { JSX } from "react";

/**
 * Market Intelligence Page
 *
 * This is a server component that renders the Market Intelligence page
 */
export default function MarketIntelligencePage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <MarketIntelligence />
    </AuthorizeServer>
  );
}
