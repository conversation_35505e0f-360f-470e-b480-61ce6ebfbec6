.Link {
  display: flex;
  column-gap: 5px;
  font-size: 14px;
  font-weight: bold;
  text-decoration: none;
  text-transform: uppercase;
  color: #094436;
  padding: 5px;
}

.ActiveTab {
  font-weight: 600;
}

.SourceFull {
  padding: 10px 10px;
}

.SourceTitle {
  font-weight: 600;
}

.SummaryText {
  font-size: 15px;
  color: rgba(0, 0, 0, 0.75);
}

.Divider {
  width: 4px;
  height: auto;
  background-color: #767676;
  border-radius: 2px;
}

.SourceLite {
  padding: 10px 10px;

  .SourceLiteTitle {
    cursor: pointer;
  }

  .SourceLiteChip {
    height: 16px;
    font-size: 0.675rem;
    color: inherit;
    border-color: inherit;
    cursor: pointer;
  }

  .SourceLiteIcon {
    font-size: medium;
  }
}

.SourceButton {
  font-size: 11px;
  padding: 0;
  border-radius: 5px;

  &:hover {
    background-color: unset;
    font-weight: bold;
  }
}

.RankText {
  font-size: 11px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.65);
  padding: 0 2px;
}

.Dialog {
  padding: 20px;
  border-radius: 10px;
}

.itemContainer {
  padding: 25px;
  border-bottom: 1px solid;
  border-color: rgba(238, 238, 238, 1);
  //overflow: scroll;
}

.irrelevantContainer {
  border: 1px solid;
  border-radius: 6px;
  padding: 4px;
  color: rgba(30, 70, 57, 1);
  border-color: rgba(30, 70, 57, 1);
}

.tabsContainer {
  border-bottom: 1px solid;
  border-color: rgba(238, 238, 238, 1);
}

.numberCircle {
  display: flex;
  width: fit-content;
  min-width: 1px;
  padding: 2px;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  font: 12px Arial, sans-serif;
  background: rgba(0, 0, 0, 0.04);
}