import { AdminMarketNewsResponse, MarketNewsScope } from "@rubiconcarbon/shared-types";
import { VisibilityTags } from "../constants/tags";

export const mapVisibility = (item?: AdminMarketNewsResponse): string[] => {
  const mapping: string[] = [];

  if (!item) return [];

  if (item.scopes?.includes(MarketNewsScope.ADMIN_PLATFORM)) {
    mapping.push(VisibilityTags.PLATFORM);
  }
  if (item.scopes?.includes(MarketNewsScope.CUSTOMER_PORTAL)) {
    mapping.push(VisibilityTags.CUSTOMER_PORTAL);
  }
  if (item?.isIrrelevant === true) {
    mapping.push(VisibilityTags.NOT_RELEVANT);
  }
  return mapping;
};
