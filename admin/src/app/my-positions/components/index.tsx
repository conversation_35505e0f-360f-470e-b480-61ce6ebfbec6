"use client";

import Page from "@components/layout/containers/page";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { SupersetDashboard } from "@components/superset-dashboard/superset-dashboard";
import { Box, Stack, Tab, Tabs } from "@mui/material";
import { useState, type JSX } from "react";
import useAuth from "@providers/auth-provider";

import classes from "../styles/my-positions.module.scss";

enum TabLabels {
  HOLDINGS_ANALYSIS = "Holdings Analysis",
  SPOT_POSITION = "Spot Position",
  INVENTORY = "Inventory",
}

interface TabsDef {
  label: TabLabels;
  permission: PermissionEnum;
  dashboard?: string;
}

const tabTypes: TabsDef[] = [
  {
    label: TabLabels.SPOT_POSITION,
    permission: PermissionEnum.REPORTING_MARKET_DATA,
    dashboard: "3df2ef6e-00a3-4a88-94df-7386ea73efbe",
  },
  {
    label: TabLabels.HOLDINGS_ANALYSIS,
    permission: PermissionEnum.REPORTING_MARKET_DATA,
    dashboard: "ec1e3344-a8a7-4788-b101-a8d203167117",
  },
  {
    label: TabLabels.INVENTORY,
    permission: PermissionEnum.REPORTING_MARKET_DATA,
    dashboard: "aa52e723-8ebc-4800-9bf8-fa8d6425e8fc",
  },
];

export default function MyPositions(): JSX.Element {
  const [selectedTab, setSelectedTab] = useState<TabsDef>(tabTypes[0]);
  const { user: loginUser } = useAuth();

  const tabChangeHandler = (newValue: TabsDef): void => {
    setSelectedTab(newValue);
  };

  const TabLabel = (props: { tab: TabsDef }): JSX.Element => {
    const { tab } = props;
    return (
      <Stack direction="row" gap={1}>
        <Box>{tab.label}</Box>
      </Stack>
    );
  };

  const renderTab = (tab: TabLabels): JSX.Element => {
    switch (tab) {
      case TabLabels.HOLDINGS_ANALYSIS:
        return (
          <SupersetDashboard
            key={selectedTab?.dashboard}
            dashboard={selectedTab?.dashboard ?? ""}
            config={{
              hideTitle: true,
              hideTab: true,
              hideChartControls: false,
              filters: { visible: false, expanded: false },
            }}
            style={{ height: "75vh" }}
          />
        );
      case TabLabels.INVENTORY:
        return (
          <SupersetDashboard
            key={selectedTab?.dashboard}
            dashboard={selectedTab?.dashboard ?? ""}
            config={{
              hideTitle: true,
              hideTab: true,
              hideChartControls: false,
              filters: { visible: false, expanded: false },
            }}
            style={{ height: "75vh" }}
          />
        );
      case TabLabels.SPOT_POSITION:
        return (
          <SupersetDashboard
            key={selectedTab?.dashboard}
            dashboard={selectedTab?.dashboard ?? ""}
            config={{
              hideTitle: true,
              hideTab: true,
              hideChartControls: false,
              filters: { visible: false, expanded: false },
            }}
            style={{ height: "75vh" }}
          />
        );
    }
  };

  return (
    <Page>
      <Stack gap={2} height={{ overflow: "hidden", height: "100%" }}>
        <Box className={classes.tabsContainer}>
          <Tabs
            sx={{ height: "30px", minHeight: "40px" }}
            value={selectedTab}
            onChange={(_, newValue) => tabChangeHandler(newValue)}
          >
            {tabTypes.map(
              (t) =>
                loginUser?.hasPermission(t.permission) && (
                  <Tab key={t.label} value={t} label={<TabLabel tab={t} />} sx={{ textTransform: "none" }} />
                ),
            )}
          </Tabs>
        </Box>
        {renderTab(selectedTab.label)}
      </Stack>
    </Page>
  );
}
