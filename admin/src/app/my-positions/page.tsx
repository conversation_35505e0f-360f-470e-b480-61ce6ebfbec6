import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import MyPositions from "./components";

import type { JSX } from "react";

/**
 * My Positions Page
 *
 * This is a server component that renders the My Positions page
 */
export default function MyPositionsPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.REPORTING_MARKET_DATA]}>
      <MyPositions />
    </AuthorizeServer>
  );
}
