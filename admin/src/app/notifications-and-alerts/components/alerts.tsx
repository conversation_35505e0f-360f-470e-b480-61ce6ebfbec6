import { useStore<PERSON>rovider } from "@providers/store-provider";
import { useMemo, type JSX } from "react";
import GroupedAlerts from "./grouped-alerts";
import { Stack, Typography } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";

const Alerts = (): JSX.Element => {
  const { ephemeralState } = useStoreProvider();

  const {
    alerts: { groups: alertsGroups },
  } = ephemeralState;

  const alerts = useMemo(() => {
    return Object.values(alertsGroups).flat();
  }, [alertsGroups]);

  return (
    <>
      <Maybe condition={!!alerts.length}>
        <Stack gap={2}>
          {alerts.map((group, index) => (
            <GroupedAlerts key={index} group={group} />
          ))}
        </Stack>
      </Maybe>
      <Maybe condition={!alerts.length}>
        <Typography variant="h6" color="GrayText">
          You have no alerts.
        </Typography>
      </Maybe>
    </>
  );
};

export default Alerts;