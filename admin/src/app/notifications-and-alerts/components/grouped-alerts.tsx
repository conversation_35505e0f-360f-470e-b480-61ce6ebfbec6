import MatIcon from "@components/ui/mat-icon/mat-icon";
import { Accordion, AccordionSummary, Stack, Typography, Badge, AccordionDetails, Box } from "@mui/material";
import { DeepReadonly } from "@rubiconcarbon/frontend-shared";
import { useState, type JSX } from "react";
import { AlertGroup } from "@providers/alert-provider/types/alert";

const GroupedAlerts = ({ group }: { group: DeepReadonly<AlertGroup> }): JSX.Element => {
  const [expanded, setExpanded] = useState(false);

  return (
    <Accordion
      elevation={0}
      disableGutters
      sx={{
        backgroundColor: "#F9F4E5",
        borderRadius: "5px",
        "&::before": {
          backgroundColor: "unset !important",
        },
      }}
      expanded={expanded}
      onChange={(): void => setExpanded(!expanded)}
    >
      <AccordionSummary>
        <Stack direction="row" gap={1} alignItems="center" justifyContent="space-between" width="100%">
          <Stack direction="row" gap={1} alignItems="center">
            <MatIcon value="error" variant="round" size={25} sx={{ color: "#EEB928" }} />
            {group.summary}
          </Stack>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography variant="body2" color="action">
              {expanded ? "Hide" : "Show"}
            </Typography>
            <Badge badgeContent={group.alerts.length} color="error" />
          </Stack>
        </Stack>
      </AccordionSummary>
      <AccordionDetails>
        <Stack gap={1}>
          {group.alerts.map((alert, index) => (
            <Box key={`${index}-${alert.type}`}>{alert.content}</Box>
          ))}
        </Stack>
      </AccordionDetails>
    </Accordion>
  );
};

export default GroupedAlerts;