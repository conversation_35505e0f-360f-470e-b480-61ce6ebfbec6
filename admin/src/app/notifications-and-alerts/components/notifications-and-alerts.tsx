import { AdminAlertQueryResponse, AdminNotificationSubscriptionQueryResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import { GenericTabs, GenericTabKey, Match } from "@rubiconcarbon/frontend-shared";
import { Stack, Typography } from "@mui/material";
import { useStoreProvider } from "@providers/store-provider";
import SubscriptiontionSettings from "./subscription-settings";
import Alerts from "./alerts";

import classes from "../styles/subscriptions.module.scss";

import type { JSX } from "react";

const Tabs = [
  {
    key: "alerts",
    data: "Alerts",
  },
  {
    key: "subscription-settings",
    data: "Notification & Alert Settings",
  },
];

const NotificationsAndAlerts = ({
  notificationSubsResponse,
  alertSubsResponse,
}: {
  notificationSubsResponse: AdminNotificationSubscriptionQueryResponse;
  alertSubsResponse: AdminAlertQueryResponse;
}): JSX.Element => {
  const { ephemeralState, updateEphemeralState } = useStoreProvider();

  const { subscriptions } = ephemeralState;
  const { viewing: tab } = subscriptions;

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={classes.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  return (
    <Stack className={classes.Container} gap={2}>
      <GenericTabs
        tabs={Tabs}
        value={tab}
        renderTab={renderTab}
        onTabChange={(key: GenericTabKey): void => updateEphemeralState("subscriptions.viewing", key)}
        classes={{
          root: classes.Tabs,
          tab: classes.Tab,
          active: classes.Active,
        }}
      />
      <Match
        value={tab}
        cases={[
          {
            case: "alerts",
            component: <Alerts />,
          },
          {
            case: "subscription-settings",
            component: <SubscriptiontionSettings notificationSubsResponse={notificationSubsResponse} alertSubsResponse={alertSubsResponse} />,
          },
        ]}
      />
    </Stack>
  );
};

export default NotificationsAndAlerts;