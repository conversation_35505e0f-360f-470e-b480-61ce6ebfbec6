import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import {
  ChangeEvent,
  Fragment,
  MouseEvent,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import SectionHeader from "./section-header";
import {
  AlertEvent,
  AdminAlertQueryResponse,
  AdminAlertSubscriptionRequest,
  NotificationCadence,
  NotificationEvent,
  AdminNotificationSubscriptionQueryResponse,
  AdminNotificationSubscriptionRequest,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import { Control, ControllerRenderProps, useController, useForm } from "react-hook-form";
import { LoadingButton } from "@mui/lab";
import {
  ALERT_SUBSCRIPTIONS_API_URL,
  NOTIFICATION_SUBSCRIPTIONS_API_URL,
  NotificationCadenceUILabel,
  NotificationEventUILabel,
} from "../constants/subscription";
import {
  SuscriptionSections,
  NotificationSectionsError,
  SubscriptionSectionType,
  SubscriptionSection,
  SubscriptionModel,
} from "../types/subscription";
import useAuth from "@providers/auth-provider";
import { Maybe, pickFromRecord, toTitleCase, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@providers/logging";
import { useGetSet } from "react-use";
import { useRefreshAlerts } from "@providers/alert-provider";

import classes from "../styles/subscription-settings.module.scss";

const SeedNotificationSectionsData: SuscriptionSections = {
  purchase: {
    label: "Customer Sales",
    subLabel: "Receive email notifications on sale-related transactions.",
    events: [NotificationEvent.PURCHASE_CREATED, NotificationEvent.PURCHASE_STATUS_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.PURCHASE_CREATED]: [PermissionEnum.CUSTOMER_SALES_READ],
      [NotificationEvent.PURCHASE_STATUS_UPDATED]: [PermissionEnum.CUSTOMER_SALES_READ],
    },
  },
  retirement: {
    label: "Retirements",
    subLabel: "Receive email notifications on retirement-related transactions.",
    events: [NotificationEvent.RETIREMENT_CREATED, NotificationEvent.RETIREMENT_STATUS_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.RETIREMENT_CREATED]: [PermissionEnum.RETIREMENTS_READ],
      [NotificationEvent.RETIREMENT_STATUS_UPDATED]: [PermissionEnum.RETIREMENTS_READ],
    },
  },
  portfolio: {
    label: "Portfolios",
    subLabel: "Receive email notifications and platform alerts on portfolio changes.",
    events: [
      NotificationEvent.BOOK_CREATED,
      NotificationEvent.BOOK_PRICE_UPDATED,
      NotificationEvent.BASKET_COMPOSITION_UPDATED,
    ],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.BOOK_CREATED]: [PermissionEnum.BOOKS_READ],
      [NotificationEvent.BOOK_PRICE_UPDATED]: [PermissionEnum.BOOKS_READ],
      [NotificationEvent.BASKET_COMPOSITION_UPDATED]: [PermissionEnum.BOOKS_READ],
    },
    alerts: [
      {
        events: [AlertEvent.PORTFOLIO_POLICY_VIOLATION, AlertEvent.PORTFOLIO_STALE_PRICE],
        eventsSelected: [],
        permissions: {
          [AlertEvent.PORTFOLIO_POLICY_VIOLATION]: [PermissionEnum.ALERTS_SUBSCRIBE_PORTFOLIO_ALERTS],
          [AlertEvent.PORTFOLIO_STALE_PRICE]: [PermissionEnum.ALERTS_SUBSCRIBE_PORTFOLIO_ALERTS],
        },
      },
    ],
  },
  project: {
    label: "Projects",
    subLabel: "Receive email notifications on project changes.",
    events: [NotificationEvent.PROJECT_CREATED, NotificationEvent.PROJECT_BUFFER_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.PROJECT_CREATED]: [PermissionEnum.PROJECTS_READ],
      [NotificationEvent.PROJECT_BUFFER_UPDATED]: [PermissionEnum.PROJECTS_READ],
    },
  },
  "org and user": {
    label: "Organizations and Users",
    subLabel: "Receive email notifications on organization and user changes.",
    events: [NotificationEvent.ORGANIZATION_CREATED, NotificationEvent.USER_CREATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.ORGANIZATION_CREATED]: [PermissionEnum.ORGANIZATIONS_READ],
      [NotificationEvent.USER_CREATED]: [PermissionEnum.USERS_READ],
    },
  },
  trade: {
    label: "Trades",
    subLabel: "Receive email notifications on trade changes.",
    events: [NotificationEvent.TRADE_CREATED, NotificationEvent.TRADE_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.TRADE_CREATED]: [PermissionEnum.TRADES_READ],
      [NotificationEvent.TRADE_UPDATED]: [PermissionEnum.TRADES_READ],
    },
  },
  book: {
    label: "Books",
    subLabel: "Receive email notifications on book changes.",
    events: [NotificationEvent.TRANSFER_EXECUTED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.TRANSFER_EXECUTED]: [PermissionEnum.BOOKS_READ],
    },
  },
  activity: {
    label: "Customer Activities",
    subLabel: "Receive email notifications on customer activity changes.",
    events: [NotificationEvent.ACTIVITY_EVENT],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.ACTIVITY_EVENT]: [PermissionEnum.USER_ACTIONS_VIEW_CUSTOMER],
    },
  },
  "model portfolio": {
    label: "Portfolio Sandbox",
    subLabel: "Receive email notifications on portfolio sandbox changes.",
    events: [NotificationEvent.MODEL_PORTFOLIO_CREATED, NotificationEvent.MODEL_PORTFOLIO_UPDATED],
    cadences: [NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.MODEL_PORTFOLIO_CREATED]: [PermissionEnum.MODEL_PORTFOLIOS_READ],
      [NotificationEvent.MODEL_PORTFOLIO_UPDATED]: [PermissionEnum.MODEL_PORTFOLIOS_READ],
    },
  },
};

const SeedNotificationSectionErrorData: NotificationSectionsError = {
  purchase: { events: false, cadences: false },
  retirement: { events: false, cadences: false },
  portfolio: { events: false, cadences: false },
  project: { events: false, cadences: false },
  "org and user": { events: false, cadences: false },
  trade: { events: false, cadences: false },
  book: { events: false, cadences: false },
  activity: { events: false, cadences: false },
  "model portfolio": { events: false, cadences: false },
};

const useSetControllerOnSections = (control: Control<SubscriptionModel>, sections: SuscriptionSections): void => {
  for (const [key, section] of Object.entries(sections)) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    section.controller = useController({ name: key as SubscriptionSectionType, control });
  }
};

export default function SubscriptionSettings({
  notificationSubsResponse: serverNotificationSubsResponse,
  alertSubsResponse: serverAlertSubsResponse,
}: { notificationSubsResponse: AdminNotificationSubscriptionQueryResponse; alertSubsResponse: AdminAlertQueryResponse }) {
  const { user: loginUser } = useAuth();
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const refreshAlerts = useRefreshAlerts();

  const [formErrors, setFormErrors] = useState<NotificationSectionsError>(SeedNotificationSectionErrorData);
  const [submittingBoth, setSubmittingBoth] = useGetSet<boolean>(false);

  const { 
    data: notificationSubsResponse, 
    trigger: refreshNotificationSubscriptions, 
    isMutating: loadingNotificationSubs, 
    error: errorLoadingNotificationSubs 
    } = useTriggerRequest<AdminNotificationSubscriptionQueryResponse>({
    url: NOTIFICATION_SUBSCRIPTIONS_API_URL,
    optimisticData: serverNotificationSubsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch notification subscriptions.");
        logger.error(`Unable to fetch notification subscriptions. Error: ${error?.message}`, {});
      },
    },
  });

  const { 
    data: alertSubscriptionsResponse, 
    trigger: refreshAlertSubscriptions,
    isMutating: loadingAlertSubs, 
    error: errorLoadingAlertSubs 
    } = useTriggerRequest<AdminAlertQueryResponse>({
    url: ALERT_SUBSCRIPTIONS_API_URL,
    optimisticData: serverAlertSubsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch alert subscriptions.");
        logger.error(`Unable to fetch alert subscriptions. Error: ${error?.message}`, {});
      },
    },
  });

  const { trigger: saveNotificationSubscriptions } = useTriggerRequest<object, AdminNotificationSubscriptionRequest>({
    url: NOTIFICATION_SUBSCRIPTIONS_API_URL,
    method: "put",
    swrOptions: {
      onSuccess: () => {
        if (!submittingBoth()) enqueueSuccess("Successfully saved notification subscriptions.");
        refreshNotificationSubscriptions();
      },
      onError: (error: any) => {
        enqueueError("Unable to save notification subscriptions.");
        logger.error(`Unable to save notification subscriptions. Error: ${error?.message}`, {});
      },
    },
  });

  const { trigger: saveAlertSubscriptions } = useTriggerRequest<object, AdminAlertSubscriptionRequest>({
    url: ALERT_SUBSCRIPTIONS_API_URL,
    method: "put",
    swrOptions: {
      onSuccess: async () => {
        if (!submittingBoth()) enqueueSuccess("Successfully saved alert subscriptions.");
        await refreshAlertSubscriptions();
        await refreshAlerts();
      },
      onError: (error: any) => {
        enqueueError("Unable to save alert subscriptions.");
        logger.error(`Unable to save alert subscriptions. Error: ${error?.message}`, {});
      },
    },
  });

  const sections = useMemo(() => {
    let updatableSections = Object.assign({}, SeedNotificationSectionsData);
    updatableSections = Object.entries(updatableSections).reduce((accum, [sectionKey, sectionValue]) => {
      let { events } = sectionValue;
      const { permissions, ...rest } = sectionValue;

      events = Object.entries(permissions)
        /**
         * currently, I check for the presence of 'all' permissions so it is an 'every' and not a 'some'
         * this might change in the future.
         */
        .filter(([, eventPermissions]) => loginUser.hasPermissions(eventPermissions))
        .map(([eventKey]) => eventKey as NotificationEvent);

      if (events.length > 0) {
        const alertSections = rest.alerts?.map((alert) => {
          const { permissions, ...rest } = alert;

          const alertEvents = Object.entries(permissions)
            .filter(([, eventPermissions]) => loginUser.hasPermissions(eventPermissions))
            .map(([eventKey]) => eventKey as AlertEvent);

          if (alertEvents.length > 0) {
            return {
              events: alertEvents,
              permissions,
              ...rest,
            };
          }

          return null;
        });

        return {
          ...accum,
          [sectionKey]: {
            events,
            permissions,
            ...{
              ...rest,
              alerts: alertSections?.filter((alert) => alert !== null),
            },
          },
        };
      }

      return accum;
    }, {} as SuscriptionSections);

    if (
      !errorLoadingNotificationSubs &&
      !loadingNotificationSubs &&
      !!notificationSubsResponse.subscriptions
    ) {
      const updateSection = (
        key: SubscriptionSectionType,
        event: NotificationEvent,
        cadence: NotificationCadence,
      ): void => {
        if (updatableSections[key]) {
          updatableSections[key] = {
            ...updatableSections[key],
            eventsSelected: Array.from(new Set([...updatableSections[key].eventsSelected, event])),
            cadencesSelected: Array.from(new Set([...updatableSections[key].cadencesSelected, cadence])),
          };
        }
      };

      for (const { event, cadence } of notificationSubsResponse.subscriptions) {
        switch (event) {
          case NotificationEvent.PURCHASE_CREATED:
          case NotificationEvent.PURCHASE_STATUS_UPDATED:
            updateSection("purchase", event, cadence);
            break;
          case NotificationEvent.RETIREMENT_CREATED:
          case NotificationEvent.RETIREMENT_STATUS_UPDATED:
            updateSection("retirement", event, cadence);
            break;
          case NotificationEvent.BOOK_CREATED:
          case NotificationEvent.BOOK_PRICE_UPDATED:
          case NotificationEvent.BASKET_COMPOSITION_UPDATED:
            updateSection("portfolio", event, cadence);
            break;
          case NotificationEvent.PROJECT_CREATED:
          case NotificationEvent.PROJECT_BUFFER_UPDATED:
            updateSection("project", event, cadence);
            break;
          case NotificationEvent.ORGANIZATION_CREATED:
          case NotificationEvent.USER_CREATED:
            updateSection("org and user", event, cadence);
            break;
          case NotificationEvent.TRADE_CREATED:
          case NotificationEvent.TRADE_UPDATED:
            updateSection("trade", event, cadence);
            break;
          case NotificationEvent.TRANSFER_EXECUTED:
            updateSection("book", event, cadence);
            break;
          case NotificationEvent.ACTIVITY_EVENT:
            updateSection("activity", event, cadence);
            break;
          case NotificationEvent.MODEL_PORTFOLIO_CREATED:
          case NotificationEvent.MODEL_PORTFOLIO_UPDATED:
            updateSection("model portfolio", event, cadence);
            break;
          default:
            break;
        }
      }
    }

    if (!errorLoadingAlertSubs && !loadingAlertSubs && !!alertSubscriptionsResponse.data) {
      const updateSection = (key: SubscriptionSectionType, event: AlertEvent): void => {
        if (updatableSections[key]) {
          updatableSections[key] = {
            ...updatableSections[key],
            alerts: updatableSections[key].alerts?.map((alert) => {
              if (alert.events.includes(event)) {
                return {
                  ...alert,
                  eventsSelected: Array.from(new Set([...alert.eventsSelected, event])),
                };
              }

              return alert;
            }),
          };
        }
      };

      for (const { event } of alertSubscriptionsResponse.data) {
        switch (event) {
          case AlertEvent.PORTFOLIO_POLICY_VIOLATION:
          case AlertEvent.PORTFOLIO_STALE_PRICE:
            updateSection("portfolio", event);
            break;
          default:
            break;
        }
      }
    }

    return updatableSections;
  }, [
    notificationSubsResponse,
    errorLoadingNotificationSubs,
    loadingNotificationSubs,
    alertSubscriptionsResponse,
    errorLoadingAlertSubs,
    loadingAlertSubs,
    loginUser,
  ]);

  const defaultValues = useMemo(
    () => ({
      purchase: pickFromRecord(sections.purchase, ["eventsSelected", "cadencesSelected", "alerts"]),
      retirement: pickFromRecord(sections.retirement, ["eventsSelected", "cadencesSelected", "alerts"]),
      portfolio: pickFromRecord(sections.portfolio, ["eventsSelected", "cadencesSelected", "alerts"]),
      project: pickFromRecord(sections.project, ["eventsSelected", "cadencesSelected", "alerts"]),
      "org and user": pickFromRecord(sections["org and user"], ["eventsSelected", "cadencesSelected", "alerts"]),
      trade: pickFromRecord(sections.trade, ["eventsSelected", "cadencesSelected", "alerts"]),
      book: pickFromRecord(sections.book, ["eventsSelected", "cadencesSelected", "alerts"]),
      activity: pickFromRecord(sections.activity, ["eventsSelected", "cadencesSelected", "alerts"]),
      "model portfolio": pickFromRecord(sections["model portfolio"], ["eventsSelected", "cadencesSelected", "alerts"]),
    }),
    [sections],
  );

  const hasFormError = useMemo(
    () => Object.values(formErrors).some(({ events, cadences }) => events || cadences),
    [formErrors],
  );

  const {
    handleSubmit,
    formState: { isDirty, isSubmitting },
    control,
    reset,
  } = useForm<SubscriptionModel>({
    mode: "onTouched",
    defaultValues,
  });

  useSetControllerOnSections(control, sections);

  useEffect(() => reset(defaultValues as any), [defaultValues, reset]);

  const RenderedSelection = useCallback((value: NotificationCadence[]): ReactNode => {
    const count = value.length || 0;
    const hasCadence = count > 0;
    const hasMultiple = hasCadence && count > 1;

    return (
      <>
        <Maybe condition={hasCadence && !hasMultiple}>{NotificationCadenceUILabel[value[0]]}</Maybe>
        <Maybe condition={hasCadence && hasMultiple}>{count} selected</Maybe>
      </>
    );
  }, []);

  const handleFormReset = (event: MouseEvent<HTMLButtonElement>): void => {
    event.preventDefault();
    reset(defaultValues as any);
    setFormErrors(SeedNotificationSectionErrorData);
  };

  const handleFrequencySelection = (
    event: SelectChangeEvent<(typeof NotificationCadence)[]>,
    field: ControllerRenderProps<SubscriptionModel, SubscriptionSectionType>,
  ): void => {
    const { value, name } = event.target;
    const { value: current, onChange } = field;

    const updatedCadences = value;
    const hasEvents = current.eventsSelected.length > 0;
    const hasCadences = updatedCadences.length > 0;

    onChange({
      ...current,
      cadencesSelected: updatedCadences,
    });

    setFormErrors((previous) => ({
      ...previous,
      [name]: {
        events: hasCadences ? !hasEvents : false,
        cadences: hasEvents ? !hasCadences : false,
      },
    }));
  };

  const handleNotificationEventCheck = (
    event: ChangeEvent<HTMLInputElement>,
    field: ControllerRenderProps<SubscriptionModel, SubscriptionSectionType>,
  ): void => {
    const { checked, value, name } = event.target;
    const { value: current, onChange } = field;

    const updatedEvents = checked
      ? [...current?.eventsSelected, value]
      : current?.eventsSelected?.filter((event) => event !== value);
    const hasEvents = updatedEvents.length > 0;
    const hasCadences = current.cadencesSelected.length > 0;

    if (checked)
      onChange({
        ...current,
        eventsSelected: updatedEvents,
      });
    else
      onChange({
        ...current,
        eventsSelected: updatedEvents,
      });

    setFormErrors((previous) => ({
      ...previous,
      [name]: {
        events: hasCadences ? !hasEvents : false,
        cadences: hasEvents ? !hasCadences : false,
      },
    }));
  };

  const handleAlertEventCheck = (
    event: ChangeEvent<HTMLInputElement>,
    field: ControllerRenderProps<SubscriptionModel, SubscriptionSectionType>,
    alertIndex: number,
  ): void => {
    const { checked, value } = event.target;
    const { value: current, onChange } = field;

    const updatedAlerts = [...(current.alerts || [])];
    const targetAlert = updatedAlerts[alertIndex];

    const sectionKey = field.name;
    const originalAlert = sections[sectionKey]?.alerts?.[alertIndex];
    const originalOrder = originalAlert?.events || [];

    if (checked) {
      const newEventsSelected = originalOrder.filter(
        (possibleEvent) => targetAlert.eventsSelected?.includes(possibleEvent) || possibleEvent === value,
      );

      updatedAlerts[alertIndex] = {
        ...targetAlert,
        eventsSelected: newEventsSelected,
      };
    } else {
      const updatedEventsSelected = (targetAlert.eventsSelected || []).filter((event) => event !== value);

      updatedAlerts[alertIndex] = {
        ...targetAlert,
        eventsSelected: updatedEventsSelected,
      };
    }

    onChange({
      ...current,
      alerts: updatedAlerts,
    });
  };

  const onSubmit = async (formData: SubscriptionModel): Promise<void> => {
    const notificationPayload: AdminNotificationSubscriptionRequest = {
      subscriptions: [],
    };

    const alertPayload: AdminAlertSubscriptionRequest = {
      subscribedEvents: [],
    };

    // Process each section's data
    for (const { eventsSelected = [], cadencesSelected = [], alerts = [] } of Object.values(formData) as Pick<
      SubscriptionSection,
      "eventsSelected" | "cadencesSelected" | "alerts"
    >[]) {
      // Handle notification subscriptions
      for (const event of eventsSelected) {
        cadencesSelected.forEach((cadence) => {
          notificationPayload.subscriptions.push({ event, cadence });
        });
      }

      // Handle alert subscriptions
      alerts?.forEach((alert) => {
        alert.eventsSelected?.forEach((event) => {
          alertPayload.subscribedEvents.push(event);
        });
      });
    }

    // Check what has changed
    const originalNotifications = notificationSubsResponse?.subscriptions || [];
    const originalAlerts = alertSubscriptionsResponse?.data || [];

    // Compare notification changes
    const notificationChanged =
      notificationPayload.subscriptions.length !== originalNotifications.length ||
      !notificationPayload.subscriptions.every((sub) =>
        originalNotifications.some((orig) => orig.event === sub.event && orig.cadence === sub.cadence),
      );

    // Compare alert changes
    const alertChanged =
      alertPayload.subscribedEvents.length !== originalAlerts.length ||
      !alertPayload.subscribedEvents.every((sub) => originalAlerts.some((orig) => orig.event === sub));

    // Determine what to submit
    const shouldSaveNotifications = notificationChanged;
    const shouldSaveAlerts = alertChanged;
    const shouldSaveBoth = shouldSaveNotifications && shouldSaveAlerts;

    try {
      if (shouldSaveBoth) {
        setSubmittingBoth(true);

        await Promise.all([
          saveNotificationSubscriptions({ requestBody: notificationPayload }),
          saveAlertSubscriptions({ requestBody: alertPayload }),
        ]);
        enqueueSuccess("Successfully saved notification and alerts subscriptions.");
      } else if (shouldSaveNotifications) {
        await saveNotificationSubscriptions({ requestBody: notificationPayload });
      } else if (shouldSaveAlerts) {
        await saveAlertSubscriptions({ requestBody: alertPayload });
      }
    } catch (error: any) {
      enqueueError("Unable to save subscriptions.");
      logger.error(`Unable to save subscriptions. Error: ${error?.message}`, {});
    } finally {
      setSubmittingBoth(false);
    }
  };

  return (
    <Box className={classes.Container}>
    <form id="subscription-settings" onSubmit={handleSubmit(onSubmit)}>
        <Grid className={classes.Content} container direction="column" gap={3}>
        {/* Main Header */}
        <Grid className={classes.SectionHeader} item>
            <Typography className={classes.DescriptionLabel}>
            Select the email notifications and alerts you want to receive.
            </Typography>
        </Grid>
        {/*Notification Content */}
        <Grid item container direction="column" gap={1}>
            {Object.entries(sections).map(([key, { label, subLabel, events, cadences, controller, alerts }]) => {
            const { field } = controller ?? {};
            const { value } = field ?? {};
            const { eventsSelected, cadencesSelected } = value ?? {};

            return (
                <Fragment key={key}>
                {/* Section */}
                <Grid item container direction="column" gap={2}>
                    {/* Section Header */}
                    <SectionHeader label={label} subLabel={subLabel} />
                    {/* Section Content */}
                    <Grid item container gap={2}>
                    {/* Frequencies */}
                    <Grid item xs={12} container direction="column" sm={4} md={5} gap={2}>
                        <Typography variant="body2" fontWeight={100} color="black">
                        Notification Frequency
                        </Typography>
                        <FormControl className={classes.Frequency} error={!!formErrors[key as SubscriptionSectionType]?.cadences}>
                        <InputLabel>Frequency</InputLabel>
                        <Select
                            multiple
                            {...field}
                            label="Frequency"
                            renderValue={RenderedSelection as any}
                            value={cadencesSelected ?? []}
                            onChange={(event: SelectChangeEvent<(typeof NotificationCadence)[]>) =>
                            handleFrequencySelection(event, field)
                            }
                        >
                            {cadences.map((cadence) => (
                            <MenuItem key={cadence} value={cadence}>
                                <Checkbox checked={cadencesSelected?.includes(cadence)} />
                                <ListItemText primary={NotificationCadenceUILabel[cadence]} />
                            </MenuItem>
                            ))}
                        </Select>
                        <Maybe condition={!!formErrors[key as SubscriptionSectionType]?.cadences}>
                            <FormHelperText error>At least one frequency selection required</FormHelperText>
                        </Maybe>
                        </FormControl>
                    </Grid>
                    {/* Events */}
                    <Grid item xs={12} container direction="column" sm={6} md={5} gap={1}>
                        <Typography variant="body2" fontWeight={100} color="black">
                        Notification{events?.length > 1 ? "s" : ""}
                        </Typography>
                        <FormGroup className={classes.CheckboxGroup}>
                        {events.map((event) => (
                            <FormControlLabel
                            key={event}
                            control={
                                <Checkbox
                                {...field}
                                value={event}
                                checked={eventsSelected?.includes(event)}
                                onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                    handleNotificationEventCheck(event, field)
                                }
                                />
                            }
                            label={NotificationEventUILabel[event]}
                            />
                        ))}
                        <Maybe condition={!!formErrors[key as SubscriptionSectionType]?.events}>
                            <FormHelperText error>At least one event selection required</FormHelperText>
                        </Maybe>
                        </FormGroup>
                    </Grid>
                    </Grid>
                    <Maybe condition={!!alerts?.length}>
                    <Grid item xs={12} sm={4} md={5}>
                        {alerts?.map((alert, index) => {
                        const { events, label } = alert;

                        return (
                            <Grid key={index} item container gap={2}>
                            <Grid item xs={12} sm={4} md={5}></Grid>
                            <Grid item container direction="column" xs={12} sm={6} md={5} gap={0.5}>
                                <Divider sx={{ marginBottom: "10px" }} />
                                <Typography variant="body2" fontWeight={100} color="black">
                                {label || `Alert${events?.length > 1 ? "s" : ""}`}
                                </Typography>
                                <FormGroup className={classes.CheckboxGroup}>
                                {events.map((event) => (
                                    <FormControlLabel
                                    key={event}
                                    control={
                                        <Checkbox
                                        value={event}
                                        checked={value?.alerts?.[index]?.eventsSelected?.includes(event)}
                                        onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                            handleAlertEventCheck(event, field, index)
                                        }
                                        />
                                    }
                                    label={toTitleCase(event)}
                                    />
                                ))}
                                </FormGroup>
                            </Grid>
                            </Grid>
                        );
                        })}
                    </Grid>
                    </Maybe>
                </Grid>
                <Divider />
                </Fragment>
            );
            })}
        </Grid>
        {/* Action Buttons */}
        <Grid className={classes.Actions} item container justifyContent="center" gap={2}>
            <Button className={classes.TextButton} variant="text" onClick={handleFormReset} disabled={!isDirty}>
            reset
            </Button>
            <LoadingButton
            variant="contained"
            type="submit"
            form="subscription-settings"
            loading={isSubmitting}
            disabled={!isDirty || (isDirty && hasFormError) || isSubmitting}
            >
            save
            </LoadingButton>
        </Grid>
        </Grid>
    </form>
    </Box>
  );
}