import { NotificationCadence, NotificationEvent } from "@rubiconcarbon/shared-types";

export const NOTIFICATION_SUBSCRIPTIONS_API_URL = "admin/notifications/subscriptions";
export const ALERT_SUBSCRIPTIONS_API_URL = "admin/alerts/subscriptions";

export const NotificationEventUILabel: Partial<Record<NotificationEvent, string>> = {
  [NotificationEvent.PURCHASE_CREATED]: "Customer sale created",
  [NotificationEvent.PURCHASE_STATUS_UPDATED]: "Customer sale status updated",
  [NotificationEvent.RETIREMENT_CREATED]: "Retirement created",
  [NotificationEvent.RETIREMENT_STATUS_UPDATED]: "Retirement status updated",
  [NotificationEvent.BOOK_CREATED]: "New portfolio created",
  [NotificationEvent.BOOK_PRICE_UPDATED]: "Portfolio price updated",
  [NotificationEvent.BASKET_COMPOSITION_UPDATED]: "Portfolio composition updated",
  [NotificationEvent.PROJECT_CREATED]: "New project created",
  [NotificationEvent.PROJECT_BUFFER_UPDATED]: "Project buffer updated",
  [NotificationEvent.ORGANIZATION_CREATED]: "New organization created",
  [NotificationEvent.USER_CREATED]: "New user created",
  [NotificationEvent.TRADE_CREATED]: "New trade created",
  [NotificationEvent.TRADE_UPDATED]: "Trade updated",
  [NotificationEvent.TRANSFER_EXECUTED]: "Book transfer executed",
  [NotificationEvent.ACTIVITY_EVENT]: "New activity",
  [NotificationEvent.MODEL_PORTFOLIO_CREATED]: "Portfolio sandbox created",
  [NotificationEvent.MODEL_PORTFOLIO_UPDATED]: "Portfolio sandbox updated",
};

export const NotificationCadenceUILabel = {
  [NotificationCadence.REALTIME]: "Real-time",
  [NotificationCadence.DAILY]: "Daily summary",
  [NotificationCadence.WEEKLY]: "Weekly summary",
};
