import { uuid } from "@rubiconcarbon/shared-types";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { useState, type JSX } from "react";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import integerFormat from "@utils/formatters/integer-format";

interface VintageLiquidity {
  id: number;
  index: number;
  year: string;
  cancellations: number;
  issuances: number;
  retirements: number;
  surplus: number;
}
export default function ProjectLiquidity(props: { projectId: uuid }): JSX.Element {
  const { projectId } = props;
  const [vintagesLiquidity, setVintagesLiquidity] = useState<VintageLiquidity[]>();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const columnsDef: ColDef[] = [
    {
      columnName: "year",
      displayName: "Vintage Year",
    },
    {
      columnName: "issuances",
      displayName: "Issued",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
    {
      columnName: "retirements",
      displayName: "Retired",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
    {
      columnName: "cancellations",
      displayName: "Canceled",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
    {
      columnName: "surplus",
      displayName: "In Circulation",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
  ];

  const { data: projectLiquidityResponse } = useRequest<VintageLiquidity[]>({
    url: `/reporting/market-data/liquidity`,
    queryParams: {
      project_id: projectId,
    },
    swrOptions: {
      onSuccess: (data: VintageLiquidity[]) =>
        setVintagesLiquidity(
          data?.map((x) => {
            return { id: x.index, ...x };
          }),
        ),
      onError: (error: any) => {
        enqueueError("Unable to fetch project liquidity data.");
        logger.error(
          `Unable to fetch project liquidity data for project id: ${projectId}. Error: ${error?.message}`,
          {},
        );
      },
    },
  });

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = projectLiquidityResponse
      ?.map((x: VintageLiquidity) => {
        return { id: x.index, ...x };
      })
      .filter((row) => row?.year?.toString()?.toUpperCase().includes(searchString));
    setVintagesLiquidity(filteredData);
  };

  return (
    <>
      <TableBox>
        {vintagesLiquidity && (
          <EnhancedTable
            name={"project_liquidity"}
            columnsDef={columnsDef}
            exportable={true}
            data={vintagesLiquidity}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            defaultSort={{ columnName: "year", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
    </>
  );
}
