import { uuid } from "@rubiconcarbon/shared-types";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { useState, type JSX } from "react";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import integerFormat from "@utils/formatters/integer-format";
import dateFormatterEST from "@utils/formatters/est-date-formatter";

interface ProjectRetirement {
  id: number;
  index: number;
  transaction_date: Date;
  retirement_beneficiary: string;
  credits_quantity: number;
  vintage_year: number;
  registry_retiree: string;
  registry_retirement_details: string;
}
export default function ProjectMarketRetirements(props: { projectId: uuid }): JSX.Element {
  const { projectId } = props;
  const [projectRetirements, setProjectRetirements] = useState<ProjectRetirement[]>();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const columnsDef: ColDef[] = [
    {
      columnName: "transaction_date",
      displayName: "Date",
      formatter: { func: (input: any) => dateFormatterEST(input) },
      exportFormatter: { func: (input: any) => dateFormatterEST(input) },
    },
    {
      columnName: "retirement_beneficiary",
      displayName: "Beneficiary",
    },
    {
      columnName: "registry_retiree",
      displayName: "Retiree",
    },
    {
      columnName: "registry_retirement_details",
      displayName: "Retiree Details",
    },
    {
      columnName: "credits_quantity",
      displayName: "Qty",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
    {
      columnName: "vintage_year",
      displayName: "Vintage",
    },
  ];

  const { data: projectRetirementsResponse } = useRequest<ProjectRetirement[]>({
    url: `/reporting/market-data/retirements`,
    queryParams: {
      project_id: projectId,
    },
    swrOptions: {
      onSuccess: (data: ProjectRetirement[]) =>
        setProjectRetirements(
          data?.map((x) => {
            return { id: x.index, ...x };
          }),
        ),
      onError: (error: any) => {
        enqueueError("Unable to fetch project retirements data.");
        logger.error(
          `Unable to fetch project retirements data for project id: ${projectId}. Error: ${error?.message}`,
          {},
        );
      },
    },
  });

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = projectRetirementsResponse
      ?.map((x: ProjectRetirement) => {
        return { id: x.index, ...x };
      })
      .filter(
        (row) =>
          row?.retirement_beneficiary?.toString()?.toUpperCase().includes(searchString) ||
          row?.credits_quantity?.toString()?.toUpperCase().includes(searchString) ||
          row?.registry_retiree?.toString()?.toUpperCase().includes(searchString) ||
          row?.registry_retirement_details?.toString()?.toUpperCase().includes(searchString) ||
          row?.vintage_year?.toString()?.toUpperCase().includes(searchString),
      );
    setProjectRetirements(filteredData);
  };

  return (
    <>
      <TableBox>
        {projectRetirements && (
          <EnhancedTable
            name={"project_market_retirements"}
            columnsDef={columnsDef}
            exportable={true}
            data={projectRetirements}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            defaultSort={{ columnName: "transaction_date", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
    </>
  );
}
