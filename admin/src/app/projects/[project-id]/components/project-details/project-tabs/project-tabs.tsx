import { AdminProjectResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { useEffect, useMemo, type JSX } from "react";
import { GenericTabItem, GenericTabKey, GenericTabs, Match } from "@rubiconcarbon/frontend-shared";
import { Stack, Typography } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import useAuth from "@providers/auth-provider";
import { useStoreProvider } from "@providers/store-provider";
import ProjectTransactions from "./project-transactions";
import ProjectSandbox from "./project-sandbox";
import ProjectLiquidity from "./project-liquidity";
import ProjectMarketRetirements from "./project-market-retirements";
import BidAskSection from "@app/[level-one-nav-page]/bid-ask/components/bid-ask";
import { CustomerTableTabsTypes } from "@app/[level-one-nav-page]/organizations/[organization-id]/components/customer-details";

import classes from "../../../styles/project-details.module.scss";

export default function ProjectTabs(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  const { user } = useAuth();
  const { ephemeralState, updateEphemeralState } = useStoreProvider();
  const { customer: customerTabState } = ephemeralState;
  const { viewing: tab } = customerTabState;

  const hasTransactionsPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRANSACTIONS_READ), [user]);
  const hasCustomerQuotesPermission = useMemo(() => user?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_READ), [user]);
  const hasBidAskPermission = useMemo(() => user?.hasPermission(PermissionEnum.REPORTING_MARKET_DATA), [user]);

  const tabs = useMemo(
    () =>
      [
        hasTransactionsPermission
          ? {
              key: "transactions",
              data: "Transactions",
            }
          : null,
        hasBidAskPermission
          ? {
              key: "bidAsk",
              data: "Bid / Ask",
            }
          : null,
        hasCustomerQuotesPermission
          ? {
              key: "quotes",
              data: "Customer Quotes",
            }
          : null,
        hasBidAskPermission
          ? {
              key: "retirements",
              data: "Market Retirements",
            }
          : null,
        hasBidAskPermission
          ? {
              key: "liquidity",
              data: "Liquidity",
            }
          : null,
      ].filter((entry) => !!entry),
    [hasTransactionsPermission, hasCustomerQuotesPermission, hasBidAskPermission],
  ) as GenericTabItem<CustomerTableTabsTypes, string>[];

  useEffect(() => {
    updateEphemeralState("customer.viewing", "transactions");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={classes.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  return (
    <Stack>
      <GenericTabs
        tabs={tabs}
        value={tab}
        renderTab={renderTab}
        onTabChange={(key: GenericTabKey): void => updateEphemeralState("customer.viewing", key)}
        classes={{
          root: classes.Tabs,
          tab: classes.Tab,
          active: classes.Active,
        }}
      />
      <Match
        value={tab}
        cases={[
          {
            case: "transactions",
            component: <ProjectTransactions project={project} />,
          },
          {
            case: "bidAsk",
            component: <BidAskSection projectId={project?.id} />,
          },
          {
            case: "quotes",
            component: <ProjectSandbox projectId={project?.id} />,
          },
          {
            case: "retirements",
            component: <ProjectMarketRetirements projectId={project?.id} />,
          },
          {
            case: "liquidity",
            component: <ProjectLiquidity projectId={project?.id} />,
          },
        ]}
      />
    </Stack>
  );
}
