import {
  AdminProjectResponse,
  OrderByDirection,
  PermissionEnum,
  TransactionOrderByOptions,
  AdminTransactionQueryResponse,
  AdminTransactionResponse,
  TransactionType,
  AdminAssetFlowResponse,
} from "@rubiconcarbon/shared-types";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import { useLogger } from "@providers/logging";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { useContext, useEffect, useMemo, useState, type JSX } from "react";
import { AuthContext } from "@providers/auth-provider";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import { capitalize, isEmpty } from "lodash";
import currencyFormat from "@utils/formatters/currency-format";
import integerFormat from "@utils/formatters/integer-format";
import StatusChip from "@components/ui/status-chip/StatusChip";

export default function ProjectTransactions(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;
  const [projectTransactions, setProjectTransactions] = useState<AdminTransactionResponse[]>([]);
  const { user } = useContext(AuthContext);
  const { logger } = useLogger();
  const hasSalesPermission = useMemo(() => user?.hasPermission(PermissionEnum.CUSTOMER_SALES_READ), [user]);
  const hasTradesPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRADES_READ), [user]);
  const projectVintages = useMemo(() => project?.projectVintages?.map((pv) => pv?.id) ?? [], [project]);

  const columnsDef: ColDef[] = [
    {
      columnName: "updatedAt",
      displayName: "Updated Date",
      formatter: { func: (input: any) => dateFormatterEST(input) },
      exportFormatter: { func: (input: any) => dateFormatterEST(input) },
    },
    {
      columnName: "uiKey",
      displayName: "Transaction Key",
    },
    {
      columnName: "assetFlows",
      displayName: "Vintage",
      formatter: { func: (input: any) => {
        const af = input as AdminAssetFlowResponse[];
        return !isEmpty(af) ? af[0]?.asset?.projectVintageName : MISSING_DATA;
      } },
      exportFormatter: { func: (input: any) => {
        const af = input as AdminAssetFlowResponse[];
        return !isEmpty(af) ? af[0]?.asset?.projectVintageName : MISSING_DATA;
      } },
    },
    {
      columnName: "counterpartyName",
      displayName: "Counterparty",
    },
    {
      columnName: "type",
      displayName: "Type",
      formatter: { func: (input: any) => capitalize(input as string) },
      exportFormatter: { func: (input: any) => capitalize(input as string) },
    },
    {
      columnName: "totalQuantity",
      displayName: "Quantity",
      formatter: { func: (input: any) => integerFormat(input) },
      exportFormatter: { func: (input: any) => integerFormat(input) },
    },
    {
      columnName: "totalPrice",
      displayName: "Price",
      formatter: { func: (input: any) => currencyFormat(input as number) },
      exportFormatter: { func: (input: any) => currencyFormat(input as number) },
    },
    {
      columnName: "status",
      displayName: "Status",
      formatter: { func: (input: any) => <StatusChip status={input as string} /> },
      exportFormatter: { func: (input: any) => capitalize(input as string) },
    },
  ];

  const { data: transactionsResponse, trigger: getTransactions } = useTriggerRequest<AdminTransactionQueryResponse>({
    url: "/admin/transactions",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      assetIds: project?.projectVintages?.map((pv) => pv?.id),
      types: [
        hasSalesPermission ? TransactionType.PURCHASE : null,
        hasTradesPermission ? TransactionType.TRADE : null,
      ]?.filter((entry) => !!entry),
      orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
    },
    swrOptions: {
      onSuccess: (data: AdminTransactionQueryResponse) => setProjectTransactions(data?.data),
      onError: (error: any) => {
        setProjectTransactions([]);
        logger.error(`Unable to fetch sale and trade transactions. Error: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    if (projectVintages?.length > 0) {
      getTransactions();
    } else {
      setProjectTransactions([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectVintages]);

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = transactionsResponse?.data.filter(
      (row) =>
        row?.updatedAt?.toString()?.toUpperCase().includes(searchString) ||
        row?.uiKey?.toUpperCase().includes(searchString) ||
        row?.counterpartyName?.toUpperCase().includes(searchString) ||
        row?.type?.toUpperCase().includes(searchString) ||
        row?.totalPrice?.toString()?.toUpperCase().includes(searchString) ||
        row?.totalQuantity?.toString()?.toUpperCase().includes(searchString) ||
        row?.status?.toUpperCase().includes(searchString) ||
        row?.assetFlows?.[0]?.asset?.projectVintageName?.toUpperCase().includes(searchString),
    );
    setProjectTransactions(filteredData);
  };

  return (
    <TableBox>
      <EnhancedTable
        name={"project_transactions"}
        columnsDef={columnsDef}
        exportable={true}
        data={projectTransactions}
        rowsCountPerPage={100}
        getFilteredData={getFilteredData}
        defaultSort={{ columnName: "updatedAt", order: SortOrder.DESC }}
      />
    </TableBox>
  );
}
