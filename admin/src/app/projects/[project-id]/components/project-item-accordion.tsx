import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";
import { Grid, Typography } from "@mui/material";
import ExpandMore from "@mui/icons-material/ExpandMore";
import { Remark } from "react-remark";
import { isEmpty } from "lodash";
import ProjectSDGRow from "./project-sdg-row";
import COLORS from "@components/ui/theme/colors";

import type { JSX } from "react";

const accordionStyle = {
  border: `1px solid ${COLORS.lightGrey}`,
  borderBottom: "none",
  boxShadow: "none",
  "&:first-of-type": {
    borderRadius: 0,
  },
  "&:last-of-type": {
    borderRadius: 0,
    borderBottom: `1px solid ${COLORS.lightGrey}`,
  },
  "&:before": {
    height: 0,
  },
};
const accordionSummaryStyle = {
  backgroundColor: COLORS.whiteGrey,
  flexDirection: "row-reverse",
  gap: "1rem",
  "&:hover": {
    backgroundColor: COLORS.lightGrey,
  },
};

export default function ProjectItemAccordion(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  if (!project) return <p>No Data</p>;

  return (
    <>
      {project.projectDescription && (
        <Accordion disableGutters sx={accordionStyle}>
          <AccordionSummary
            sx={accordionSummaryStyle}
            expandIcon={<ExpandMore />}
            id="projectDescriptionAccordion"
            aria-controls="projectDescriptionAccordionDetails"
          >
            <Typography variant="body1" component="h4">
              Project description
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography component="p" variant="body2">
              {project.projectDescription}
            </Typography>
          </AccordionDetails>
        </Accordion>
      )}
      {project.additionalityBlurb && (
        <Accordion disableGutters sx={accordionStyle}>
          <AccordionSummary
            sx={accordionSummaryStyle}
            expandIcon={<ExpandMore />}
            id="additionalityBriefAccordion"
            aria-controls="additionalityBriefAccordionDetails"
          >
            <Typography variant="body1" component="h4">
              Additionality brief
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {project?.additionalityBlurb && (
              <Typography component="span" variant="body2">
                <Remark>{project?.additionalityBlurb}</Remark>
              </Typography>
            )}
          </AccordionDetails>
        </Accordion>
      )}
      {project.permanenceBlurb && (
        <Accordion disableGutters sx={accordionStyle}>
          <AccordionSummary
            sx={accordionSummaryStyle}
            expandIcon={<ExpandMore />}
            id="permanenceBriefAccordion"
            aria-controls="permanenceBriefAccordionDetails"
          >
            <Typography variant="body1" component="h4">
              Permanence brief
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {project?.permanenceBlurb && (
              <Typography component="span" variant="body2">
                <Remark>{project?.permanenceBlurb}</Remark>
              </Typography>
            )}
          </AccordionDetails>
        </Accordion>
      )}
      {(!isEmpty(project.projectSDGs) || !isEmpty(project.otherCoBenefitsBlurb)) && (
        <Accordion disableGutters sx={accordionStyle}>
          <AccordionSummary
            sx={accordionSummaryStyle}
            expandIcon={<ExpandMore />}
            id="cobenefitsBriefAccordion"
            aria-controls="cobenefitsBriefAccordionDetails"
          >
            <Typography variant="body1" component="h4">
              Co-benefits brief
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="h5" sx={{ color: COLORS.rubiconGreen, mb: "2rem" }} component="h5">
              Co-benefits
            </Typography>
            {project.projectSDGs ? (
              <Typography component="span" variant="body2">
                {project.projectSDGs.map((sdg) => (
                  <ProjectSDGRow key={sdg.id} projectSDG={sdg} />
                ))}
              </Typography>
            ) : (
              "N/A"
            )}
            <Grid>
              <Typography variant="h5" sx={{ color: COLORS.rubiconGreen, mb: "2rem" }} component="h5">
                Other Co-benefits
              </Typography>
              <Typography component="span" variant="body2">
                {project.otherCoBenefitsBlurb ? <Remark>{project?.otherCoBenefitsBlurb}</Remark> : "N/A"}
              </Typography>
            </Grid>
          </AccordionDetails>
        </Accordion>
      )}
    </>
  );
}
