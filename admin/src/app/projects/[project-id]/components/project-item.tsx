import { useEffect, type JSX } from "react";
import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import ProjectDetails from "./project-details/project-details";
import { Maybe } from "@rubiconcarbon/frontend-shared";

export default function ProjectItem({ project }: { project: AdminProjectResponse }): JSX.Element {
  const { updateBreadcrumbName } = useBreadcrumbs();

  useEffect(() => {
    if (project) updateBreadcrumbName?.("Project Details", project.name);
  }, [project, updateBreadcrumbName]);

  return (
    <>
      <Maybe condition={!project}>
        <p>No Data</p>
      </Maybe>
      <Maybe condition={!!project}>
        <ProjectDetails project={project} />
      </Maybe>
    </>
  );
}
