import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import { TrimmedProjectSdgResponse as ProjectSDG } from "@rubiconcarbon/shared-types";
import { Remark } from "react-remark";
import { SDGIconMap } from "@constants/sdg-icon-map";
import Image from "next/image";

import type { JSX } from "react";

function ProjectSDGRow(props: { projectSDG: ProjectSDG }): JSX.Element {
  const { projectSDG } = props;
  return (
    <Grid container sx={{ mb: "2rem" }}>
      <Image height="80" src={SDGIconMap.get(projectSDG.sdgTypeId) ?? ""} alt="Rubicon Carbon" priority />
      <Grid container direction="column" flex={1} sx={{ ml: "2rem" }}>
        <Typography variant="body1" component="h4" sx={{ fontWeight: 500 }}>
          {projectSDG.sdgType.title}
        </Typography>
        <Typography variant="body2">
          <Remark>{projectSDG.sdgDescriptionBlurb ?? ""}</Remark>
        </Typography>
      </Grid>
    </Grid>
  );
}

export default ProjectSDGRow;
