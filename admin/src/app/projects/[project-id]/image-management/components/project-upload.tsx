import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Box, Button, capitalize, Divider, IconButton, Paper, Stack, Typography } from "@mui/material";
import { ProjectOrderByOption, AdminProjectQueryResponse, AdminProjectQuery } from "@rubiconcarbon/shared-types";
import { generateQueryParams, Maybe, Nullable, px, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  Dispatch,
  MouseEvent,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type JSX,
} from "react";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";
import { FileUploadHandlers } from "@uitypes/headless-downloader";
import { LoadingButton } from "@mui/lab";
import useNavigation from "@hooks/use-navigation";
import { AxiosContext } from "@providers/axios-provider";
import Img from "@components/ui/img/img";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { useGetSet, useToggle } from "react-use";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { AxiosError } from "axios";
import { IHookStateSetAction } from "react-use/lib/misc/hookState";
import { ProjectImageType } from "../types/project-image";
import { ProjectImageTypeToAspectRatio, ProjectImageTypeToDimensions } from "../constants/project-image-constants";

import classes from "../styles/project-upload.module.scss";

type UploadedProps = {
  type: ProjectImageType;
  url?: string;
  removeFile: (type: ProjectImageType) => Promise<boolean>;
};

type UploadItemProps = {
  projectId: string;
  type: ProjectImageType;
  allowedDimension: [number, number];
  aspectRatio: [number, number];
  url?: string;
  refreshProject: () => Promise<void>;
  setUploadHandlers: Dispatch<SetStateAction<FileUploadHandlers>>;
  setUploadings: Dispatch<SetStateAction<Record<string, boolean>>>;
  setUploadErrors: Dispatch<IHookStateSetAction<Record<ProjectImageType, boolean>>>;
  handleExposeUploadHandler: (
    inputId: string,
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ) => void;
};

type ProjectUploadProps = {
  projectsResponse: AdminProjectQueryResponse;
};

const Uploaded = ({ type, url, removeFile }: UploadedProps): JSX.Element => {
  const [open, setOpen] = useToggle(false);

  return (
    <Stack
      component={Paper}
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      padding={3}
      margin="10px 0"
    >
      <Stack direction="row" gap={1}>
        <Box border="solid 0.5px lightgray" borderRadius="5px">
          <Img source={url} alt={`${capitalize(type)}ProjectImage`} size={50} />
        </Box>
      </Stack>
      <IconButton sx={{ width: 50, height: 50 }} onClick={() => setOpen(true)}>
        <MatIcon value="delete" />
      </IconButton>
      <GenericDialog
        open={open}
        title={`Delete ${type} image`}
        positiveAction={{
          buttonText: "Delete",
          className: classes.NegativePositiveAction,
        }}
        negativeAction={{
          buttonText: "Cancel",
        }}
        onClose={() => setOpen(false)}
        onPositiveClick={() => {
          setOpen(false);
          setTimeout(async () => await removeFile(type));
        }}
        onNegativeClick={() => setOpen(false)}
        classes={{
          title: `${classes.Title} ${classes.NegativeTitle}`,
          actions: classes.Actions,
        }}
      >
        <Stack component="p" rowGap={1}>
          <Typography component="span">Are you sure you want to delete this image?</Typography>
        </Stack>
      </GenericDialog>
    </Stack>
  );
};

const UploadItem = ({
  projectId,
  url,
  type,
  allowedDimension,
  aspectRatio,
  refreshProject,
  setUploadHandlers,
  setUploadings,
  setUploadErrors,
  handleExposeUploadHandler,
}: UploadItemProps): JSX.Element => {
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const uploaded = useMemo(() => !!url, [url]);

  const { trigger: deleteFile } = useTriggerRequest({
    url: "reporting/projects/images",
    method: "delete",
    queryParams: {
      project_id: projectId,
      image_types: [type],
    },
  });

  const patchUrl = async (url: Nullable<string>): Promise<void> => {
    try {
      await api.patch(`admin/projects/${projectId}`, {
        [`${type === "profile" ? "preview" : type}ImageUrl`]: url,
      });
    } catch (error: any) {
      enqueueError(`Unable to update ${capitalize(type)} image.`);
      logger.error(`Unable to update ${capitalize(type)} image: ${error?.message}`, {});
    }
  };

  const onUploadSuccess = async (_: File, metadata?: OnFileUploadSuccessMetaData): Promise<void> => {
    setTimeout(async () => {
      await patchUrl(metadata?.response?.data?.s3_url);
      await refreshProject();
      setUploadings((previous) => ({ ...previous, [type]: false }));
      setUploadErrors((previous) => ({ ...previous, [type]: false }));
    });
  };

  const onUploadError = (error: AxiosError): void => {
    enqueueError(`${capitalize(type)}: ${(error?.response?.data as { detail: string })?.detail}`);
    setUploadErrors((previous) => ({ ...previous, [type]: true }));
  };

  const removeFile = async (): Promise<boolean> => {
    let removed = true;

    if (uploaded) {
      try {
        await deleteFile();
        await patchUrl(null);
        enqueueSuccess(`Successfully deleted ${capitalize(type)} image.`);
      } catch {
        enqueueError(`Unabled to delete ${capitalize(type)} image.`);
        removed = false;
      }
    }

    if (removed) {
      setUploadHandlers((previous: any) => ({
        ...previous,
        [`${type}-upload`]: null,
      }));

      await refreshProject();
    }

    return removed;
  };

  return (
    <Stack gap={1}>
      <Typography variant="body2" fontWeight="bold" color="black">
        {capitalize(type)} Image
      </Typography>
      <Maybe condition={!!url}>
        <Uploaded type={type} url={url} removeFile={removeFile} />
      </Maybe>
      <Maybe condition={!url}>
        <UploaderWidget
          inputId={`${type}-upload`}
          uploadMethod="post"
          api={api}
          encType="multipart/form-data"
          uploadLink={`reporting/projects/images?${generateQueryParams({
            project_id: projectId,
            image_type: type,
          })}`}
          uploadIcon={{
            name: "insert_photo",
            variant: "outlined",
          }}
          removeIcon={{
            name: uploaded ? "delete" : "clear",
            ...px({ variant: uploaded ? null : "round" }),
          }}
          dismissUploaderOnFileExhaustion
          maxFileSize={1572864} // 1.5 MB
          allowedDimension={{
            size: allowedDimension,
            exclusive: "min",
          }}
          allowedAspectRatio={aspectRatio}
          allowedExtensions={["image/png"]}
          canDragAndDrop
          externallyUpload
          onExposeUploadHandler={handleExposeUploadHandler}
          onUploadingStatusChange={(status: boolean) => setUploadings((previous) => ({ ...previous, [type]: status }))}
          onFileUploadSuccess={onUploadSuccess}
          onFileUploadError={onUploadError}
          onFileRemoval={removeFile}
        />
      </Maybe>
    </Stack>
  );
};

const ProjectUpload = ({ projectsResponse: serverProjectsResponse }: ProjectUploadProps): JSX.Element => {
  const { popFromPath } = useNavigation();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const { breadcrumbs, updateBreadcrumbName } = useBreadcrumbs();

  const [uploadings, setUploadings] = useState<Record<ProjectImageType, boolean>>({
    profile: false,
    map: false,
    illustration: false,
  });
  const [uploadHandlers, setUploadHandlers] = useState<FileUploadHandlers>({});
  const [uploadErrors, setUploadErrors] = useGetSet<Record<ProjectImageType, boolean>>({
    profile: false,
    map: false,
    illustration: false,
  });

  const { data, trigger: getProject } = useTriggerRequest<AdminProjectQueryResponse, object, object, AdminProjectQuery>({
    url: "/admin/projects",
    queryParams: { ids: [serverProjectsResponse?.data?.at(0)?.id], orderBy: ProjectOrderByOption.NAME },
    optimisticData: serverProjectsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch project.");
        logger.error(`Unable to fetch project: ${error?.message}`, {});
      },
    },
  });

  const project = useMemo(() => data?.data?.at(0), [data?.data]);

  const projectDetailsBreadcrumb = useMemo(() => project?.name, [project?.name]);
  const [previewImageUrl, mapImageUrl, illustrationImageUrl] = useMemo(
    () => [project?.previewImageUrl, project?.mapImageUrl, project?.illustrationImageUrl],
    [project?.illustrationImageUrl, project?.mapImageUrl, project?.previewImageUrl],
  );
  const isUploading = useMemo(() => Object.values(uploadings)?.some((value) => value), [uploadings]);
  const hasFileToUpload = useMemo(() => Object.values(uploadHandlers)?.some?.((value) => !!value), [uploadHandlers]);

  useEffect(() => {
    if (!!projectDetailsBreadcrumb && breadcrumbs?.at(1)?.name !== projectDetailsBreadcrumb)
      updateBreadcrumbName?.("Project Details", projectDetailsBreadcrumb);
  }, [breadcrumbs, projectDetailsBreadcrumb, updateBreadcrumbName]);

  const refreshProject = useCallback(async () => {
    await getProject();
  }, [getProject]);

  const handleExposeUploadHandler = (
    inputId: string,
    // eslint-disable-next-line no-unused-vars
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    if (handler) {
      setUploadHandlers((previous: any) => ({
        ...previous,
        [inputId]: handler,
      }));
    } else {
      setUploadHandlers((previous: any) =>
        Object.entries(previous).reduce((accum, [key, value]) => {
          if (key !== inputId)
            accum = {
              ...accum,
              [key]: value,
            };
          return accum;
        }, {}),
      );
    }
  };

  const handleImageUploads = async (): Promise<void> => {
    const imageUploadPromises = Object.entries(uploadHandlers).reduce(
      (accum, [inputId, handler]) => [
        ...accum,
        handler?.({
          preventDefault: () => {},
          stopPropagation: () => {},
          target: null,
          currentTarget: null,
          type: 'click',
          bubbles: false,
          cancelable: true,
          defaultPrevented: false,
        } as MouseEvent<HTMLButtonElement>)?.then(() =>
          handleExposeUploadHandler(inputId, null),
        ),
      ],
      [] as Promise<void>[],
    );

    await Promise.all(imageUploadPromises);

    setTimeout(async () => {
      if (!Object.values(uploadErrors())?.some((value) => value)) await popFromPath(2);
    }, 500);
  };

  return (
    <Stack className={classes.Container} gap={1} bgcolor="white" p={2}>
      <UploadItem
        projectId={project?.id as string}
        url={previewImageUrl}
        type="profile"
        allowedDimension={ProjectImageTypeToDimensions.profile}
        aspectRatio={ProjectImageTypeToAspectRatio.profile}
        refreshProject={refreshProject}
        setUploadHandlers={setUploadHandlers}
        setUploadings={setUploadings}
        setUploadErrors={setUploadErrors}
        handleExposeUploadHandler={handleExposeUploadHandler}
      />
      <Divider />
      <UploadItem
        projectId={project?.id as string}
        url={mapImageUrl}
        type="map"
        allowedDimension={ProjectImageTypeToDimensions.map}
        aspectRatio={ProjectImageTypeToAspectRatio.map}
        refreshProject={refreshProject}
        setUploadHandlers={setUploadHandlers}
        setUploadings={setUploadings}
        setUploadErrors={setUploadErrors}
        handleExposeUploadHandler={handleExposeUploadHandler}
      />
      <Divider />
      <UploadItem
        projectId={project?.id as string}
        url={illustrationImageUrl}
        type="illustration"
        allowedDimension={ProjectImageTypeToDimensions.illustration}
        aspectRatio={ProjectImageTypeToAspectRatio.illustration}
        refreshProject={refreshProject}
        setUploadHandlers={setUploadHandlers}
        setUploadings={setUploadings}
        setUploadErrors={setUploadErrors}
        handleExposeUploadHandler={handleExposeUploadHandler}
      />
      <Stack direction="row" justifyContent="space-between">
        <Button color="error" onClick={() => popFromPath(2)}>
          Cancel
        </Button>
        <LoadingButton
          variant="contained"
          sx={{ borderRadius: 1, width: 100 }}
          loading={isUploading}
          disabled={!hasFileToUpload}
          onClick={handleImageUploads}
        >
          Save
        </LoadingButton>
      </Stack>
    </Stack>
  );
};

export default ProjectUpload;
