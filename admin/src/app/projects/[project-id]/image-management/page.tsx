import { AuthorizeServer } from "@app/authorize-server";
import { AdminProjectQueryResponse, PermissionEnum, ProjectOrderByOption } from "@rubiconcarbon/shared-types";
import ImageManagement from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement, type JSX } from "react";

/**
 * Project Image Management Page
 *
 * This is a server component that renders the Project Image Management page
 */
export default async function ProjectImageManagementPage({
  params,
}: {
  params: Promise<{ "project-id": string }>;
}): Promise<JSX.Element> {
  const { "project-id": id } = await params;

  const projectsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminProjectQueryResponse>(
      `admin/projects?${generateQueryParams({ ids: [id], orderBy: ProjectOrderByOption.NAME })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(projectsResponse)) return projectsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.PROJECTS_WRITE]}>
      <ImageManagement projectsResponse={projectsResponse as AdminProjectQueryResponse} />
    </AuthorizeServer>
  );
}
