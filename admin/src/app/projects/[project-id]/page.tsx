import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminProjectQueryResponse,
  AdminProjectResponse,
  PermissionEnum,
  ProjectRelations,
} from "@rubiconcarbon/shared-types";
import ProjectItem from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { isValidElement, type JSX } from "react";

/**
 * Project Item Page
 *
 * This is a server component that renders the Project Item page
 */
export default async function ProjectItemPage({
  params,
}: {
  params: Promise<{ "project-id": string }>;
}): Promise<JSX.Element> {
  const { "project-id": id } = await params;

  const projects = await withErrorHandling(async () =>
    baseApiRequest<AdminProjectQueryResponse>(
      `admin/projects?${generateQueryParams({ ids: [id], includeRelations: [ProjectRelations.COUNTRY, ProjectRelations.BUFFER_CATEGORY, ProjectRelations.PROJECT_VINTAGES] })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(projects)) return projects;

  return (
    <AuthorizeServer permissions={[PermissionEnum.PROJECTS_READ]}>
      <ProjectItem project={(projects as AdminProjectQueryResponse)?.data?.at(0) ?? ({} as AdminProjectResponse)} />
    </AuthorizeServer>
  );
}
