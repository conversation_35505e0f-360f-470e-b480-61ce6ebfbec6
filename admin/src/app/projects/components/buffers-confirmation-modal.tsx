import React, { useContext, BaseSyntheticEvent, useEffect, type JSX } from "react";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Grid,
  TableHead,
  TextField,
} from "@mui/material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { useState } from "react";
import { AdminProjectVintageRiskBufferRequest } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import { BufferListItem } from "@models/buffer-list-item";
import { isEmpty, isNil } from "lodash";
import Decimal from "decimal.js";
import { percentageFormat } from "@rubiconcarbon/frontend-shared";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface BuffersConfirmaModalProps extends BaseDialogProps {
  bufferList: BufferListItem[];
  refreshData: () => void;
}

export default function BuffersConfirmationModal({
  isOpen,
  bufferList,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: BuffersConfirmaModalProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState<boolean>(false);
  const [note, setNote] = useState<string>();
  const { api } = useContext(AxiosContext);

  useEffect(() => {
    setNote("");
  }, [bufferList]);

  const submitBuffersHandler = (event: BaseSyntheticEvent): void => {
    event.preventDefault();
    const payload = bufferList.map((bufferItem) => ({
      projectVintageId: bufferItem.projectVintageId,
      riskBufferPercentage: bufferItem.riskBufferPercentage,
      lowBufferPercentage: bufferItem.lowBufferPercentage,
      highBufferPercentage: bufferItem.highBufferPercentage,
      notes: note,
    }));

    setRequestInFlight(true);
    api
      .patch<AdminProjectVintageRiskBufferRequest>(`admin/project-vintages/risk-buffers`, payload)
      .then(() => {
        refreshData();
        onConfirm?.("Successfully updated project vintage buffers.");
        onClose();
      })
      .catch((e) => {
        let message = "Failed to update project vintage buffers.";
        if (!isEmpty(e?.response?.data?.message)) {
          message += e?.response?.data?.message;
        }
        console.error(message, e);
        onError?.(message);
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  const noteHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setNote(event.target.value.trimStart());
  };
  const columns = ["Project Vintage", "Lower limit buffer", "Higher limit buffer", "Buffer"];

  const generateBufferCell = (origValue?: Decimal, curValue?: Decimal): JSX.Element => {
    if (isNil(origValue) && isNil(curValue))
      return (
        <Typography sx={{ textAlign: "center" }} variant="body2">
          -
        </Typography>
      );

    return (
      <Grid style={{ display: "flex", color: "#094436" }}>
        <Typography sx={{ textAlign: "center" }} variant="body2">
          {isNil(origValue) ? "-" : `${percentageFormat(+origValue)}`}
        </Typography>
        <ArrowForwardIcon sx={{ paddingBottom: "4px", marginLeft: "5px", marginRight: "5px" }} />
        <Typography sx={{ textAlign: "center" }} variant="body2">
          {isNil(curValue) ? "-" : `${percentageFormat(+curValue)}`}
        </Typography>
      </Grid>
    );
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle>Confirm Project Vintage Buffers Update</DialogTitle>
      <DialogContent sx={{ marginTop: "-20px" }}>
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          Are you sure you want to save the following changes?
        </Typography>
        <Box mt={3}>
          <TableContainer sx={{ width: 750, maxHeight: 500 }}>
            <Table aria-label="buffers updates table">
              <TableHead>
                <TableRow>
                  {columns?.map((column, idx) => (
                    <TableCell key={`${column}-${idx}`}>
                      <Typography variant="body2">{column}</Typography>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {bufferList?.map((row) => (
                  <TableRow key={row.projectVintageId} sx={{ "& > *": { borderBottom: "unset" } }}>
                    <TableCell align="left">{row.name}</TableCell>
                    <TableCell align="left">
                      {generateBufferCell(row.origLowBufferPercentage, row.lowBufferPercentage)}
                    </TableCell>
                    <TableCell align="left">
                      {generateBufferCell(row.origHighBufferPercentage, row.highBufferPercentage)}
                    </TableCell>
                    <TableCell align="left">
                      {generateBufferCell(row.origRiskBufferPercentage, row.riskBufferPercentage)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
        <Box mt={4}>
          <form id="buffers-request-form" onSubmit={submitBuffersHandler}>
            <fieldset disabled={requestInFlight} style={{ display: "contents" }}>
              <Grid container gap={4} flexDirection="column">
                <TextField
                  id="memo"
                  required
                  label="Memo"
                  multiline
                  rows={4}
                  value={note}
                  onChange={noteHandler}
                  inputProps={{ maxLength: 500 }}
                />
              </Grid>
            </fieldset>
          </form>
        </Box>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions>
        <Button disabled={requestInFlight} variant="text" onClick={onClose} sx={{ fontWeight: 600 }}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          variant="contained"
          form="buffers-request-form"
          type="submit"
          sx={{ px: 3.5, color: "#FFFFFF" }}
        >
          Yes, proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}
