"use client";;
import Page from "@components/layout/containers/page";
import ProjectsTable from "./projects-table";
import { AdminProjectQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const Projects = ({ projects }: { projects: AdminProjectQueryResponse }): JSX.Element => {
  return (
    <Page>
      <ProjectsTable projects={projects} />
    </Page>
  );
};

export default Projects;
