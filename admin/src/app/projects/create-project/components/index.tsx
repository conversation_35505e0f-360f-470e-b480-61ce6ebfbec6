"use client";;
import Page from "@components/layout/containers/page";
import { AdminCountryResponse, AdminProjectTypeResponse } from "@rubiconcarbon/shared-types";
import NewProject from "./new-project";

import type { JSX } from "react";

const CreateProject = ({
  types,
  countries,
}: {
  types: AdminProjectTypeResponse[];
  countries: AdminCountryResponse[];
}): JSX.Element => {
  return (
    <Page>
      <NewProject types={types} countries={countries} />
    </Page>
  );
};

export default CreateProject;
