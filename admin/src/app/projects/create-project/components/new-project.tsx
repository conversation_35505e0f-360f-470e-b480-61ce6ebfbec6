import React, { useState, BaseSyntheticEvent, useCallback, useContext, useMemo, type JSX } from "react";
import { useRouter } from "next/navigation";
import { AdminProjectResponse, AdminProjectTypeResponse, AdminCountryResponse } from "@rubiconcarbon/shared-types";
import {
  Box,
  Grid,
  Card,
  CardActions,
  CardContent,
  TextField,
  Button,
  FormLabel,
  FormControl,
  Typography,
  Autocomplete,
} from "@mui/material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@hooks/use-navigation";
import { AxiosContext } from "@providers/axios-provider";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

interface NewProject {
  projectName: string;
  projectType?: AdminProjectTypeResponse;
  registryProjectId?: string;
  country?: AdminCountryResponse;
}

export default function NewProject({
  types,
  countries,
}: {
  types: AdminProjectTypeResponse[];
  countries: AdminCountryResponse[];
}): JSX.Element {
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);
  const router = useRouter();
  const { api } = useContext(AxiosContext);
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [newProject, setNewProject] = useState<NewProject>({ projectName: "" });
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);

  const onSubmit = (event: BaseSyntheticEvent): void => {
    event.preventDefault();
    setIsConfirmationDialogOpen(true);
  };

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const updatedProject = { ...newProject };
      if (event.target.name === 'projectName' || event.target.name === 'registryProjectId') {
        updatedProject[event.target.name] = event.target.value;
      }
      setNewProject(updatedProject);
    },
    [newProject],
  );

  const onConfirmCreate = async (): Promise<void> => {
    setSubmissionInFlight(true);
    const payload = {
      name: newProject.projectName.trim(),
      projectTypeId: newProject?.projectType?.id,
      registryProjectId: newProject?.registryProjectId?.trim(),
      countryCode: newProject?.country?.alpha3,
    };

    setIsConfirmationDialogOpen(false);

    try {
      await api.post<AdminProjectResponse>(`admin/projects`, payload);
      enqueueSuccess("Successfully created a new project");
      popFromPath(1);
    } catch (error: any) {
      setSubmissionInFlight(false);
      if (error?.response?.data?.message)
      if (error?.response?.data?.message)
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      else enqueueError("Unable to create new project");
    }
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmCreate(),
      tooltip: "proceed with creating a new project",
    },
  ];

  const projectTypesDefaultProps = useMemo(
    () => ({
      options: types?.sort((a, b) => a.type?.localeCompare(b.type)) ?? [],
      getOptionLabel: (option: AdminProjectTypeResponse): string => (option?.type ? option.type : ""),
      isOptionEqualToValue: (option: AdminProjectTypeResponse, value: AdminProjectTypeResponse): boolean =>
        option.id === value.id,
    }),
    [types],
  );

  const countriesDefaultProps = useMemo(
    () => ({
      options: countries?.sort((a, b) => a.name?.localeCompare(b.name)) ?? [],
      getOptionLabel: (option: AdminCountryResponse): string => (option?.name ? option.name : ""),
      isOptionEqualToValue: (option: AdminCountryResponse, value: AdminCountryResponse): boolean =>
        option.alpha3 === value.alpha3,
    }),
    [countries],
  );

  const selectionHandler = useCallback(
    (event: React.ChangeEvent, newValue: AdminProjectTypeResponse | AdminCountryResponse) => {
      const selectedItemId = event.target.getAttribute("id");
      if (!!selectedItemId && selectedItemId.split("-")?.length > 0) {
        const parentId = selectedItemId.split("-")[0];
        const updatedProject = { ...newProject };
        if (parentId === 'projectType' || parentId === 'country') {
          (updatedProject as any)[parentId] = newValue;
        }
        setNewProject(updatedProject);
      }
    },
    [newProject],
  );

  const itemDtailsCard = useMemo(
    () => (
      <React.Fragment>
        <CardContent>
          <form id="permissions-request-form" onSubmit={onSubmit}>
            <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
              <Grid container gap={4} flexDirection="column">
                <FormControl fullWidth>
                  <FormLabel component="legend" sx={{ marginTop: 1.5, marginBottom: 1.5, lineHeight: "150%" }}>
                    Create a new project.
                  </FormLabel>
                  <TextField
                    sx={{ marginTop: "30px" }}
                    type={"text"}
                    name="registryProjectId"
                    id="registryProjectId"
                    label="Registry Project ID"
                    value={newProject.registryProjectId}
                    onChange={onChangeHandler}
                    required
                    inputProps={{ maxLength: 256 }}
                    helperText={
                      <Typography variant="caption">
                        For Verra projects, begin with &apos;VCS&apos;, for Gold Standard, start with &apos;GS&apos;,
                        and for ACR, use &apos;ACR&apos;.
                      </Typography>
                    }
                  />
                  <TextField
                    type={"text"}
                    sx={{ marginTop: "30px" }}
                    name="projectName"
                    id="projectName"
                    label="Project Name"
                    value={newProject.projectName}
                    onChange={onChangeHandler}
                    required
                    inputProps={{ maxLength: 256 }}
                  />
                  <Autocomplete
                    {...projectTypesDefaultProps}
                    value={newProject.projectType}
                    onChange={(event, value) => selectionHandler(event as any, value as AdminProjectTypeResponse)}
                    disablePortal
                    id="projectType"
                    sx={{ marginTop: "30px" }}
                    renderInput={(params) => <TextField required {...params} label="Project Type" />}
                    ListboxProps={{
                      sx: {
                        maxHeight: "200px",
                      },
                    }}
                  />
                  <Autocomplete
                    {...countriesDefaultProps}
                    value={newProject.country}
                    onChange={(event, value) => selectionHandler(event as any, value as AdminCountryResponse)}
                    disablePortal
                    id="country"
                    sx={{ marginTop: "30px" }}
                    renderInput={(params) => <TextField required {...params} label="Project Country" />}
                    ListboxProps={{
                      sx: {
                        maxHeight: "200px",
                      },
                    }}
                  />
                </FormControl>
              </Grid>
            </fieldset>
          </form>
        </CardContent>
        <CardActions
          sx={{
            justifyContent: "space-between",
            mt: "50px",
          }}
        >
          <Box ml={2} sx={{ display: "flex" }}></Box>
          <Box mr={2} mb={1} mt={1}>
            <Button
              sx={{ marginRight: 3, fontWeight: 600 }}
              disabled={submissionInFlight}
              variant="text"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              form="permissions-request-form"
              sx={{ px: 3.5, color: "#FFFFFF" }}
              disabled={submissionInFlight}
            >
              Create
            </Button>
          </Box>
        </CardActions>
      </React.Fragment>
    ),
    [
      newProject,
      submissionInFlight,
      countriesDefaultProps,
      projectTypesDefaultProps,
      onChangeHandler,
      selectionHandler,
      router,
    ],
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <ConfirmationModal
        isOpen={isConfirmationDialogOpen}
        onClose={() => setIsConfirmationDialogOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          You are about to create a new project named <b>{newProject.projectName}</b>.
        </Typography>
      </ConfirmationModal>
    </Box>
  );
}
