import React, {
  useState,
  BaseSyntheticEvent,
  useCallback,
  useContext,
  useMemo,
  SyntheticEvent,
  KeyboardEvent,
  type JSX,
} from "react";
import { useRouter } from "next/navigation";
import {
  AdminProjectSearchResponse,
  AdminProjectVintageRequest,
  AdminProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import {
  Box,
  Grid,
  Card,
  CardActions,
  CardContent,
  TextField,
  Button,
  FormLabel,
  FormControl,
  Typography,
  Autocomplete,
  Stack,
  IconButton,
  AutocompleteInputChangeReason,
} from "@mui/material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@hooks/use-navigation";
import { AxiosContext } from "@providers/axios-provider";
import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import COLORS from "@components/ui/theme/colors";
import dayjs, { Dayjs } from "dayjs";
import { isEmpty, isFinite } from "lodash";
import { useLogger } from "@providers/logging";
import { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import useAutoCompleteOptionsAsync from "@hooks/use-auto-complete-options-async";
import { useToggle } from "react-use";
import usePerformantEffect from "@hooks/use-performant-effect";
import { Nullable } from "@rubiconcarbon/frontend-shared";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import YearRangePicker from "@components/ui/year-range/year-range";
import DateRangePicker from "@components/ui/date-range/date-range";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

import classes from "../styles/style.module.scss";

interface DateRange {
  from: Nullable<Date>;
  to: Nullable<Date>;
}

const getDateFromStr = (input: string): Date => {
  const [day, month, year] = input.split("/");
  return new Date(+year, +month - 1, +day);
};

const buildDateRange = (dateFrom: Dayjs, dateTo: Dayjs): string => {
  if (dateFrom?.isValid() && dateTo?.isValid() && !dateFrom.isSame(dateTo)) {
    return `${dayjs(dateFrom).format("MM/DD/YYYY")} - ${dayjs(dateTo).format("MM/DD/YYYY")}`;
  } else if (dateFrom?.isValid()) {
    return `${dayjs(dateFrom).format("MM/DD/YYYY")}`;
  } else if (dateTo?.isValid()) {
    return `${dayjs(dateTo).format("MM/DD/YYYY")}`;
  }
  return "";
};

const buildYearRange = (dateFrom: Dayjs, dateTo: Dayjs): string => {
  if (dateFrom?.isValid() && dateTo?.isValid() && dateFrom.year() !== dateTo.year()) {
    return `${dayjs(dateFrom).year()} - ${dayjs(dateTo).year()}`;
  } else if (dateFrom?.isValid()) {
    return `${dayjs(dateFrom).year()}`;
  } else if (dateTo?.isValid()) {
    return `${dayjs(dateTo).year()}`;
  }
  return "";
};

const extractDatesFromRange = (input: string): DateRange => {
  if (isEmpty(input)) {
    return {
      from: null,
      to: null,
    };
  }

  const splitInput = input.split("-");
  if (!!splitInput && splitInput.length === 2) {
    const from = splitInput[0].trim();
    const to = splitInput[1].trim();
    //years range
    if (isFinite(+splitInput[0].trim()) && isFinite(+splitInput[1].trim())) {
      return {
        from: new Date(`01/01/${from}`),
        to: from == to ? null : new Date(`12/31/${to}`),
      };
    } else {
      //dates range
      return {
        from: getDateFromStr(from),
        to: from === to ? null : getDateFromStr(to),
      };
    }
  }

  if (isFinite(+input)) {
    return {
      from: new Date(`01/01/${input}`),
      to: new Date(`12/31/${input}`),
    };
  }

  const inputDate = getDateFromStr(input);
  if (dayjs(inputDate).isValid()) {
    return {
      from: inputDate,
      to: new Date(`12/31/${inputDate.getFullYear()}`),
    };
  }

  return {
    from: null,
    to: null,
  };
};

interface NewVintage {
  vintageName: string;
  project?: UseAutoCompleteOptionsReturnEntry;
}

export default function NewVintage(): JSX.Element {
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);
  const router = useRouter();
  const { popFromPath } = useNavigation();
  const [editName, setEditName] = useState<boolean>(false);
  const [newVintage, setNewVintage] = useState<NewVintage>({
    vintageName: "",
    project: {
      label: "",
      value: null,
    },
  });
  const [dateRange, setDateRange] = useState<string>("");
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const [projectSearchTerm, setProjectSearchTerm] = useState<string>("");
  const [open, setOpen] = useToggle(false);

  const onSubmit = useCallback(
    (event: BaseSyntheticEvent) => {
      event.preventDefault();
      setIsConfirmationDialogOpen(true);
    },
    [setIsConfirmationDialogOpen],
  );

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const updatedVintage = { ...newVintage };
      if (event.target.name === 'vintageName') {
        updatedVintage.vintageName = event.target.value;
      }
      setNewVintage(updatedVintage);
    },
    [newVintage],
  );

  const projectVintageName = useMemo(() => {
    if (!newVintage.vintageName || newVintage.vintageName === "") return "";

    const parts = newVintage.vintageName.split("-");
    if (parts.length > 0 && parts[0].length > 8) {
      const interval = extractDatesFromRange(dateRange);

      return !interval?.to || interval?.from?.getFullYear() === interval?.to?.getFullYear()
        ? `${interval?.from?.getFullYear()}`
        : `${interval?.from?.getFullYear()} - ${interval?.to?.getFullYear()}`;
    }
    return newVintage.vintageName;
  }, [newVintage.vintageName, dateRange]);

  const validationMessage = useMemo(() => {
    const dates = extractDatesFromRange(dateRange);

    if (!dayjs(dates?.from).isValid()) return "Please select a valid start date";

    if (!dayjs(dates?.to).isValid()) return "Please select a valid end date";

    if (dayjs(dates?.from).isValid() && dayjs(dates?.to).isValid() && dates.from! > dates.to!)
      return "End date should be greater than start date";

    return "";
  }, [dateRange]);

  usePerformantEffect(() => {
    if (!projectSearchTerm) setOpen(false);
    else
      setTimeout(() => {
        setOpen(true);
      }, 500);
  }, [projectSearchTerm]);

  const onConfirmCreate = async (): Promise<void> => {
    setSubmissionInFlight(true);
    const interval = extractDatesFromRange(dateRange);
    const payload: AdminProjectVintageRequest = {
      name: projectVintageName,
      projectId: newVintage?.project?.value,
      interval: `${interval?.from?.toISOString()} - ${interval?.to?.toISOString()}`,
    };

    setIsConfirmationDialogOpen(false);

    try {
      await api.post<AdminProjectVintageResponse>(`admin/project-vintages`, payload);
      enqueueSuccess("Successfully created a new vintage");
      popFromPath(1);
    } catch (error: any) {
      setSubmissionInFlight(false);
      if (error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to create new vintage");
    }
  };

  const projectOptions = useAutoCompleteOptionsAsync<AdminProjectSearchResponse, AdminProjectSearchResponse>({
    q: projectSearchTerm,
    keys: ["id", "name", "registryProjectId"],
    label: (entry) => `${entry?.registryProjectId} - ${entry?.name}`,
    value: (entry) => entry?.id,
    request: {
      url: "admin/projects/search",
      queryParams: {
        name: true,
        id: true,
        fuzzy: true,
        limit: 50,
      },
      swrOptions: {
        onError: (error: any): void => {
          setProjectSearchTerm("");
          enqueueError(`Unable to search projects for search term ${projectSearchTerm}.`);
          logger.error(
            `Unable to search projects for search term ${projectSearchTerm} in Add Vintage Modal in Trade. Error: ${error?.message}`,
            {},
          );
        },
      },
    },
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const handleProjectSearch = useCallback(
    (event: SyntheticEvent, value: string, reason: AutocompleteInputChangeReason): void => {
      event?.preventDefault();

      if (reason === "input") {
        setProjectSearchTerm(value);
      }
      if (reason === "clear") {
        setProjectSearchTerm("");
        setNewVintage((previous) => ({
          ...previous,
          project: { label: "", value: null },
        }));
      }

      setOpen(false);
    },
    [setOpen],
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent): void => {
      if (event.key === "Escape") setOpen(false);
    },
    [setOpen],
  );

  const handleClose = useCallback((): void => setOpen(false), [setOpen]);

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmCreate(),
      tooltip: "proceed with creating a new vintage",
    },
  ];

  const   selectionHandler = useCallback(
    (event: React.ChangeEvent, newValue: UseAutoCompleteOptionsReturnEntry) => {
      const selectedItemId = event.target.getAttribute("id");
      if (!!selectedItemId && selectedItemId.split("-")?.length > 0) {
        const parentId = selectedItemId.split("-")[0];
        const updatedVintage = { ...newVintage };
        if (parentId === 'project') {
          updatedVintage.project = newValue;
        }
        setNewVintage(updatedVintage);
      }
    },
    [newVintage],
  );

  const handleNameClick = useCallback(() => {
    setEditName(true);
  }, [setEditName]);

  const saveDateRange = useCallback(() => {
    const updatedVintage = { ...newVintage };
    if (!validationMessage) {
      updatedVintage.vintageName = dateRange;
      setNewVintage(updatedVintage);
      setEditName(false);
    }
  }, [newVintage, dateRange, validationMessage]);

  const cancelEditDateRange = useCallback(() => {
    setDateRange(newVintage.vintageName);
    setEditName(false);
  }, [newVintage.vintageName, setEditName]);

  const dateRangeHandler = useCallback(
    (dateFrom: Dayjs, dateTo: Dayjs): void => {
      const result = buildDateRange(dateFrom, dateTo);
      setDateRange(result);
    },
    [setDateRange],
  );

  const yearRangeHandler = useCallback(
    (dateFrom: Dayjs, dateTo: Dayjs): void => {
      const result = buildYearRange(dateFrom, dateTo);
      setDateRange(result);
    },
    [setDateRange],
  );

  const showYearRange = useCallback(
    (dateRangeInput: string): JSX.Element => {
      const yearRange = extractDatesFromRange(dateRangeInput);
      return (
        <YearRangePicker dateFrom={dayjs(yearRange.from)} dateTo={dayjs(yearRange.to)} onChange={yearRangeHandler} />
      );
    },
    [yearRangeHandler],
  );

  const showDateRange = useCallback(
    (dateRangeInput: string): JSX.Element => {
      const dateRange = extractDatesFromRange(dateRangeInput);
      return (
        <DateRangePicker dateFrom={dayjs(dateRange.from)} dateTo={dayjs(dateRange.to)} onChange={dateRangeHandler} />
      );
    },
    [dateRangeHandler],
  );

  const itemDtailsCard = useMemo(
    () => (
      <React.Fragment>
        <CardContent sx={{ height: "250px" }}>
          <form id="new-vintage" onSubmit={onSubmit}>
            <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
              <Grid container gap={4} flexDirection="column">
                <FormControl fullWidth>
                  <FormLabel component="legend" sx={{ marginTop: 1.5, marginBottom: 1.5, lineHeight: "150%" }}>
                    Create a new vintage.
                  </FormLabel>
                  <Autocomplete
                    open={open}
                    options={projectOptions}
                    value={newVintage.project}
                    onChange={(event, newValue) => selectionHandler(event as any, newValue as UseAutoCompleteOptionsReturnEntry)}
                    onInputChange={handleProjectSearch}
                    onKeyDown={handleKeyDown}
                    onClose={handleClose}
                    disablePortal
                    id="project"
                    sx={{ marginTop: "30px" }}
                    renderInput={(params) => (
                      <TextField
                        required
                        {...params}
                        label="Project"
                        placeholder="Search and select a project"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                    ListboxProps={{
                      sx: {
                        maxHeight: 210,
                      },
                    }}
                    classes={{
                      popupIndicator: classes.AsyncPopoutIndicator,
                    }}
                  />
                  {!editName ? (
                    <TextField
                      type={"text"}
                      sx={{ marginTop: "30px" }}
                      name="vintageName"
                      id="vintageName"
                      label="Vintage"
                      placeholder="Click to enter vintage range"
                      value={newVintage.vintageName}
                      InputLabelProps={{ shrink: true }}
                      onChange={onChangeHandler}
                      required
                      inputProps={{ maxLength: 256 }}
                      onFocus={handleNameClick}
                    />
                  ) : (
                    <Box
                      mt={4}
                      sx={{
                        height: "120px",
                        border: "solid",
                        borderColor: "#094436",
                        borderWidth: "1px",
                        borderRadius: "5px",
                        padding: "10px",
                      }}
                    >
                      <Typography
                        variant="body1"
                        component="p"
                        sx={{ fontWeight: 500, color: " #094436", marginTop: "5px" }}
                      >
                        Vintage
                      </Typography>
                      <Stack direction="row" sx={{ marginTop: "10px" }}>
                        <Box>{showYearRange(dateRange)}</Box>
                        <Box sx={{ marginLeft: "20px", marginRight: "20px", paddingTop: "8px", color: "gray" }}>
                          - or -
                        </Box>
                        <Box>{showDateRange(dateRange)}</Box>
                        <Box sx={{ marginLeft: "20px" }}>
                          <Stack direction="row" gap={0}>
                            <IconButton
                              sx={{ color: COLORS.rubiconGreen }}
                              edge="start"
                              disabled={!!validationMessage}
                              onClick={saveDateRange}
                            >
                              <CheckIcon />
                            </IconButton>
                            <IconButton
                              sx={{ marginLeft: "1px", color: COLORS.rubiconGreen }}
                              edge="start"
                              onClick={cancelEditDateRange}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Stack>
                        </Box>
                      </Stack>
                      <Typography
                        sx={{ color: COLORS.red, fontSize: "12px", marginLeft: "5px", marginTop: "4px" }}
                        variant="body2"
                        component="p"
                      >
                        {validationMessage}
                      </Typography>
                    </Box>
                  )}
                </FormControl>
              </Grid>
            </fieldset>
          </form>
        </CardContent>
        <CardActions
          sx={{
            justifyContent: "space-between",
            mt: "50px",
          }}
        >
          <Box ml={2} sx={{ display: "flex" }}></Box>
          <Box mr={2} mb={1} mt={1}>
            <Button
              sx={{ marginRight: 3, fontWeight: 600 }}
              disabled={submissionInFlight}
              variant="text"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              form="new-vintage"
              sx={{ px: 3.5, color: "#FFFFFF" }}
              disabled={
                submissionInFlight ||
                !!editName ||
                newVintage.project === null ||
                newVintage.vintageName === "" ||
                !newVintage.vintageName
              }
            >
              Create
            </Button>
          </Box>
        </CardActions>
      </React.Fragment>
    ),
    [
      onSubmit,
      submissionInFlight,
      open,
      projectOptions,
      newVintage.project,
      newVintage.vintageName,
      selectionHandler,
      handleProjectSearch,
      handleKeyDown,
      handleClose,
      editName,
      onChangeHandler,
      handleNameClick,
      showYearRange,
      dateRange,
      showDateRange,
      validationMessage,
      saveDateRange,
      cancelEditDateRange,
      router,
    ],
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <ConfirmationModal
        isOpen={isConfirmationDialogOpen}
        onClose={() => setIsConfirmationDialogOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          You are about to create a new vintage named <b>{projectVintageName}</b>.
        </Typography>
      </ConfirmationModal>
    </Box>
  );
}
