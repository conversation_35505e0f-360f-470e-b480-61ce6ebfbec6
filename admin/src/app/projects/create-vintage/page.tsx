import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import CreateVintage from "./components";

import type { JSX } from "react";

/**
 * Create Vintage Page
 *
 * This is a server component that renders the Create Vintage page
 */
export default function CreateVintagePage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.PROJECTS_WRITE]}>
      <CreateVintage />
    </AuthorizeServer>
  );
}
