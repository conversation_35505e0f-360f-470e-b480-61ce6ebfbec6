import React, { use<PERSON><PERSON>back, useMemo, type JSX } from "react";
import {
  PermissionEnum,
  TrimmedRetirementLink,
  AdminRetirementResponse,
  uuid,
  AdminVintageAssetRetirementResponse,
} from "@rubiconcarbon/shared-types";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { FileDownloadRounded } from "@mui/icons-material";
import { numberFormat } from "@rubiconcarbon/frontend-shared";
import dateRangeFormatter from "@utils/formatters/date-range-formatter";
import GenericTable from "@components/ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import TableBox from "@components/ui/table-box/table-box";
import { COLUMNS } from "../constants/details-columns";
import { ExtendedRetirementDetailsModel } from "../types/extended-retirement-details-model";
import RegistryLinks from "./registry-links";

interface RetirementDetailTableProps {
  id: uuid;
  rows?: AdminVintageAssetRetirementResponse[];
  refreshRetirement: () => Promise<AdminRetirementResponse>;
}

export default function RetirementDetailsTable(props: RetirementDetailTableProps): JSX.Element {
  const { id, rows, refreshRetirement } = props;

  const tableRows = useMemo(() => rows?.filter(({ amount }) => !!amount), [rows]);

  const toRowModel = useCallback(
    (
      row: AdminVintageAssetRetirementResponse & { links: TrimmedRetirementLink[] },
    ): GenericTableRowModel<ExtendedRetirementDetailsModel> => {
      return {
        id: row?.asset?.id,
        ...row,
        suspendedF: row?.projectVintage?.project?.suspended ? "Yes" : "No",
        rctStandardF: row?.projectVintage?.project?.rctStandard ? "Yes" : "No",
        intervalF: dateRangeFormatter(row?.projectVintage?.interval as string),
        amountF: numberFormat(row?.amount, { fallback: MISSING_DATA }),
        linksF:
          row?.links.reduce((accum, link, index) => accum.concat(index > 0 ? ", " : "").concat(link?.url), "") ||
          MISSING_DATA,
      };
    },
    [],
  );

  const { table } = useGenericTableUtility<ExtendedRetirementDetailsModel>({});

  const columns: GenericTableColumn<ExtendedRetirementDetailsModel>[] = useMemo(
    () =>
      COLUMNS.reduce<GenericTableColumn<ExtendedRetirementDetailsModel>[]>((accum, column) => {
        return [
          ...accum,
          ["links"].includes(column.field)
            ? {
                ...column,
                renderDataCell:
                  column.field === "links"
                    ? (row: ExtendedRetirementDetailsModel): JSX.Element => (
                        <RegistryLinks
                          id={id}
                          projectVintageId={row?.projectVintage?.id}
                          links={row?.links || []}
                          refreshRetirement={refreshRetirement}
                        />
                      )
                    : column.renderDataCell,
              }
            : column,
        ];
      }, []),
    [id, refreshRetirement],
  );

  return (
    <TableBox>
      <GenericTable
        id="Retirement-Info"
        toRowModel={toRowModel}
        columns={columns}
        pageableData={{
          data: (tableRows || []).map(row => ({
            ...row,
            links: row.links || [],
          })),
          page: {
            offset: 0,
            limit: SERVER_PAGINATION_LIMIT,
            size: tableRows?.length || 0,
            totalCount: tableRows?.length || 0,
          },
        }}
        sort={{
          sorts: {
            "projectVintage.project": "asc",
          },
        }}
        globalSearch={{
          searchKeys: [
            "projectVintage.project.name",
            "projectVintage.project.registryProjectId",
            "projectVintage.name",
            "suspendedF",
            "rctStandardF",
            "intervalF",
            "amount",
            "amountF",
            "linksF",
          ],
        }}
        export={{
          filename: `Retirement-Info-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.RETIREMENTS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
        ]}
      />
    </TableBox>
  );
}
