import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import { MISSING_DATA } from "@constants/constants";
import { Stack } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { TrimmedRetirementLink, TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import { ExtendedRetirementDetailsModel } from "../types/extended-retirement-details-model";
import dateRangeFormatter from "@utils/formatters/date-range-formatter";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";

export const COLUMNS: GenericTableColumn<ExtendedRetirementDetailsModel>[] = [
  {
    field: "id",
    label: "",
    exportable: false,
    width: GenericTableFieldSizeEnum.xtiny,
    fixedWidth: true,
    renderDataCell: (row) => <RCTEligibilityChip vintage={row?.projectVintage} />,
  },
  {
    field: "projectVintage.project.registryProjectId",
    label: "Project ID",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage.project.name",
    label: "Project Name",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage.name",
    label: "Vintage",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage",
    label: "Name",
    exportable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    maxWidth: GenericTableFieldSizeEnum.large,
    transformDataValue: (value: TrimmedProjectVintageResponse) => value?.name, // todo : @kofi what is this
    renderDataCell: (row) => (
      <>
        <div>{row?.asset?.name}</div>
        <div style={{ color: "#a0a0a0" }}>
          {row?.asset?.registryProjectId} - {row?.asset?.projectVintageName}
          <Stack direction="row" gap={1}>
            <Maybe condition={row?.asset?.isSuspended}>
              <SuspendedChip />
            </Maybe>
          </Stack>
        </div>
      </>
    ),
  },
  {
    field: "projectVintage.interval",
    label: "Vintage Date Range",
    transformDataValue: (value: string): string => `${dateRangeFormatter(value)}`,
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "links",
    label: "Registry Confirmation",
    sortable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    transformDataValue: (value: TrimmedRetirementLink[]) =>
      value.reduce((accum, link, index) => accum.concat(index > 0 ? ", " : "").concat(link?.url), "") || MISSING_DATA,
  },
];
