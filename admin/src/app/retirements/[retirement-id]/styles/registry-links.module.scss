.LinkDropdownButtonContainer {
    &:hover { 
        cursor: pointer ;
    }

    .LinkDropdownButton {
        width: 50px;
        height: 50px;

        .Badge {
            z-index: 0;
            top: 3px;
            right: -3px;
            border: 1px solid white;
        }
    }
}

.ManageLinksButton {
    width: 120px,;

    &:hover { 
        background-color: rgba(0, 0, 0, 0.2);
    }

    .ManageLinksText {
        text-transform: none; 
        text-decoration: underline; 
        text-underline-offset: 4px;
    }
}

.MenuItem {
    display: flex; 
    justify-content: center; 
    gap: 8px; 
    min-width: 150px;
    max-width: 350px;

    .Link {
        text-decoration: none;
        width: 100%;
        color: #094436;

        .Text {
            overflow: hidden;
            text-overflow: ellipsis;
            text-wrap: nowrap;
        }
    }
}

.Dialog {
    max-width: 700px !important;
    width: 100%;

    .InputRoot {
        height: 56px;
        padding: 10px !important;

        &:before { 
            border-bottom-style: none;
        }

        textarea {
            color: black;
            -webkit-text-fill-color: black;
        }
    }
}