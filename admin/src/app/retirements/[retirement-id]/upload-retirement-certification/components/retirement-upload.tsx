import { Box, Grid, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useCallback, useState, type JSX } from "react";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import {
  AdminDocumentUpdateRequest,
  uuid,
  DocumentType,
  AdminDocumentUploadUrlRequest,
  AdminRetirementResponse,
} from "@rubiconcarbon/shared-types";
import { useLogger } from "@providers/logging";
import useDocumentsApi from "@hooks/use-documents-api";
import { useGetSetState } from "react-use";
import usePerformantEffect from "@hooks/use-performant-effect";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";

import classes from "../styles/retirement-upload.module.scss";

export default function RetirementUpload({
  retirementResponse,
}: {
  retirementResponse: AdminRetirementResponse;
}): JSX.Element {
  const router = useRouter();
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<AdminDocumentUploadUrlRequest>();
  const [updatePayload, setUpdatePayload] = useState<AdminDocumentUpdateRequest>();

  const { uiKey, organization } = retirementResponse || {};

  const { fetching, fetch, retrieveUploadLink, update } = useDocumentsApi({
    query: {
      organizationId: organization?.id,
      relatedUiKey: uiKey,
    },
    updatePayload,
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      await fetch();
    },
    onUpdateError: (error: any) => {
      enqueueError("Successful upload but was unable to update file details");
      logger.error(`Successful upload but was unable to update file details: ${error?.message}`, {});
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!organization?.id && !!uiKey) {
      setTimeout(async () => await fetch());
    }
  }, [organization?.id, uiKey]);

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      organizationId: organization?.id,
      relatedUiKey: uiKey,
      filename: file.name,
      type: DocumentType.RETIREMENT_CERTIFICATE,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const onFileUploadSuccess = useCallback(
    async (file: File, metadata?: OnFileUploadSuccessMetaData): Promise<void> => {
      setUpdatePayload({
        id: uuid(metadata?.s3FileId),
        relatedUiKey: uiKey,
        organizationId: organization?.id,
        filename: file.name,
        type: DocumentType.RETIREMENT_CERTIFICATE,
        isPublic: true,
      });

      setTimeout(async () => {
        await update();
        router.back();
      });
    },
    [organization?.id, router, uiKey, update],
  );

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  return (
    <Box className={classes.Container}>
      <Grid className={classes.Content} container direction="column" gap={3}>
        <Grid item>
          <Typography variant="body2">File details</Typography>
        </Grid>
        <Grid item container direction="row">
          <Grid item container md={3} xs={6} direction="row" gap={1}>
            <Typography className={classes.Label} variant="body2">
              Organization:
            </Typography>
            <Typography variant="body2">{organization?.name}</Typography>
          </Grid>
          <Grid item container md={3} xs={6} direction="row" gap={1}>
            <Typography className={classes.Label} variant="body2">
              Transaction Key:
            </Typography>
            <Typography variant="body2">{uiKey}</Typography>
          </Grid>
        </Grid>
        <UploaderWidget
          inputId="retirement-uploads"
          cancelButtonText="back"
          uploadLink={getS3UploadApiLink}
          allowedExtensions={["application/pdf"]}
          canDragAndDrop={true}
          uploadConfirmation={{
            title: "Confirm document upload",
            content: (
              <span>
                Are you sure you want to share this document with <strong>{organization?.name}</strong>?
              </span>
            ),
          }}
          onFileUploadSuccess={onFileUploadSuccess}
          onFileUploadError={onFileUploadError}
          handleUploadClosure={() => router.back()}
        />
      </Grid>
    </Box>
  );
}
