import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, AdminRetirementResponse, RetirementRelations } from "@rubiconcarbon/shared-types";
import RetirementUpload from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { isValidElement, type JSX } from "react";

/**
 * Upload Retirement Certification Page
 *
 * This is a server component that renders the Upload Retirement Certification page
 */
export default async function RetirementUploadPage({
  params,
}: {
  params: Promise<{ "retirement-id": string }>;
}): Promise<JSX.Element> {
  const { "retirement-id": id } = await params;

  const retirementResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminRetirementResponse>(
      `admin/retirements/${id}?${generateQueryParams({
        includeRelations: [RetirementRelations.CUSTOMER_PORTFOLIO],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(retirementResponse)) return retirementResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.DOCUMENTS_CREATE, PermissionEnum.DOCUMENTS_UPDATE]}>
      <RetirementUpload retirementResponse={retirementResponse as AdminRetirementResponse} />
    </AuthorizeServer>
  );
}
