"use client";;
import { AdminRetirementQueryResponse } from "@rubiconcarbon/shared-types";
import BufferSumulationComponent from "./buffer-simulation";
import Page from "@components/layout/containers/page";

import type { JSX } from "react";

export default function BufferCalculator({
  completedRetirementsResponse,
}: {
  completedRetirementsResponse: AdminRetirementQueryResponse;
}): JSX.Element {
  return (
    <Page>
      <BufferSumulationComponent completedRetirementsResponse={completedRetirementsResponse} />
    </Page>
  );
}
