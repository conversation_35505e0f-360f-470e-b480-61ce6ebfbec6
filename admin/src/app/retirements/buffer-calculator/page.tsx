import { AuthorizeServer } from "@app/authorize-server";
import { AdminRetirementQueryResponse, PermissionEnum, RetirementRelations, RetirementStatus, RetirementType } from "@rubiconcarbon/shared-types";
import BufferCalculator from "./components";
import { isValidElement, type JSX } from "react";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Buffer Calculator Page
 *
 * This is a server component that renders the Buffer Calculator page
 */
export default async function BufferCalculatorPage(): Promise<JSX.Element> {
  const completedRetirementsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminRetirementQueryResponse>(
      `admin/retirements?${generateQueryParams({
        includeTotalCount: true,
        limit: SERVER_PAGINATION_LIMIT,
        statuses: [RetirementStatus.COMPLETED],
        types: [RetirementType.RETIREMENT],
        includeRelations: [
            RetirementRelations.CUSTOMER_PORTFOLIO,
            RetirementRelations.ASSETS,
            RetirementRelations.RCT_VINTAGES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(completedRetirementsResponse)) return completedRetirementsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_READ]}>
      <BufferCalculator completedRetirementsResponse={completedRetirementsResponse as AdminRetirementQueryResponse} />
    </AuthorizeServer>
  );
}
