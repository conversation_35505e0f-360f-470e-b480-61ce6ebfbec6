"use client";;
import Page from "@components/layout/containers/page";
import RetirementsComponent from "./retirements";
import { AdminRetirementQueryResponse } from "@rubiconcarbon/shared-types";

import type { JSX } from "react";

const Retirements = ({ retirementsResponse }: { retirementsResponse: AdminRetirementQueryResponse }): JSX.Element => {
  return (
    <Page>
      <RetirementsComponent retirementsResponse={retirementsResponse} />
    </Page>
  );
};

export default Retirements;
