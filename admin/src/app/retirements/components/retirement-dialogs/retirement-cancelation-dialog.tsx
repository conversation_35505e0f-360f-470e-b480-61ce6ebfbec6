import React, { useContext, useMemo, type JSX } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import { useState } from "react";
import { RetirementUpdateStatus, AdminTransactionResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import { AxiosContext } from "@providers/axios-provider";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface RetirementCancelationDialogProps extends BaseDialogProps {
  retirement: TransactionModel;
  refreshData: () => void;
}
export default function RetirementCancellationDialog({
  isOpen,
  retirement,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: RetirementCancelationDialogProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);

  const isTransfer = useMemo(() => retirement?.type === AllTransactionType.TRANSFER_OUTFLOW, [retirement?.type]);

  const submitCancelHandler = (): void => {
    setRequestInFlight(true);
    api
      .patch<AdminTransactionResponse>(`admin/retirements/${retirement.id}/${RetirementUpdateStatus.CANCEL}`)
      .then(() => {
        refreshData();
        onConfirm?.(`Successfully canceled ${isTransfer ? "transfer" : "retirement"}`);
        onClose();
      })
      .catch(() => {
        onError?.("Sorry, we are unable to complete your request");
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle sx={{ backgroundColor: COLORS.modalMargins }}>
        Cancel {isTransfer ? "transfer" : "retirement"} request
      </DialogTitle>
      <DialogContent
        sx={{
          borderTop: "1px solid #B2B6BB",
          borderBottom: "1px solid #B2B6BB",
        }}
      >
        <Typography mt={0} variant="body1">
          Are you sure you want to cancel the {isTransfer ? "transfer" : "retirement"} request,
          <em>
            <b>{` ${retirement?.uiKey}`}</b>
          </em>
          ?
        </Typography>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions sx={{ height: 60, backgroundColor: COLORS.modalMargins }}>
        <Button disabled={requestInFlight} variant="text" onClick={onClose} sx={{ fontWeight: 600 }}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          variant="contained"
          type="submit"
          sx={{ px: 3.5, color: "#FFFFFF" }}
          onClick={submitCancelHandler}
        >
          Yes, proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}
