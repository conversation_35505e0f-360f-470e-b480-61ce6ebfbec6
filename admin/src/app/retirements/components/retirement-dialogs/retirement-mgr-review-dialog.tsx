import React, { useContext, useMemo, type JSX } from "react";
import Typography from "@mui/material/Typography";
import { useState } from "react";
import { RetirementUpdateStatus, AdminTransactionResponse } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface RetirementConfirmationDialogProps extends BaseDialogProps {
  retirement: TransactionModel;
  refreshData: () => void;
}
export default function RetirementMgrReviewDialog({
  isOpen,
  retirement,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: RetirementConfirmationDialogProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);

  const isTransfer = useMemo(() => retirement?.type === AllTransactionType.TRANSFER_OUTFLOW, [retirement?.type]);

  const submitConfirmationHandler = (): void => {
    setRequestInFlight(true);
    api
      .patch<AdminTransactionResponse>(`admin/retirements/${retirement.id}/${RetirementUpdateStatus.CALCULATE}`)
      .then(() => {
        refreshData();
        onConfirm?.("Successfully updated step to manager review");
        onClose();
      })
      .catch((e) => {
        onError?.("Sorry, we are unable to complete your request");
        console.error(e);
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: submitConfirmationHandler,
    },
  ];
  return (
    <>
      <DialogBackdrop requestInFlight={requestInFlight} />
      <ConfirmationModal
        isOpen={isOpen}
        onClose={onClose}
        title={`Confirm ${isTransfer ? "transfer" : "retirement"} request`}
        dialogButtons={dialogButtons}
      >
        <Typography variant="body1">
          Are you sure you want to move {isTransfer ? "transfer" : "retirement"} request
          <em>
            <b>{` ${retirement?.uiKey} `}</b>
          </em>
          to portfolio manager review?
        </Typography>
      </ConfirmationModal>
    </>
  );
}
