import { useMemo, useState, type JSX } from "react";
import { Grid, Box } from "@mui/material";
import { PermissionEnum, DocumentType, RetirementStatus, AdminRetirementQueryResponse } from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { MISSING_DATA } from "@constants/constants";
import useNavigation from "@hooks/use-navigation";
import usePerformantEffect from "@hooks/use-performant-effect";
import useDocumentsApi from "@hooks/use-documents-api";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import ItemDetails from "@components/ui/details/item-details";
import RetirementAttachment from "./retirement-attachment";
import RetirementCancellationDialog from "./retirement-dialogs/retirement-cancelation-dialog";
import RetirementConfirmationDialog from "./retirement-dialogs/retirement-confirmation-dialog";
import RetirementExecutionDialog from "./retirement-dialogs/retirement-exe-dialog";
import RetirementMgrReviewDialog from "./retirement-dialogs/retirement-mgr-review-dialog";
import ButtonGroup from "@components/ui/button-group/button-group";

type RetirementSummaryProps = {
  transaction: TransactionModel;
  refresh: () => Promise<AdminRetirementQueryResponse>;
};

type AvailableButton = {
  id: string;
  name: string;
  handler: () => void;
  tooltip: string;
  requiredPermission: PermissionEnum;
};

const RetirementSummary = (props: RetirementSummaryProps): JSX.Element => {
  const { pushToPath } = useNavigation();
  const [isCalcelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isMgrReviewDialogOpen, setIsMgrReviewDialogOpen] = useState(false);
  const [isRetiteDialogOpen, setIsRetiteDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { transaction, refresh } = props;

  const isTransfer = useMemo(() => transaction?.type === AllTransactionType.TRANSFER_OUTFLOW, [transaction?.type]);

  const organization = useMemo(
    () => transaction?.[isTransfer ? "transfer" : "retirement"]?.organization,
    [isTransfer, transaction],
  );

  const {
    documents: [retirementCertificate],
    fetching,
    fetch,
  } = useDocumentsApi({
    query: {
      relatedUiKey: transaction?.uiKey,
      types: [DocumentType.RETIREMENT_CERTIFICATE],
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!organization?.id && !!transaction?.orders?.at(0)?.source?.id && !!transaction?.uiKey) {
      setTimeout(async () => await fetch());
    }
  }, [organization?.id, transaction?.uiKey, transaction?.orders]);

  const buttons: AvailableButton[] = [
    {
      id: "viewDetails",
      name: "view details",
      handler: viewDetailsHandler,
      tooltip: "Show more details",
      requiredPermission: PermissionEnum.RETIREMENTS_READ,
    },
    {
      id: "cancelAtAdminReview",
      name: "cancel",
      handler: () => setIsCancelDialogOpen(true),
      tooltip: `Cancel this ${isTransfer ? "transfer" : "retirement"}`,
      requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_ADMIN_REVIEW,
    },
    {
      id: "cancelAtPMReview",
      name: "cancel",
      handler: () => setIsCancelDialogOpen(true),
      tooltip: `Cancel this ${isTransfer ? "transfer" : "retirement"}`,
      requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW,
    },
    {
      id: "managerReview",
      name: "update to portfolio manager review",
      handler: () => setIsMgrReviewDialogOpen(true),
      tooltip: "Move to portfolio manager review step",
      requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_ADMIN_REVIEW,
    },
    {
      id: "execute",
      name: "update to in-progress",
      handler: () => setIsRetiteDialogOpen(true),
      tooltip: `Execute this ${isTransfer ? "transfer" : "retirement"}`,
      requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW,
    },
    {
      id: "uploadRetirementCert",
      name: "upload retirement certification",
      handler: uploadDocumentViewHandler,
      tooltip: "Upload certificate to this retirement",
      requiredPermission: PermissionEnum.DOCUMENTS_CREATE,
    },
    {
      id: "complete",
      name: `complete ${isTransfer ? "transfer" : "retirement"}`,
      handler: () => setIsConfirmDialogOpen(true),
      tooltip: `Complete this ${isTransfer ? "transfer" : "retirement"}`,
      requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_PROCESSING,
    },
  ];

  function buildAvailableActions(status: RetirementStatus): AvailableButton[] {
    if (status === RetirementStatus.ADMIN_REVIEW)
      return buttons.filter(
        (button) => button.id === "viewDetails" || button.id === "managerReview" || button.id === "cancelAtAdminReview",
      );

    if (status === RetirementStatus.PORTFOLIO_MANAGER_REVIEW)
      return buttons.filter(
        (button) => button.id === "viewDetails" || button.id === "execute" || button.id === "cancelAtPMReview",
      );

    if (status === RetirementStatus.PROCESSING)
      return buttons.filter(
        (button) =>
          button.id === "viewDetails" ||
          (!isTransfer && !retirementCertificate && button.id === "uploadRetirementCert") ||
          (!isTransfer ? !!retirementCertificate && button.id === "complete" : button.id === "complete"),
      );

    if (
      status === RetirementStatus.COMPLETED ||
      status === RetirementStatus.FAILED ||
      status === RetirementStatus.CANCELED
    )
      return buttons.filter(
        (button) =>
          button.id === "viewDetails" ||
          (!isTransfer && !retirementCertificate && button.id === "uploadRetirementCert"),
      );

    return buttons;
  }

  function viewDetailsHandler(): void {
    if (transaction?.id) {
      pushToPath(transaction.id);
    }
  }

  function closeCancelationHandler(): void {
    setIsCancelDialogOpen(false);
  }

  function closeRetirementHandler(): void {
    setIsRetiteDialogOpen(false);
  }

  function closeConfirmHandler(): void {
    setIsConfirmDialogOpen(false);
  }

  function closeMgrReviewHandler(): void {
    setIsMgrReviewDialogOpen(false);
  }

  function uploadDocumentViewHandler(): void {
    pushToPath(`${transaction.id}/upload-retirement-certification`);
  }

  function refreshPage(): void {
    refresh();
  }

  const availableActions = buildAvailableActions(transaction.status as any);

  return (
    <Box>
      <Grid
        container
        spacing={0}
        flexDirection={{
          xs: "column",
          md: "row",
        }}
        alignItems={{
          xs: "flex-start",
          md: "center",
        }}
      >
        <Grid container spacing={0}>
          <Maybe condition={!isTransfer}>
            <Grid item xs={12}>
              <ItemDetails label="Beneficiary:" value={transaction?.retirement?.beneficiary ?? MISSING_DATA} />
            </Grid>
            <Grid item xs={12}>
              <ItemDetails label="Memo:" value={transaction?.memo ?? MISSING_DATA} />
            </Grid>
            <Maybe condition={!!retirementCertificate}>
              <Grid item xs={12}>
                <ItemDetails
                  label="Retirement Certificate:"
                  value={<RetirementAttachment retirementCertificate={retirementCertificate} onDelete={fetch} />}
                  spacing={{
                    value: {
                      xl: 6,
                      lg: 3,
                      md: 2.1,
                      sm: 2.1,
                      xs: 2.1,
                    },
                  }}
                  sx={{ alignItems: "baseline" }}
                />
              </Grid>
            </Maybe>
          </Maybe>
          <Maybe condition={isTransfer}>
            <Grid item xs={12}>
              <ItemDetails
                label="Transfer Credits to Account:"
                value={transaction?.transfer?.registryAccount ?? MISSING_DATA}
              />
            </Grid>
          </Maybe>
          <Grid container spacing={0} mt={4} mb={2}>
            <Grid item xs={12}>
              <ButtonGroup buttons={availableActions} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <RetirementCancellationDialog
        isOpen={isCalcelDialogOpen}
        onClose={closeCancelationHandler}
        onConfirm={enqueueSuccess}
        onError={enqueueError}
        retirement={transaction}
        refreshData={refreshPage}
      />
      <RetirementExecutionDialog
        isOpen={isRetiteDialogOpen}
        onClose={closeRetirementHandler}
        onConfirm={enqueueSuccess}
        onError={enqueueError}
        retirement={transaction}
        refreshData={refreshPage}
      />
      <RetirementConfirmationDialog
        isOpen={isConfirmDialogOpen}
        onClose={closeConfirmHandler}
        onConfirm={enqueueSuccess}
        onError={enqueueError}
        retirement={transaction}
        refreshData={refreshPage}
      />
      <RetirementMgrReviewDialog
        isOpen={isMgrReviewDialogOpen}
        onClose={closeMgrReviewHandler}
        onConfirm={enqueueSuccess}
        onError={enqueueError}
        retirement={transaction}
        refreshData={refreshPage}
      />
    </Box>
  );
};

export default RetirementSummary;
