"use client";;
import { AdminOrganizationQueryResponse } from "@rubiconcarbon/shared-types";
import NewRetirementForm from "./new";
import Page from "@components/layout/containers/page";

import type { JSX } from "react";

const NewRetirement = ({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <NewRetirementForm organizationsResponse={organizationsResponse} />
    </Page>
  );
};

export default NewRetirement;
