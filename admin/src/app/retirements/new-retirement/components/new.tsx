import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@hooks/use-navigation";
import { useBoolean, usePrevious } from "react-use";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useFieldArray } from "react-hook-form";
import { useTriggerRequest, toBoolean, toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import {
  AdminRetirementRequest,
  AdminAssetTypeQuery,
  AssetType,
  AdminBookResponse,
  RetirementType,
  uuid,
  AdminOrganizationQueryResponse,
  AdminOrganizationResponse,
  AdminGroupedAllocationWithNestedResponse,
} from "@rubiconcarbon/shared-types";
import { useEffect, useMemo, type JSX } from "react";
import { KeyboardArrowRightRounded } from "@mui/icons-material";
import { Container, Stack, Autocomplete, TextField, Button, MenuItem } from "@mui/material";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import PublicStatus from "@components/ui/public-status/public-status";
import { ProductTypeOptions } from "@constants/products";

import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { TransactionModel } from "@models/transaction";
import { getNewRetirementModel } from "@utils/helpers/transaction/get-new-transaction-models";
import RetirementProductDetails from "./retirement-product-details";
import RetirementConfirmation from "./retirement-confirmation";
import { useEnhancedForm } from "@hooks/use-enhanced-form";

import classes from "../../styles/new-form.module.scss";

const parserBlacklist = ["$", ","];

const VisibilityOptions: { label: string; value: boolean }[] = [
  {
    label: "Public",
    value: true,
  },
  {
    label: "Private",
    value: false,
  },
];

const RetirementModelResolver = classValidatorResolver(TransactionModel);

const NewRetirementForm = ({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [canConfirm, setCanConfirm] = useBoolean(false);

  const model = getNewRetirementModel();

  const {
    control,
    formState: { errors },
    trigger,
    smartSetValue,
    watch,
    resetField,
    handleSubmit,
  } = useEnhancedForm<TransactionModel>({
    resolver: RetirementModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: lineItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "orders",
  });

  const data = watch();
  const organization = data?.retirement?.organization;

  const isPortfolioSelection = useMemo(
    () => data?.retirement?.productType === AssetType.RCT,
    [data?.retirement?.productType],
  );

  const hiddenLines = isPortfolioSelection
? lineItems?.reduce<string[]>((ids, { _id }, index) => {
    if (index > 0 && _id) {
      return [...ids, _id];
    }
    return ids;
  }, [])
    : [];

  const previousSelectedOrganizationId = usePrevious(organization?.id);

  const {
    data: assetsResponse,
    trigger: getAssets,
    isMutating: loadingAssets,
  } = useTriggerRequest<AdminGroupedAllocationWithNestedResponse, null, { id: uuid }, AdminAssetTypeQuery>({
    url: "admin/organizations/{id}/holdings",
    pathParams: {
      id: organization?.id,
    },
    queryParams: {
      assetTypes: [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch assets for ${organization?.name}`);
        logger.error(`Unable to load assets for ${organization?.name}: ${error?.message}`, {});
      },
    },
  });

  const hasPortfolioProducts = useMemo(
    () => assetsResponse?.allocations?.some((s) => s.asset.type === AssetType.RCT),
    [assetsResponse?.allocations],
  );

  const hasVintageProducts = useMemo(
    () => assetsResponse?.allocations?.some((s) => s.asset.type === AssetType.REGISTRY_VINTAGE),
    [assetsResponse?.allocations],
  );

  const productTypeOptions = useMemo(
    () =>
      ProductTypeOptions.filter((option) =>
        option.value === AssetType.RCT
          ? hasPortfolioProducts
          : option.value === AssetType.REGISTRY_VINTAGE
            ? hasVintageProducts
            : false,
      ),
    [hasPortfolioProducts, hasVintageProducts],
  );

  const { trigger: commitRetirement, isMutating: commitingRetirement } = useTriggerRequest<
    any,
    AdminRetirementRequest
  >({
    url: "admin/retirements",
    method: "post",
    requestBody: {
      organizationId: data?.retirement?.organization?.id,
      isPublic: toBoolean(data?.retirement?.isPublic),
      assetType: data?.retirement?.productType,
      beneficiary: data?.retirement?.beneficiary,
      memo: data?.memo,
      type: RetirementType.RETIREMENT,
      assets: data?.orders?.map((item) => ({
        assetId: item?.supplementaryAssetDetails?.id,
        sourceId: data?.retirement?.organization?.id,
        amount: toNumber(item?.amount, { parserBlacklist }),
        rawPrice: toDecimal(0.0),
      })),
    },
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Retirement created successfully.");
        popFromPath(1);
      },
      onError: (error: any): void => {
        enqueueError("Unable to create retirement.");
        logger.error(`Unable to create retirement: ${error?.message}`, {});
      },
    },
  });

  const allocations = useMemo(() => assetsResponse?.allocations, [assetsResponse?.allocations]);

  useEffect(() => {
    const organizationId = organization?.id;
    if (!!organizationId && previousSelectedOrganizationId !== organizationId && !loadingAssets)
      setTimeout(async () => await getAssets());
  }, [organization?.id, getAssets, loadingAssets, previousSelectedOrganizationId]);

  const organizationOptions = useAutoCompleteOptions<AdminOrganizationResponse, AdminOrganizationResponse>({
    data: organizationsResponse?.data || [],
    keys: ["id", "name", "customerPortfolio"],
    label: (entry: Partial<AdminOrganizationResponse>) => entry?.name,
    value: (entry: Partial<AdminOrganizationResponse>) => entry as AdminBookResponse,
    preTransform: (data: AdminOrganizationResponse[]) => data?.filter(({ isEnabled }) => isEnabled),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const onSubmit = (): void => setCanConfirm(true);

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="retirement.organization"
          control={control}
          render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = organizationOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
            return (
              <Autocomplete
                options={organizationOptions}
                value={selectedOption}
                onChange={(_, selection) => onChange(selection?.value)}
                id="organization"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminOrganizationResponse>) => option?.value?.id}
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminOrganizationResponse>) => option?.label}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Organization"
                    {...otherProps}
                    error={!!errors?.retirement?.organization}
                    helperText={errors?.retirement?.organization?.message}
                    fullWidth
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <Controller
          name="retirement.isPublic"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Visibility"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.isPublic}
              helperText={errors?.retirement?.isPublic?.message}
              {...otherProps}
              fullWidth
            >
              {VisibilityOptions.map((option) => (
                <MenuItem key={option.label} value={option.value?.toString()}>
                  <PublicStatus isPublic={option.value} />
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <Controller
          name="retirement.productType"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Product Type"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.productType}
              helperText={
                !organization
                  ? "Please select Organization"
                  : !loadingAssets && !productTypeOptions?.length
                    ? "No Products Available to Retire"
                    : errors?.retirement?.productType?.message
              }
              {...otherProps}
              fullWidth
              disabled={loadingAssets || !organization || !productTypeOptions?.length}
            >
              {productTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <RetirementProductDetails
          loadingAssets={loadingAssets}
          allocations={allocations}
          control={control}
          errors={errors}
          lineItems={lineItems}
          trigger={trigger}
          setValue={smartSetValue}
          resetField={resetField}
          watch={watch}
          append={append}
          remove={remove}
        />
        <Controller
          name="retirement.beneficiary"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Beneficiary"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.beneficiary}
              helperText={errors?.retirement?.beneficiary?.message}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        <Controller
          name="memo"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Memo"
              value={value ?? ""}
              InputProps={{ ref, sx: { whiteSpace: "pre-wrap" } }}
              minRows={4}
              {...otherProps}
              error={!!errors.memo}
              helperText={errors.memo?.message}
              fullWidth
              multiline
            />
          )}
        />
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
            disabled={loadingAssets}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        title="Review and Confirm Retirement"
        open={canConfirm}
        onClose={() => setCanConfirm(false)}
        positiveAction={{
          buttonText: "CONFIRM RETIREMENT",
          loading: commitingRetirement,
          onClick: async () => await commitRetirement(),
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setCanConfirm(false),
        }}
        classes={{
          root: classes.Dialog,
          title: classes.Title,
          content: classes.Content,
          actions: classes.Actions,
        }}
      >
        <RetirementConfirmation
          data={{
            ...data,
            orders: data?.orders?.filter((item) => !hiddenLines?.length || !hiddenLines.includes(item?._id)),
          }}
        />
      </GenericDialog>
    </Container>
  );
};

export default NewRetirementForm;
