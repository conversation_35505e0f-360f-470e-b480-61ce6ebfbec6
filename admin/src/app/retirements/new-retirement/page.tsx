import { AuthorizeServer } from "@app/authorize-server";
import { AdminOrganizationQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import NewRetirementForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement, type JSX } from "react";

/**
 * New Retirement Page
 *
 * This is a server component that renders the New Retirement page
 */
export default async function NewRetirementPage(): Promise<JSX.Element> {
  const organizationsResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminOrganizationQueryResponse>(
      `admin/organizations?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: false,
        isCustomerPortfolio: true,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_CREATE]}>
      <NewRetirementForm organizationsResponse={organizationsResponse as AdminOrganizationQueryResponse} />
    </AuthorizeServer>
  );
}