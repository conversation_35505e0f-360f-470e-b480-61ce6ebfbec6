"use client";;
import { AdminOrganizationQueryResponse } from "@rubiconcarbon/shared-types";
import NewTransferFormComponent from "./new";
import Page from "@components/layout/containers/page";

import type { JSX } from "react";

const NewTransfer = ({
  organizationsResponse,
}: {
  organizationsResponse: AdminOrganizationQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <NewTransferFormComponent organizationsResponse={organizationsResponse} />
    </Page>
  );
};

export default NewTransfer;
