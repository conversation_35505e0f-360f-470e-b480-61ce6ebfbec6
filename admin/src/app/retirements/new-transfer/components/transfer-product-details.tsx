import { <PERSON><PERSON>, Autocomplete, Box, Divider, Grid, <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from "@mui/material";
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldErrors,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFormResetField,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
} from "react-hook-form";
import { AddCircleRounded, DeleteRounded } from "@mui/icons-material";
import { calculator, Maybe, numberFormat, toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { AdminAllocationResponse, uuid, AssetType } from "@rubiconcarbon/shared-types";
import { NumericFormat } from "react-number-format";
import { Fragment, useCallback, useMemo, type JSX } from "react";
import { MISSING_DATA } from "@constants/constants";
import { useToggle } from "react-use";
import { AssetOrder, NonTradeAssetDetails, TransactionModel } from "@models/transaction";
import usePerformantEffect from "@hooks/use-performant-effect";

import classes from "../../styles/product-details.module.scss";

type LineItemProps = {
  index: number;
  currentAccumulatedAssetQuantities: Record<uuid, number>;
  productOptions: UseAutoCompleteOptionsReturnEntry<NonTradeAssetDetails>[];
} & Pick<
  TransferProductDetailsProps,
  "loadingAssets" | "control" | "errors" | "trigger" | "setValue" | "watch" | "remove"
>;

type TransferProductDetailsProps = {
  loadingAssets: boolean;
  allocations: AdminAllocationResponse[];
  control: Control<TransactionModel, any>;
  errors: FieldErrors<TransactionModel>;
  lineItems: FieldArrayWithId<TransactionModel, "orders", "id">[];
  trigger: UseFormTrigger<TransactionModel>;
  setValue: UseFormSetValue<TransactionModel>;
  resetField: UseFormResetField<TransactionModel>;
  watch: UseFormWatch<TransactionModel>;
  append: UseFieldArrayAppend<TransactionModel, "orders">;
  remove: UseFieldArrayRemove;
};

const parserBlacklist = ["$", ","];

const LineItem = ({
  index,
  productOptions = [],
  loadingAssets,
  control,
  errors,
  trigger,
  setValue,
  watch,
  remove,
}: LineItemProps): JSX.Element => {
  const organization = watch("transfer.organization");
  const lineItem = watch(`orders.${index}`);
  const asset = lineItem?.supplementaryAssetDetails;

  const [focused, setFocused] = useToggle(false);

  const lineItemErrors = useMemo(() => errors?.orders?.at?.(index), [errors?.orders, index]);
  const grossAvailable = useMemo(() => asset?.available, [asset?.available]);
  const netAvailable = useMemo(
    () =>
      calculator(grossAvailable)
        .subtract(lineItem?.amount || 0)
        .calculate()
        .toNumber(),
    [grossAvailable, lineItem?.amount],
  );
  const positiveAvailability = useMemo(() => netAvailable >= 0, [netAvailable]);

  usePerformantEffect(() => {
    setValue(`orders.${index}.amountAvailable`, grossAvailable);
  }, [grossAvailable, index, setValue]);

  usePerformantEffect(() => {
    const subscription = watch(({ orders = [] }, { name, type }) => {
      if (type === "change") {
        const item = orders?.at(index);

        const amount = toDecimal(item?.amount, { parserBlacklist, treatNothingAsNaN: true });

        switch (name) {
          case `orders.${index}.asset`:
            if (!orders?.at(index)?.supplementaryAssetDetails) setValue(`orders.${index}.amount`, "" as any);

            if (!amount?.isNaN()) trigger(`orders.${index}.amount`);

            break;
          default:
            break;
        }
      }
    });
    return (): void => subscription.unsubscribe();
  }, [index, watch]);

const hasError = (field: keyof AssetOrder): boolean => {
  if (typeof lineItemErrors === 'object' && field in lineItemErrors) {
    return !!lineItemErrors[field];
  }
  return false;
};

  const onFocus = (): void => setFocused(true);

  const onBlur = (): void => setFocused(false);

  return (
    <Grid className={classes.BodyRow} container item alignItems="start" gap={0.5}>
      <Grid className={classes.BodyCell} item xs={8}>
        <Controller
          name={`orders.${index}.supplementaryAssetDetails`}
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = productOptions.find((option) => value?.id === option?.value?.id);

            return (
              <Autocomplete
                options={productOptions}
                getOptionDisabled={(option) => option?.disabled}
                value={selectedOption || null}
                loading={loadingAssets}
                onChange={(_, selection) => onChange(selection?.value)}
                id="asset"
                renderInput={({ InputProps, ...params }) => (
                  <Box position="relative">
                    <TextField
                      {...params}
                      InputProps={{
                        ref,
                        ...InputProps,
                        sx: {
                          position: "relative",
                          zIndex: 1,
                          color: focused ? "black" : "transparent",
                        },
                      }}
                      label="Search and select product"
                      {...otherProps}
                      error={!!lineItemErrors?.supplementaryAssetDetails}
                      helperText={
                        !hasError("supplementaryAssetDetails") && !organization
                          ? "Please select Organization"
                          : lineItemErrors?.supplementaryAssetDetails?.message
                      }
                      fullWidth
                      onFocus={onFocus}
                      onBlur={onBlur}
                    />
                    <Maybe condition={!focused && !!selectedOption}>
                      <Box
                        sx={{
                          position: "absolute",
                          top: 10,
                          left: 13,
                          right: 60,
                          bottom: 10,
                          display: "flex",
                          alignContent: "center",
                          zIndex: 2,
                          pointerEvents: "none",
                        }}
                      >
                        <Stack justifyContent="center" alignItems="flex-start">
                          <Typography variant="body1" color="black">
                            {selectedOption?.value?.registryProjectId} - {selectedOption?.value?.name}
                          </Typography>
                          <Typography variant="caption" color="GrayText">
                            {selectedOption?.value?.projectName}
                          </Typography>
                        </Stack>
                      </Box>
                    </Maybe>
                  </Box>
                )}
                renderOption={(props, option) => (
                  <li {...props} key={option.value?.id}>
                    {option?.displayLabel}
                  </li>
                )}
                fullWidth
                classes={{
                  root: classes.Autocomplete,
                  inputRoot: classes.InputRoot,
                  input: classes.Input,
                }}
                disabled={!organization}
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={2.9}>
        <Controller
          control={control}
          name={`orders.${index}.amount`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
            return (
              <NumericFormat
                allowNegative={false}
                thousandSeparator
                decimalScale={0}
                label="Enter quantity"
                value={value}
                isAllowed={({ floatValue }) => !floatValue || floatValue <= grossAvailable}
                customInput={TextField}
                InputProps={{
                  ref,
                  classes: {
                    formControl: classes.FormControl,
                    root: classes.InputRoot,
                    input: classes.Input,
                  },
                }}
                error={!!lineItemErrors?.amount}
                helperText={
                  !hasError("amount") ? (
                    !asset ? (
                      "Please select Product"
                    ) : positiveAvailability && !!grossAvailable ? (
                      `Available: ${numberFormat(grossAvailable)}`
                    ) : null
                  ) : (
                    <Stack component="span" gap={0.5}>
                      <Maybe condition={!positiveAvailability && !!grossAvailable}>
                        <span>Available: {numberFormat(grossAvailable)}</span>
                      </Maybe>
                      <Maybe
                        condition={
                          lineItemErrors?.amount?.type !== "maxOfField" ||
                          (lineItemErrors?.amount?.type === "maxOfField" && !positiveAvailability && !!grossAvailable)
                        }
                      >
                        <span>{lineItemErrors?.amount?.message}</span>
                      </Maybe>
                    </Stack>
                  )
                }
                {...otherProps}
                fullWidth
                disabled={!asset}
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={1} paddingTop="8px">
        <IconButton onClick={() => remove(index)}>
          <DeleteRounded />
        </IconButton>
      </Grid>
    </Grid>
  );
};

const TransferProductDetails = ({
  loadingAssets: loadingProducts,
  allocations = [],
  control,
  errors,
  lineItems,
  trigger,
  setValue,
  resetField,
  watch,
  append,
  remove,
}: TransferProductDetailsProps): JSX.Element => {
  const watchedLineItems = watch("orders");
  const selectedAssetIds = watchedLineItems
    ?.map(({ supplementaryAssetDetails }) => supplementaryAssetDetails?.id)
    ?.filter((id) => !!id);

  const currentAccumulatedAssetQuantities = watchedLineItems?.reduce(
    (record, { supplementaryAssetDetails: asset, amount }) => {
      if (asset?.id) {
        if (record?.[asset.id]) record[asset.id] += toNumber(amount, { parserBlacklist });
        else record[asset.id] = toNumber(amount, { parserBlacklist });
      }

      return record;
    },
    {} as Record<uuid, number>,
  );

  const productOptions = useAutoCompleteOptions<AdminAllocationResponse, NonTradeAssetDetails>({
    data: allocations,
    keys: [
      "amountAllocated",
      "amountAvailable",
      "amountPendingRetirement",
      "amountPendingCustomerTransferOutflow",
      "asset",
    ],
    label: (entry) =>
      `${entry?.asset?.registryProjectId} - ${entry?.asset?.projectVintageName} - ${entry?.asset?.name}`,
    displayLabel: (entry) => {
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <Stack>
            <span>
              {entry?.asset?.registryProjectId} - {entry?.asset?.projectVintageName}
            </span>
            <Typography variant="caption" color="GrayText">
              {entry?.asset?.name}
            </Typography>
          </Stack>
          <Typography variant="caption" color="GrayText">
            {numberFormat(entry?.amountAvailable)}
          </Typography>
        </Stack>
      );
    },
    disabled: (entry) => selectedAssetIds.includes(entry?.asset?.id),
    preTransform: (entry: AdminAllocationResponse[]) =>
      entry?.filter(({ asset }) => asset?.type === AssetType.REGISTRY_VINTAGE), // added this as an extra layer of filtering if the BE fails to filter
    value: (entry) => ({
      id: entry?.asset?.id,
      type: "vintage",
      name: entry?.asset?.projectVintageName,
      available: entry?.amountAvailable ?? 0,
      projectName: entry?.asset?.name,
      registryProjectId: entry?.asset?.registryProjectId,
    }),
    postTransform: (options) =>
      options
        .filter((option) => !!option?.value?.available)
        .sort((a, b) => {
          const firstOrder = a.value?.registryProjectId?.localeCompare(b.value?.registryProjectId);

          if (!firstOrder) return a.value?.name?.localeCompare(b.value?.name);

          return firstOrder;
        }),
  });

  const addNewLineItem = useCallback((): void => {
    append(new AssetOrder());
    if (!!errors?.orders) resetField("orders", { keepDirty: false, keepError: false });
  }, [append, errors?.orders, resetField]);

  return (
    <Stack className={classes.ProductDetails} gap={1}>
      <Typography color="black">Product Details</Typography>
      <Grid className={classes.Table} container>
        <Grid className={classes.Header} container item gap={0.5}>
          <Grid className={classes.HeaderCell} item xs={8}>
            <Typography variant="body2">Product</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={2.9}>
            <Typography variant="body2">Quantity</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={1}>
            <Typography variant="body2">Actions</Typography>
          </Grid>
        </Grid>
        <Grid className={classes.Body} container item justifyContent={"center"} gap={1}>
          <Maybe condition={!!lineItems?.length}>
            {lineItems?.map((lineItem, index) => (
              <Fragment key={lineItem?.id}>
                <LineItem
                  index={index}
                  currentAccumulatedAssetQuantities={currentAccumulatedAssetQuantities}
                  productOptions={productOptions}
                  loadingAssets={loadingProducts}
                  control={control}
                  errors={errors}
                  trigger={trigger}
                  setValue={setValue}
                  watch={watch}
                  remove={remove}
                />
                <Maybe condition={index !== lineItems?.length - 1}>
                  <Divider sx={{ width: "100%" }} />
                </Maybe>
              </Fragment>
            ))}
          </Maybe>
          <Maybe condition={!lineItems?.length}>
            <Alert
              classes={{
                root: `${classes.Alert}${errors?.orders ? ` ${classes.Error}` : ""}`,
                icon: classes.Icon,
                message: classes.Message,
              }}
              severity={!!errors?.orders ? "error" : "info"}
            >
              <Typography>Please add a product entry</Typography>
            </Alert>
          </Maybe>
        </Grid>
        <Maybe condition={!!lineItems?.length}>
          <Grid className={classes.Footer} container item justifyContent={"center"} gap={0.5}>
            <Divider sx={{ width: "100%" }} />
            <Grid className={classes.FooterRow} container item alignItems="center" gap={1} justifyContent="flex-end">
              <Grid item xs={7.3} />
              <Grid
                className={classes.FooterCell}
                container
                item
                xs={2.9}
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography className={classes.Label} variant="caption">
                  Total Quantity:
                </Typography>
                <Typography className={classes.Value} variant="body2">
                  {numberFormat(
                    watchedLineItems?.reduce((sum, { amount }) => sum + toNumber(amount, { parserBlacklist }), 0),
                    { fallback: MISSING_DATA },
                  )}
                </Typography>
              </Grid>
              <Grid item xs={1} />
            </Grid>
          </Grid>
        </Maybe>
      </Grid>
      <Box display="flex" justifyContent="flex-end">
        <IconButton color="primary" onClick={addNewLineItem}>
          <AddCircleRounded fontSize="large" />
        </IconButton>
      </Box>
    </Stack>
  );
};

export default TransferProductDetails;
