import { TrimmedBookResponse } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { CSSProperties, type JSX } from "react";
import COLORS from "../theme/colors";

const CustomerPortfolio = ({
  portfolio,
  style,
}: {
  portfolio?: TrimmedBookResponse;
  style?: CSSProperties;
}): JSX.Element => {
  return (
    <Link
      href={`/customer-management/organizations/${portfolio?.organization?.id}`}
      style={{
        ...style,
        color: COLORS.rubiconGreen,
        textUnderlineOffset: 4,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {portfolio?.name}
    </Link>
  );
};

export default CustomerPortfolio;
