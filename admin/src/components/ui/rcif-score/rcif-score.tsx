import { Box, SxProps, Tooltip, Typography } from "@mui/material";
import { isEmpty } from "lodash";
import COLORS from "../theme/colors";

import type { JSX } from "react";

const rcifScoreStyle = {
  backgroundColor: "#B3E5FC",
  padding: "5px",
  borderRadius: "4px",
  color: COLORS.black,
  textAlign: "center",
  width: "100%",
};

interface RCIFScoreProps {
  score: string;
  label?: string;
  tooltip?: string;
  style?: SxProps;
}

export default function RCIFScore(props: RCIFScoreProps): JSX.Element {
  const { score, tooltip, label, style } = props;
  return (
    <>
      {score === null || score === "" ? (
        "N/A"
      ) : (
        <Box sx={rcifScoreStyle}>
          <Tooltip title={isEmpty(tooltip) ? "" : tooltip}>
            <Typography variant="body2" sx={{ ...style }}>
              {`${!isEmpty(label) ? `${label}: ` : ""}${(+score).toFixed(2)}`}
            </Typography>
          </Tooltip>
        </Box>
      )}
    </>
  );
}
