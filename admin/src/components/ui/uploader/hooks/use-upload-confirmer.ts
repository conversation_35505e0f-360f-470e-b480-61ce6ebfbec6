import { useState, useCallback, MouseE<PERSON>, Dispatch, SetStateAction } from "react";

type UseUploadConfirmerReturn = {
  show: boolean;
  setShow: Dispatch<SetStateAction<boolean>>;
  continueUpload: (event: MouseEvent<HTMLButtonElement>) => Promise<void>;
};

const useUploadConfirmer = (
  handleUpload: (event: MouseEvent<HTMLButtonElement>) => Promise<void>,
): UseUploadConfirmerReturn => {
  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);

  const continueUpload = useCallback(
    async (event: MouseEvent<HTMLButtonElement>) => {
      setShowConfirmationModal(false);
      await handleUpload(event);
    },
    [setShowConfirmationModal, handleUpload],
  );

  return {
    show: showConfirmationModal,
    setShow: setShowConfirmationModal,
    continueUpload,
  };
};

export default useUploadConfirmer;
