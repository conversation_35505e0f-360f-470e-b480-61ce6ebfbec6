import { useMemo } from "react";
import { FileDataRecord, FileData } from "../types/hook";
import { hasError } from "../utils/hook";

const useUploaderFeedbackData = (records: FileDataRecord): FileData[] => {
  const feedbackRecords = useMemo(() => {
    const sorted = Object.values(records).sort((fileA, fileB) => {
      if (hasError(fileA.errors) && !hasError(fileB.errors)) return -1;
      else if (!hasError(fileA.errors) && hasError(fileB.errors)) return 1;
      return 0;
    });
    return sorted;
  }, [records]);

  return feedbackRecords;
};

export default useUploaderFeedbackData;
