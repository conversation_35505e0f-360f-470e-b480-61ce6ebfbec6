import { CounterpartyFeeType } from "@rubiconcarbon/shared-types";

export const FeeTypeOptions = [
  {
    label: "Percentage of Transaction Value (%)",
    value: CounterpartyFeeType.PERCENTAGE,
  },
  {
    label: "Per-Tonne Rate ($)",
    value: CounterpartyFeeType.PER_TONNE,
  },
  {
    label: "Fix Fee ($)",
    value: CounterpartyFeeType.FIXED,
  },
];

export const FeeTypeUILabel: Record<CounterpartyFeeType, string> = {
  [CounterpartyFeeType.PERCENTAGE]: "Percentage of Transaction Value (%)",
  [CounterpartyFeeType.PER_TONNE]: "Per-Tonne Rate ($)",
  [CounterpartyFeeType.FIXED]: "Fix Fee ($)",
};
