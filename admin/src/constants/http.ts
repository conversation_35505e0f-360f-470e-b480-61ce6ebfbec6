export const HttpStatusLabels = {
  SUCCESS: "success",
  CREATED: "created",
  ERROR: "error", // not a typical http status
  BADREQUEST: "badrequest",
  UNAUTHORIZED: "unauthorized",
  FORBIDDEN: "forbidden",
  NOTFOUND: "notfound",
  NEXTNOTFOUND: "NEXT_NOT_FOUND",
};

export const HttpStatusCodes = {
  SUCCESS: 200,
  CREATED: 201,
  BADREQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOTFOUND: 404,
};

export const HttpStatusMapper: Record<number, string> = {
  200: HttpStatusLabels.SUCCESS,
  201: HttpStatusLabels.CREATED,
  400: HttpStatusLabels.BADREQUEST,
  401: HttpStatusLabels.UNAUTHORIZED,
  403: HttpStatusLabels.FORBIDDEN,
  404: HttpStatusLabels.NOTFOUND,
};
