"""Market Intelligence methods."""

from datetime import date, datetime
from typing import Any, Dict, List, NotRequired, Optional, TypedDict

from pydantic import BaseModel, <PERSON><PERSON>
from sqlalchemy import BindParameter, bindparam, select, text

from api.data import execute_sql_query

from ..db import rubicon_engine
from ..models.market_intelligence import Report as ReportModel


class Report(BaseModel):  # noqa: D101
    id: int  # noqa: A003
    date: date
    updated_at: datetime
    doc: Optional[Any] = None


class ReportParameters(TypedDict):  # noqa: D101
    start_date: NotRequired[date]
    end_date: NotRequired[date]
    limit: int
    offset: int


async def reports(  # noqa: D103
    limit: int,
    offset: int,
    start_date: Optional[date],
    end_date: Optional[date],
) -> List[Report]:  # noqa: D103
    params: ReportParameters = {"limit": limit, "offset": offset}
    bind_params: List[BindParameter] = []
    where_clause = ["true"]

    # Inclusive date range on both ends
    if start_date:
        where_clause.append("date >= :start_date")
        params["start_date"] = start_date
        bind_params.append(bindparam("start_date"))
    if end_date:
        where_clause.append("date <= :end_date")
        params["end_date"] = end_date
        bind_params.append(bindparam("end_date"))
    query = text(
        f"""
            SELECT distinct on (date) id, date, updated_at, doc
            from market_intelligence.report
            WHERE {' and '.join(where_clause)}
            order by date desc, updated_at desc
            LIMIT :limit OFFSET :offset
        """
    )

    query = query.bindparams(*bind_params)

    df, meta = await execute_sql_query(query, params)
    hits: List[Report] = [Report(**r) for r in df.to_dict("records")]
    return hits


async def last_report(dt: date) -> Report | None:  # noqa: D103
    async with rubicon_engine.begin() as conn:
        last = (
            await conn.execute(
                select(ReportModel).limit(1).order_by(ReportModel.updated_at.desc()).where(ReportModel.date == dt),
            )
        ).first()

    if not last:
        return None

    return Report(**last._asdict())


async def source(id_: int) -> Dict[str, Json] | None:  # noqa: D103
    async with rubicon_engine.begin() as conn:
        result = (
            await conn.execute(text("SELECT * FROM market_intelligence.source WHERE id=:id"), {"id": id_})
        ).first()

    return result._asdict() if result else None
