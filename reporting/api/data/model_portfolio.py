"""Quote Data methods."""

from decimal import Decimal
from typing import List

from sqlalchemy import BindParameter, text

from api.data import execute_sql_query_without_cache


async def model_portfolio_data(model_id: str):
    """Return the project details to use with the report generation."""
    params = {"model_id": model_id}
    bind_params: List[BindParameter] = []

    query = text(
        """
        SELECT
			model_portfolios.name,
            price_estimate,
            include_risk_adjustment,
            model_portfolio_components.project_id         AS project_id,
            model_portfolio_components.quantity AS amount,
            projects.name                      AS project_name,
            projects.registry_project_id       AS registry_project_id,
            coalesce(project_vintages.label, model_portfolio_components.project_vintage_interval)   AS vintage_label,
            project_types.type                 AS project_type,
			case
				when projects.integrity_grade_score >= 84.5 then 'A'
				when projects.integrity_grade_score < 84.5 and projects.integrity_grade_score >= 74.5 then 'B'
				when projects.integrity_grade_score < 74.5 and projects.integrity_grade_score >= 59.5 then 'C'
				when projects.integrity_grade_score < 59.5 then 'D' end
												AS risk_score,
            countries.name                     AS country,
            NOT project_flags_v2.has_balance      AS pipeline
        FROM rubicon.model_portfolios
            JOIN rubicon.model_portfolio_components
                ON model_portfolio_id = model_portfolios.id
            JOIN rubicon.projects
                ON projects.id =  model_portfolio_components.project_id
            LEFT JOIN rubicon.project_vintages
                ON project_vintages.id =  model_portfolio_components.project_vintage_id
            JOIN rubicon.project_flags_v2
                ON projects.id = project_flags_v2.id
            JOIN rubicon.project_types
                ON project_type_id = project_types.id
            JOIN rubicon.countries
                ON countries.alpha3 = projects.country_code
        WHERE  model_portfolios.id = :model_id
            AND NOT model_portfolios.is_deleted
            AND NOT model_portfolio_components.is_deleted
            AND NOT is_buffer_component
        ORDER BY project_flags_v2.has_balance DESC, registry_project_id
    """,
    ).bindparams(*bind_params)

    df, metadata = await execute_sql_query_without_cache(query, params)

    if len(df) == 0:
        return {}, metadata

    price: Decimal = df.iloc[0]["price_estimate"]
    quantity = int(df["amount"].sum())
    per_tonne = price / quantity
    risk_adjusted: bool = bool(df.iloc[0]["include_risk_adjustment"])
    components = df[
        [
            "project_name",
            "registry_project_id",
            "vintage_label",
            "project_type",
            "risk_score",
            "country",
            "amount",
            "project_id",
            "pipeline",
        ]
    ]

    return {
        "name": df.iloc[0]["name"],
        "price": price,
        "quantity": quantity,
        "per_tonne": per_tonne,
        "risk_adjusted": risk_adjusted,
        "components": components.to_dict("records"),
    }, metadata
