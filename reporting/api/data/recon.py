"""Reconciliation methods."""

import json
from typing import Any, List, Optional
from uuid import UUID

import pandera
from pandera.typing import DataFrame, Series
from pydantic import BaseModel
from sqlalchemy import text

from ..db import writer_engine
from . import Data, execute_sql_query_without_cache
from .types import TimestampUTC, empty_dataframe


class ReconNote(BaseModel):
    date: int
    reason: str


class ReconItemProps(BaseModel):
    """Recon item properties."""

    line_item_transaction_id: UUID
    registry: str
    registry_item_id: str
    match: bool
    reconciled: bool
    notes: Optional[List[ReconNote]] = None
    line_item_transaction_amount: int
    registry_item_amount: int


SOURCES_MAP = {
    "internal": "RubiconCarbon Platform",
    "acr": "ACR",
    "verra": "Verra",
    "gs": "Gold Standard",
}


def verra_range_to_label(x: str) -> str:
    """Convert DD/MM/YYYY-DD/MM/YYYY to YYYY or YYYY-YYYY."""
    start = x[6:10]
    end = x[17:21]

    return f"{start}-{end}" if start != end else start


class RetirementsSchema(pandera.DataFrameModel):  # noqa: D101
    source: Series[str]
    internal_id: Series[str] = pandera.Field(nullable=True)
    credits: Series[int]  # noqa: A003
    registry: Series[str]
    registry_project_id: Series[str]
    vintage: Series[str]
    transaction_key: Series[str] = pandera.Field(nullable=True)
    transaction_id: Series[str] = pandera.Field(nullable=True)
    date_started: Series[TimestampUTC] = pandera.Field(nullable=True)
    date_finished: Series[TimestampUTC] = pandera.Field(nullable=True)


class TransactionsSchema(pandera.DataFrameModel):  # noqa: D101
    source: Series[str]
    internal_id: Series[str] = pandera.Field(nullable=True)
    credits: Series[int]  # noqa: A003
    registry: Series[str]
    registry_project_id: Series[str]
    vintage: Series[str]
    date_acquired: Series[TimestampUTC] = pandera.Field(nullable=True)


class TransactionsReportSchema(pandera.DataFrameModel):  # noqa: D101
    created_at: Series[TimestampUTC] = pandera.Field(nullable=True)
    registry: Series[str]
    registry_project_id: Series[str]
    name: Series[str]
    vintage: Series[str]
    amount: Series[int]
    registry_amount: Series[int]
    match: Series[bool]
    reconciled: Series[bool]
    notes: Series[Any]
    line_item_id: Series[str]
    settlement_date: Series[TimestampUTC]


class RetirementsReportSchema(pandera.DataFrameModel):  # noqa: D101
    created_at: Series[TimestampUTC] = pandera.Field(nullable=True)
    registry: Series[str]
    registry_project_id: Series[str]
    name: Series[str]
    vintage: Series[str]
    amount: Series[int]
    registry_amount: Series[int]
    match: Series[bool]
    reconciled: Series[bool]
    notes: Series[Any]
    transaction_key: Series[str]
    date_started: Series[TimestampUTC]
    date_finished: Series[TimestampUTC]
    beneficiary: Series[str]


async def recon_retirements(registry: str = None, internal: bool = None) -> Data[DataFrame[RetirementsSchema]]:
    """Return the retirement items that don't match."""
    query = """
        SELECT
            'internal' AS source,
            id::text AS internal_id,
            retired AS credits,
            registry,
            registry_project_id,
            vintage,
            transaction_key,
            transaction_id::text,
            date_started,
            date_finished
        FROM recon.mismatch_retirements_internal

        UNION ALL

        SELECT
            'acr', id::text AS internal_id, quantity, 'American Carbon Registry',
            project_id, vintage, null, null, status_effective, status_effective
        FROM recon.mismatch_retirements_acr

        UNION ALL

        SELECT
            'verra', id::text AS internal_id, quantity, 'Verra', project_id,
            vintage, null, null, retirement_date, retirement_date
        FROM recon.mismatch_retirements_verra

        UNION ALL

        SELECT
            'gs', id::text AS internal_id, number_of_credits, 'Gold Standard',
            project, vintage, null, null, created_at, updated_at
        FROM recon.mismatch_retirements_gs
    """

    df, meta = await execute_sql_query_without_cache(query)
    if registry:
        if internal:
            df = df[(df["registry"].str.lower() == registry) & (df["source"] == "internal")]
        else:
            df = df[(df["registry"].str.lower() == registry) & (df["source"] != "internal")]
    df["vintage"] = df["vintage"].astype(str)
    df.loc[df["source"] == "verra", "vintage"] = df.loc[df["source"] == "verra", "vintage"].map(
        verra_range_to_label,
    )  # rename the Verra vintages
    df.loc[df["source"] != "internal", "credits"] *= -1  # if it's not an internal retirement, mark it as negative
    df["source"] = df["source"].apply(SOURCES_MAP.get)

    if len(df) == 0:
        df = empty_dataframe(RetirementsSchema)

    return df.pipe(DataFrame[RetirementsSchema]), meta


async def recon_transactions(registry: str = None, internal: bool = None) -> Data[DataFrame[TransactionsSchema]]:
    """Return the purchased items that don't match."""
    query = """
        SELECT
            'internal' AS source,
            'Purchase' as transaction_type,
            id::text AS internal_id,
            amount AS credits,
            registry,
            registry_project_id,
            vintage,
            transaction_key,
            transaction_id::text,
			date_acquired
        FROM recon.mismatch_purchases_internal

        UNION ALL

        SELECT
            'internal' AS source,
            'Transfer' as transaction_type,
            id::text AS internal_id,
            retired AS credits,
            registry,
            registry_project_id,
            vintage,
            transaction_key,
            transaction_id::text,
			date_finished
        FROM recon.mismatch_transfers_internal

        UNION ALL

        SELECT
            'internal' AS source,
            'Sale' as transaction_type,
            id::text AS internal_id,
            amount AS credits,
            registry,
            registry_project_id,
            vintage,
            transaction_key,
            transaction_id::text,
			date_acquired
        FROM recon.mismatch_sales_internal

        UNION ALL

        SELECT
            'acr',
            'Purchase' as transaction_type,
            id::text AS internal_id,
            quantity as credits,
            'American Carbon Registry' as registry,
            project_id as registry_project_id,
            vintage,
            null,
            null,
            date_issued
        FROM recon.mismatch_purchases_acr

        UNION ALL

        SELECT
            'verra',
            'Purchase' as transaction_type,
            id::text AS internal_id,
            quantity,
            'Verra',
            project_id,
            vintage,
            null,
            null,
            date_issued
        FROM recon.mismatch_purchases_verra

        UNION ALL

        SELECT
            'gs',
            'Purchase' as transaction_type,
            id::text AS internal_id,
            number_of_credits,
            'Gold Standard',
            project,
            vintage,
            null,
            null,
            created_at
        FROM recon.mismatch_purchases_gs

        UNION ALL

        SELECT
            'acr',
            'Sale' as transaction_type,
            id::text AS internal_id,
            quantity as credits,
            'American Carbon Registry' as registry,
            project_id as registry_project_id,
            vintage,
            null,
            null,
            date_issued
        FROM recon.mismatch_sales_acr

        UNION ALL

        SELECT
            'verra',
            'Sale' as transaction_type,
            id::text AS internal_id,
            quantity,
            'Verra',
            project_id,
            vintage,
            null,
            null,
            date_issued
        FROM recon.mismatch_sales_verra
    """

    df, meta = await execute_sql_query_without_cache(query)
    if registry:
        if internal:
            df = df[(df["registry"].str.lower() == registry) & (df["source"] == "internal")]
        else:
            df = df[(df["registry"].str.lower() == registry) & (df["source"] != "internal")]
    df["vintage"] = df["vintage"].astype(str)
    df.loc[df["source"] == "verra", "vintage"] = df.loc[df["source"] == "verra", "vintage"].map(
        verra_range_to_label,
    )  # rename the Verra vintages
    df.loc[df["source"] == "internal", "credits"] *= -1  # if it's an internal purchase, mark it as negative
    df["source"] = df["source"].apply(SOURCES_MAP.get)

    if len(df) == 0:
        df = empty_dataframe(TransactionsSchema)

    return df.pipe(DataFrame[TransactionsSchema]), meta


async def recon_transactions_report() -> Data[DataFrame[TransactionsReportSchema]]:
    """Return the transactions report."""
    query = """
        SELECT
            P.created_at,
            P.registry,
            PI.registry_project_id,
            PI.name,
            PI.label as vintage,
            line_item_amount as amount,
            registry_item_amount as registry_amount,
            match,
            reconciled,
            notes,
            P.line_item_id,
            LI.settlement_date::timestamptz as settlement_date
        FROM recon.purchases P
        JOIN recon.purchases_internal PI ON PI.id = P.line_item_id
        JOIN rubicon.line_items LI ON PI.id = LI.id
        ORDER BY settlement_date, P.created_at
    """

    df, meta = await execute_sql_query_without_cache(query)
    df["vintage"] = [str(d) for d in df["vintage"]]
    df["line_item_id"] = [str(u) for u in df["line_item_id"]]

    return df.pipe(DataFrame[TransactionsReportSchema]), meta


async def recon_retirements_report() -> Data[DataFrame[RetirementsReportSchema]]:
    """Return the retirements report."""
    query = """
        SELECT
            created_at,
            R.registry,
            RI.registry_project_id,
            RI.name,
            RI.label as vintage,
            R.line_item_transaction_amount as amount,
            R.registry_item_amount as registry_amount,
            match,
            reconciled,
            notes,
            transaction_key,
            date_started::timestamptz as date_started,
            date_finished::timestamptz as date_finished,
            beneficiary
        FROM recon.retirements R
        JOIN recon.retirements_internal RI ON RI.id = R.line_item_transaction_id
        ORDER BY date_started, date_finished, created_at
    """

    df, meta = await execute_sql_query_without_cache(query)
    df["vintage"] = [str(d) for d in df["vintage"]]

    return df.pipe(DataFrame[RetirementsReportSchema]), meta


async def create_recon_retirements(retirements: list[ReconItemProps]) -> int:
    """Create multiple retirement reconciliation items.

    Args:
        retirements: List of dictionaries containing data for each reconciliation record

    """
    transaction_dicts = []
    for transaction in retirements:
        # Convert to dict
        t_dict = transaction.model_dump()

        # Explicitly convert notes to JSON string
        if t_dict["notes"] is not None:
            t_dict["notes"] = json.dumps(t_dict["notes"])

        transaction_dicts.append(t_dict)
    query = """
        INSERT INTO recon.retirements_v2
        (line_item_transaction_id, registry, registry_item_id, match, reconciled,
         notes, line_item_transaction_amount, registry_item_amount)
        VALUES (:line_item_transaction_id, :registry, :registry_item_id, :match, :reconciled,
                :notes, :line_item_transaction_amount, :registry_item_amount)
    """

    async with writer_engine.begin() as conn:
        result = await conn.execute(text(query), transaction_dicts)

    return result.rowcount


async def create_recon_transactions(transactions: list[ReconItemProps]) -> int:
    """Create multiple retirement reconciliation items.

    Args:
        transactions: List of dictionaries containing data for each reconciliation record

    """
    transaction_dicts = []
    for transaction in transactions:
        # Convert to dict
        t_dict = transaction.model_dump()

        # Explicitly convert notes to JSON string
        if t_dict["notes"] is not None:
            t_dict["notes"] = json.dumps(t_dict["notes"])

        transaction_dicts.append(t_dict)

    query = """
        INSERT INTO recon.purchases_v2
        (line_item_id, registry, registry_item_id, match, reconciled,
         notes, line_item_amount, registry_item_amount)
        VALUES (:line_item_transaction_id, :registry, :registry_item_id, :match, :reconciled,
                :notes, :line_item_transaction_amount, :registry_item_amount)
    """

    async with writer_engine.begin() as conn:
        result = await conn.execute(text(query), transaction_dicts)

    return result.rowcount


async def recon_transactions_recommendations() -> Data[DataFrame[TransactionsSchema]]:
    """Return the purchased items that don't match."""
    query = """
        SELECT internal.id as line_item_transaction_id, 'Verra' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_purchases_internal
        where registry='Verra'
        ) internal
        JOIN recon.mismatch_purchases_verra external
        ON internal.amount = external.quantity AND internal.registry_project_id = 'VCS' || external.project_id
        AND to_char(lower(internal.interval), 'DD/MM/YYYY') || '-' || to_char(upper(internal.interval) - 1, 'DD/MM/YYYY') = external.vintage

        UNION ALL
        
        SELECT internal.id as line_item_transaction_id, 'Verra' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM
        (
        select * from recon.mismatch_sales_internal
        where registry='Verra'
        ) internal
        JOIN recon.mismatch_sales_verra external
        ON internal.amount = external.quantity AND internal.registry_project_id = 'VCS' || external.project_id
        AND to_char(lower(internal.interval), 'DD/MM/YYYY') || '-' || to_char(upper(internal.interval) - 1, 'DD/MM/YYYY') = external.vintage
        
        UNION ALL
        
        SELECT internal.id as line_item_transaction_id, 'Verra' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM
        (
        select * from recon.mismatch_transfers_internal
        where registry='Verra'
        ) internal
        JOIN recon.mismatch_sales_verra external
        ON internal.retired = external.quantity AND internal.registry_project_id = 'VCS' || external.project_id
        AND to_char(lower(internal.interval), 'DD/MM/YYYY') || '-' || to_char(upper(internal.interval) - 1, 'DD/MM/YYYY') = external.vintage

        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'ACR' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_purchases_internal
        where registry='American Carbon Registry'
        ) internal
        JOIN recon.mismatch_purchases_acr external
        ON internal.amount = external.quantity AND internal.registry_project_id = external.project_id
        AND internal.vintage = external.vintage
        
        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'ACR' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_sales_internal
        where registry='American Carbon Registry'
        ) internal
        JOIN recon.mismatch_sales_acr external
        ON internal.amount = external.quantity AND internal.registry_project_id = external.project_id
        AND internal.vintage = external.vintage
        
        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'ACR' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_transfers_internal
        where registry='American Carbon Registry'
        ) internal
        JOIN recon.mismatch_sales_acr external
        ON internal.retired = external.quantity AND internal.registry_project_id = external.project_id
        AND internal.vintage = external.vintage

        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'GS' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.number_of_credits as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_purchases_internal
        where registry='Gold Standard'
        ) internal
        JOIN recon.mismatch_purchases_gs external
        ON internal.amount = external.number_of_credits AND internal.registry_project_id = external.project
        AND internal.vintage = external.vintage
        
        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'GS' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.amount as line_item_transaction_amount, external.number_of_credits as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_sales_internal
        where registry='Gold Standard'
        ) internal
        JOIN recon.mismatch_purchases_gs external
        ON internal.amount = external.number_of_credits AND internal.registry_project_id = external.project
        AND internal.vintage = external.vintage
        
        UNION ALL

        SELECT internal.id as line_item_transaction_id, 'GS' as registry, external.id as registry_item_id, true as match
        , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.number_of_credits as registry_item_amount, transaction_key
        FROM 
        (
        select * from recon.mismatch_transfers_internal
        where registry='Gold Standard'
        ) internal
        JOIN recon.mismatch_purchases_gs external
        ON internal.retired = external.number_of_credits AND internal.registry_project_id = external.project
        AND internal.vintage = external.vintage
    """

    df, meta = await execute_sql_query_without_cache(query)

    if len(df) == 0:
        df = empty_dataframe(TransactionsSchema)

    return df.pipe(DataFrame[TransactionsSchema]), meta


async def recon_retirements_recommendations() -> Data[DataFrame[TransactionsSchema]]:
    """Return the purchased items that don't match."""
    query = """
        SELECT DISTINCT ON (registry_item_id) * FROM 
            (
            SELECT DISTINCT ON (internal.id) internal.id as line_item_transaction_id, 'ACR' as registry, external.id as registry_item_id, true as match
            , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
            FROM 
            (
            select * from recon.mismatch_retirements_internal
            where registry='American Carbon Registry'
            ) internal
            JOIN recon.mismatch_retirements_acr external
            ON (-1*internal.retired) = external.quantity AND internal.registry_project_id = external.project_id
            AND external.vintage = internal.vintage

            UNION ALL

            SELECT DISTINCT ON (internal.id) internal.id as line_item_transaction_id, 'Verra' as registry, external.id as registry_item_id, true as match
            , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.quantity as registry_item_amount, transaction_key
            FROM 
            (
            select * from recon.mismatch_retirements_internal
            where registry='Verra'
            ) internal
            JOIN recon.mismatch_retirements_verra external
            ON (-1*internal.retired) = external.quantity AND internal.registry_project_id = external.project_id
            AND to_char(lower(internal.interval), 'DD/MM/YYYY') || '-' || to_char(upper(internal.interval) - 1, 'DD/MM/YYYY') = external.vintage


            UNION ALL

            SELECT DISTINCT ON (internal.id) internal.id as line_item_transaction_id, 'Gold Standard' as registry, external.id as registry_item_id, true as match
            , true as reconciled, '[]'::jsonb as notes, internal.retired as line_item_transaction_amount, external.number_of_credits as registry_item_amount, transaction_key
            FROM 
            (
            select * from recon.mismatch_retirements_internal
            where registry='Gold Standard'
            ) internal
            JOIN recon.mismatch_retirements_gs external
            ON (-1*internal.retired) = external.number_of_credits AND internal.registry_project_id = external.registry_project_id
            AND external.vintage = internal.vintage
) Q
    """

    df, meta = await execute_sql_query_without_cache(query)

    if len(df) == 0:
        df = empty_dataframe(TransactionsSchema)

    return df.pipe(DataFrame[TransactionsSchema]), meta
