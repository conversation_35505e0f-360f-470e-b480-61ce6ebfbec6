"""Report Download methods."""

from io import BytesIO
from typing import List

import numpy
import pandas
from sqlalchemy import text

from . import execute_sql_query_without_cache


async def holding_analytics() -> BytesIO:
    """Generate Holding Analytics file."""
    holding_query = text(
        """WITH pending_trades AS
                (SELECT registry_project_id ,
                        label ,
                        sum(amount) filter (WHERE action = 'buy') AS "pending_buy" ,
                        sum(amount) filter (WHERE action in ('sell', 'purchase')) AS "pending_sell"
                FROM
                    (SELECT t.trade_key,
                            p.registry_project_id,
                            p.name,
                            pv.label,
                            af.amount,
                            af.action,
                            af.status
                    FROM rubicon.asset_flows af
                    join rubicon.trades t on af.related_id = t.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    WHERE (t.status NOT IN ('settled','canceled'))

                    UNION

                    select
                        transactions.transaction_key,
                        p.registry_project_id,
                        p.name,
                        pv.label,
                        af.amount,
                        af.action,
                        af.status
                    from rubicon.transactions 
                    join rubicon.asset_flows af on af.related_id = transactions.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    where type = 'purchase' and transactions.status = 'processing' and af.asset_type = 'registry_vintage') AS virtual_table
                GROUP BY 1, 2),
                    pending_retirements AS
                (WITH average_cost AS
                    (SELECT asset_flows.related_id AS transaction_id,
                            asset_flows.asset_id AS project_vintage_id,
                            SUM(asset_flows.amount) AS amount
                    FROM rubicon.asset_flows
                    GROUP BY asset_flows.related_id,
                            asset_flows.asset_id)
                SELECT  projects.registry_project_id AS registry_project_id,
                        project_vintages.label AS label,
                        sum(average_cost.amount) AS pending_retirement
                FROM rubicon.asset_flows
                JOIN rubicon.project_vintages ON project_vintages.id = asset_flows.asset_id
                JOIN rubicon.projects ON projects.id = project_vintages.project_id
                JOIN average_cost ON average_cost.transaction_id = asset_flows.related_id
                    AND average_cost.project_vintage_id = asset_flows.asset_id
                WHERE asset_flows.action = 'retire'
                    AND asset_flows.status = 'pending'
                GROUP BY 1, 2),
                    current_allocations AS
                (SELECT ac.component_id AS project_vintage_id,
                        sum(ac.holdings) filter (WHERE b.TYPE IN ('portfolio:public', 'portfolio:customer')) AS allocated_rct,
                        sum(ac.holdings) filter (WHERE b.TYPE IN ('portfolio:custom')) AS allocated_custom,
                        sum(ac.holdings) filter (WHERE b.TYPE IN ('portfolio:reserves')) AS allocated_reserve
                FROM rubicon.asset_composition_v2 ac
                JOIN rubicon.books b ON b.id = ac.parent_id
                WHERE ac.asset_type = 'registry_vintage'
                    AND ac.LEVEL = 1
                GROUP BY 1),
                    forward_deliveries AS
                (select fli.project_vintage_id
                        , sum(fli.expected_amount) filter (where f.type = 'buy') expected_amount_buy
                         , sum(fli.expected_amount) filter (where f.type = 'sell') expected_amount_sell
                        , array_agg(fli.last_updated_delivery_date::date::text) as last_updated_delivery_date
                  from rubicon.forwards f
                  join rubicon.forward_line_items as fli on fli.forward_id = f.id
                  where fli.status = 'pending'
                  group by 1)
                SELECT P.registry_project_id AS "Registry Project ID",
                    P.name AS "Project",
                    PV.label AS "Vintage",
                    C.name AS "Country",
                    C.region AS "Region",
                    C.sub_region AS "Sub Region",
                    CASE
                        WHEN P.rct_standard THEN P.rct_standard
                        ELSE NULL
                    END AS "RCT Standard",
                    CASE
                        WHEN P.suspended THEN P.suspended
                        ELSE NULL
                    END AS "Suspended",
                    CASE
                        WHEN P.is_science_team_approved THEN P.is_science_team_approved
                        ELSE NULL
                    END AS "Website Ready",
                    CASE
                        WHEN P.rct_standard
                                AND p.is_science_team_approved
                                AND pv.risk_buffer_percentage < 1
                                AND NOT p.suspended THEN TRUE
                        ELSE NULL
                    END AS "RCT Portfolio Eligible",
                    PVA.holdings AS "Holdings",
                    current_allocations.allocated_rct AS "Allocated to RCT (QTY)",
                    current_allocations.allocated_custom AS "Allocated to Custom (QTY)",
                    current_allocations.allocated_reserve AS "Allocated to Reserves (QTY)",
                    pending_trades.pending_buy AS "Pending Buy",
                    -1 * pending_trades.pending_sell AS "Pending Sell",
                    -1 * pending_retirements.pending_retirement AS "Pending Retirement",
                    forward_deliveries.expected_amount_buy as "Buy Pending Forward Delivery QTY",
                    -1 * forward_deliveries.expected_amount_sell as "Sell Pending Forward Delivery QTY",
                    forward_deliveries.last_updated_delivery_date as "Forward Delivery Dates",
                    P.registry_name AS "Registry",
                    pt.category AS "Project Category",
                    pt.type AS "Project Type",
                    pva.average_cost_basis AS "Average Cost Basis",
                    asset_prices_v5.unit_value "MTM",
                    sum(PPM.price) filter (WHERE SOURCE='viridios') "Viridios Price",
                    PV.risk_buffer_percentage AS "Risk Buffer Percentage",
                    buffer_categories.name as "Buffer Category"
                FROM RUBICON.PROJECT_VINTAGES PV
                JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
                LEFT JOIN rubicon.countries c ON c.alpha3 = p.country_code
                left JOIN RUBICON.project_types pt ON pt.id = p.project_type_id
                LEFT JOIN RUBICON.buffer_categories on COALESCE(p.buffer_category_id, pt.buffer_category_id) = buffer_categories.id
                LEFT JOIN market_data.project_price_metrics ppm ON P.REGISTRY_PROJECT_ID = PPM.PROJECT_ID
                            AND PV.LABEL = PPM.VINTAGE
                LEFT JOIN pending_trades USING (registry_project_id,label)
                LEFT JOIN pending_retirements USING (registry_project_id,label)
                LEFT JOIN current_allocations ON pv.id = current_allocations.project_vintage_id
                LEFT JOIN rubicon.asset_prices_v5 ON owner_id = pv.id
                LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
                LEFT JOIN rubicon.project_flags_v2 pf ON pf.id = p.id
                LEFT JOIN forward_deliveries on forward_deliveries.project_vintage_id = pv.id
                WHERE (PVA.holdings > 0 OR pending_trades.pending_buy > 0 OR forward_deliveries.expected_amount_buy > 0 or forward_deliveries.expected_amount_sell > 0)
                GROUP BY 1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        8,
                        9,
                        10,
                        11,
                        12,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        21,
                        22,
                        23,
                        24,
                        25,
                        27,
                        28
                ORDER BY PVA.holdings DESC
              """,
    )

    holdings, _ = await execute_sql_query_without_cache(holding_query)
    holdings = holdings.astype(
        {
            "Average Cost Basis": "float",
            "MTM": "float",
            "Viridios Price": "float",
            "Risk Buffer Percentage": "float",
            "Allocated to RCT (QTY)": "Int64",
            "Allocated to Custom (QTY)": "Int64",
            "Allocated to Reserves (QTY)": "Int64",
            "Pending Retirement": "Int64",
            "Buy Pending Forward Delivery QTY": "Int64",
        },
    )

    books_holding_query = text(
        """WITH pending_trades AS
                (SELECT registry_project_id ,
                        label ,
                        sum(amount) filter (WHERE action = 'buy') AS "pending_buy" ,
                        sum(amount) filter (WHERE action in ('sell', 'purchase')) AS "pending_sell"
                FROM
                    (SELECT t.trade_key,
                            p.registry_project_id,
                            p.name,
                            pv.label,
                            af.amount,
                            af.action,
                            af.status
                    FROM rubicon.asset_flows af
                    join rubicon.trades t on af.related_id = t.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    WHERE (t.status NOT IN ('settled','canceled'))
                    UNION
                    select
                        transactions.transaction_key,
                        p.registry_project_id,
                        p.name,
                        pv.label,
                        af.amount,
                        af.action,
                        af.status
                    from rubicon.transactions 
                    join rubicon.asset_flows af on af.related_id = transactions.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    where type = 'purchase' and transactions.status = 'processing' and af.asset_type = 'registry_vintage') AS virtual_table
                GROUP BY 1, 2),
                    pending_retirements AS
                (WITH average_cost AS
                    (SELECT asset_flows.related_id AS transaction_id,
                            asset_flows.asset_id AS project_vintage_id,
                            SUM(asset_flows.amount) AS amount
                    FROM rubicon.asset_flows
                    GROUP BY asset_flows.related_id,
                            asset_flows.asset_id)
                SELECT  projects.registry_project_id AS registry_project_id,
                        project_vintages.label AS label,
                        sum(average_cost.amount) AS pending_retirement
                FROM rubicon.asset_flows
                JOIN rubicon.project_vintages ON project_vintages.id = asset_flows.asset_id
                JOIN rubicon.projects ON projects.id = project_vintages.project_id
                JOIN average_cost ON average_cost.transaction_id = asset_flows.related_id
                    AND average_cost.project_vintage_id = asset_flows.asset_id
                WHERE asset_flows.action = 'retire'
                    AND asset_flows.status = 'pending'
                GROUP BY 1, 2),
                    current_allocations AS
                (SELECT component_id AS project_vintage_id,
                        sum(holdings) filter (WHERE TYPE IN ('portfolio:reserves', 'portfolio:public', 'portfolio:default', 'portfolio:custom', 'portfolio:customer')) AS allocated_rct,
                        sum(holdings) filter (WHERE TYPE IN ('opportunistic:default')) AS allocated_opportunistic,
                        sum(holdings) filter (WHERE TYPE IN ('rehabilitation:default')) AS allocated_rehab,
                        sum(holdings) filter (WHERE TYPE IN ('compliance:default')) AS allocated_compliance
                FROM rubicon.asset_composition_v2 ac
                JOIN rubicon.books b ON b.id = ac.parent_id
                WHERE ac.asset_type = 'registry_vintage'
                    AND ac.LEVEL = 1
                GROUP BY 1)
                SELECT P.registry_project_id AS "Registry Project ID",
                    P.name AS "Project",
                    PV.label AS "Vintage",
                    C.name AS "Country",
                    C.region AS "Region",
                    C.sub_region AS "Sub Region",
                    CASE
                        WHEN P.rct_standard THEN P.rct_standard
                        ELSE NULL
                    END AS "RCT Standard",
                    CASE
                        WHEN P.suspended THEN P.suspended
                        ELSE NULL
                    END AS "Suspended",
                    CASE
                        WHEN P.is_science_team_approved THEN P.is_science_team_approved
                        ELSE NULL
                    END AS "Website Ready",
                    CASE
                        WHEN P.rct_standard
                                AND p.is_science_team_approved
                                AND pv.risk_buffer_percentage < 1
                                AND NOT p.suspended THEN TRUE
                        ELSE NULL
                    END AS "RCT Portfolio Eligible",
                    PVA.holdings AS "Holdings",
                    current_allocations.allocated_rct AS "RCT Book (QTY)",
                    current_allocations.allocated_opportunistic AS "Opportunistic Book (QTY)",
                    current_allocations.allocated_rehab AS "Ineligible RCT (QTY)",
                    current_allocations.allocated_compliance AS "Compliance Book (QTY)",
                    pending_trades.pending_buy AS "Pending Buy",
                    -1 * pending_trades.pending_sell AS "Pending Sell",
                    -1 * pending_retirements.pending_retirement AS "Pending Retirement",
                    P.registry_name AS "Registry",
                    pt.category AS "Project Category",
                    pt.type AS "Project Type",
                    pva.average_cost_basis AS "Average Cost Basis",
                    asset_prices_v5.unit_value "MTM",
                    sum(PPM.price) filter (WHERE SOURCE='viridios') "Viridios Price",
                    PV.risk_buffer_percentage AS "Risk Buffer Percentage",
                    buffer_categories.name as "Buffer Category"
                FROM RUBICON.PROJECT_VINTAGES PV
                JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
                LEFT JOIN rubicon.countries c ON c.alpha3 = p.country_code
                JOIN RUBICON.project_types pt ON pt.id = p.project_type_id
                LEFT JOIN RUBICON.buffer_categories on COALESCE(p.buffer_category_id, pt.buffer_category_id) = buffer_categories.id
                LEFT JOIN market_data.project_price_metrics ppm ON P.REGISTRY_PROJECT_ID = PPM.PROJECT_ID
                            AND PV.LABEL = PPM.VINTAGE
                LEFT JOIN pending_trades USING (registry_project_id, label)
                LEFT JOIN pending_retirements USING (registry_project_id, label)
                LEFT JOIN current_allocations ON pv.id = current_allocations.project_vintage_id
                LEFT JOIN rubicon.asset_prices_v5 ON owner_id = pv.id
                LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
                LEFT JOIN rubicon.project_flags_v2 pf ON pf.id = p.id
                WHERE (PVA.holdings > 0 OR pending_trades.pending_buy > 0)
                GROUP BY 1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        8,
                        9,
                        10,
                        11,
                        12,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        21,
                        22,
                        23,
                        25,
                        26
                ORDER BY PVA.holdings DESC""",
    )

    books_holdings, _ = await execute_sql_query_without_cache(books_holding_query)
    books_holdings = books_holdings.astype(
        {
            "Average Cost Basis": "float",
            "MTM": "float",
            "Viridios Price": "float",
            "Risk Buffer Percentage": "float",
            "RCT Book (QTY)": "Int64",
            "Opportunistic Book (QTY)": "Int64",
            "Ineligible RCT (QTY)": "Int64",
            "Compliance Book (QTY)": "Int64",
        },
    )

    buffer = BytesIO()
    with pandas.ExcelWriter(buffer) as writer:

        async def adjust_columns(
            target_worksheet: str,
            int_cols: List[str],
            float_cols: List[str],
            perc_cols: List[str],
            start_cell: int,
            end_cell: int,
        ):
            worksheet = writer.sheets[target_worksheet]
            for column in int_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "#,##"
            for column in float_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "$#,#0.00##"
            for column in perc_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "0.00##\%"  # noqa: W605

        holdings.to_excel(writer, sheet_name="Holdings", index=False, float_format="%.2f")
        await adjust_columns(
            "Holdings",
            int_cols=["K", "L", "M", "N", "O", "P", "Q", "R", "S"],
            float_cols=["X", "Y", "Z"],
            perc_cols=["AA"],
            start_cell=2,
            end_cell=len(holdings),
        )
        books_holdings.to_excel(writer, sheet_name="Holdings By Book", index=False, float_format="%.2f")
        await adjust_columns(
            "Holdings By Book",
            int_cols=["K", "L", "M", "N", "O", "P", "Q", "R"],
            float_cols=["V", "W", "X"],
            perc_cols=["Y"],
            start_cell=2,
            end_cell=len(books_holdings),
        )
    buffer.seek(0)

    return buffer


async def portfolio_analytics() -> BytesIO:
    """Generate Portfolio Analytics file."""
    category_query = text(
        """
        SELECT
                PT.category as "Project Category",
                coalesce(sum(pva.holdings), 0) AS "Holdings",
                sum(pva.average_cost_basis * pva.bought) / nullif(sum(pva.bought), 0) "Average Cost Basis",
				sum(pva.average_cost_basis * pva.bought) as "Value (Cost Basis)",
                sum(pvp.price * pva.bought) / nullif(sum(pva.bought), 0) "MTM",
				sum(pvp.price * pva.bought) as "Current Value"
            FROM RUBICON.PROJECT_VINTAGES PV
            JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
            JOIN RUBICON.project_types pt on pt.id = p.project_type_id
            left join RUBICON.project_vintage_prices_v2 pvp on Pv.id = pvp.vintage_id
            LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
    where pva.holdings > 0
    group by 1
    order by 2 desc""",  # noqa: E501
    )
    category_breakdown, meta = await execute_sql_query_without_cache(category_query)
    category_breakdown["% of credits"] = category_breakdown["Holdings"].apply(
        lambda x: 100 * x / category_breakdown["Holdings"].sum(),
    )
    category_breakdown["% of value"] = category_breakdown["Current Value"].apply(
        lambda x: 100 * x / category_breakdown["Current Value"].sum(),
    )
    category_breakdown = category_breakdown.set_index("Project Category")
    category_breakdown.loc["Total", "Holdings"] = category_breakdown["Holdings"].sum()
    category_breakdown.loc["Total", "Value (Cost Basis)"] = category_breakdown["Value (Cost Basis)"].sum()
    category_breakdown.loc["Total", "Average Cost Basis"] = (
        category_breakdown.loc["Total", "Value (Cost Basis)"] / category_breakdown.loc["Total", "Holdings"]
    )
    category_breakdown.loc["Total", "Current Value"] = category_breakdown["Current Value"].sum()
    category_breakdown.loc["Total", "MTM"] = (
        category_breakdown.loc["Total", "Current Value"] / category_breakdown.loc["Total", "Holdings"]
    )
    category_breakdown.loc["Total", "% of credits"] = category_breakdown["% of credits"].sum()
    category_breakdown.loc["Total", "% of value"] = category_breakdown["% of value"].sum()

    category_breakdown = category_breakdown.astype(
        {
            "Holdings": "Int64",
            "Value (Cost Basis)": "float",
            "Average Cost Basis": "float",
            "MTM": "float",
            "Current Value": "float",
            "% of credits": "float",
            "% of value": "float",
        },
    )

    vintage_query = text(
        """
        SELECT
                PV.LABEL AS "Vintage",
                coalesce(sum(pva.holdings), 0) AS "Holdings"
            FROM RUBICON.PROJECT_VINTAGES PV
            LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
            where pva.holdings > 0
        group by 1
        order by 2 desc nulls last
        """,
    )

    vintage_breakdown, meta = await execute_sql_query_without_cache(vintage_query)
    vintage_breakdown["% "] = vintage_breakdown["Holdings"].apply(
        lambda x: 100 * x / vintage_breakdown["Holdings"].sum(),
    )
    vintage_breakdown = vintage_breakdown.set_index("Vintage")
    vintage_breakdown.loc["Total", "Holdings"] = vintage_breakdown["Holdings"].sum()

    vintage_breakdown = vintage_breakdown.astype(
        {
            "Holdings": "Int64",
            "% ": "float",
        },
    )

    country_query = text(
        """
        SELECT
                C.name as "Country",
                coalesce(sum(pva.holdings), 0) AS "Holdings"
        FROM RUBICON.PROJECT_VINTAGES PV
        JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
        LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
        left join rubicon.countries c on c.alpha3 = P.country_code
        where pva.holdings > 0
        group by 1
        order by 2 desc nulls last
    """,
    )
    country_breakdown, meta = await execute_sql_query_without_cache(country_query)

    country_breakdown["% "] = country_breakdown["Holdings"].apply(
        lambda x: 100 * x / country_breakdown["Holdings"].sum(),
    )
    country_breakdown = country_breakdown.set_index("Country")
    country_breakdown.loc["Total", "Holdings"] = country_breakdown["Holdings"].sum()

    country_breakdown = country_breakdown.astype(
        {
            "Holdings": "Int64",
            "% ": "float",
        },
    )

    # Registry Breakdown
    registry_query = text(
        """
        SELECT
                P.registry_name as "Registry",
                coalesce(sum(pva.holdings), 0) AS "Holdings"
                FROM RUBICON.PROJECT_VINTAGES PV
                JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
                LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
            group by 1
            order by 2 desc nulls last""",
    )
    registry_breakdown, meta = await execute_sql_query_without_cache(registry_query)
    registry_breakdown["% "] = registry_breakdown["Holdings"].apply(
        lambda x: 100 * x / registry_breakdown["Holdings"].sum(),
    )
    registry_breakdown = registry_breakdown.set_index("Registry")
    registry_breakdown.loc["Total", "Holdings"] = registry_breakdown["Holdings"].sum()

    registry_breakdown = registry_breakdown.astype(
        {
            "Holdings": "Int64",
            "% ": "float",
        },
    )

    ## Science View of Quality
    science_query = text(
        """
        SELECT
                PV.risk_buffer_percentage as "Science View of Quality",
                coalesce(sum(pva.holdings), 0) AS "Holdings"
                FROM RUBICON.PROJECT_VINTAGES PV
                LEFT JOIN RUBICON.project_vintage_amounts_v3 PVA ON PV.ID = PVA.asset_ID
        group by 1""",
    )
    science_quality, meta = await execute_sql_query_without_cache(science_query)
    science_quality["Science View of Quality"] = (
        science_quality["Science View of Quality"].fillna(numpy.nan).astype("float")
    )
    bins = [0, 0.1, 0.3, 0.6, float("inf")]
    bin_labels = ["Buffer <10", "Buffer 10-30", "Buffer 30-60", "Buffer >60"]
    science_quality = (
        science_quality.groupby(
            pandas.cut(
                science_quality["Science View of Quality"].fillna(numpy.nan),
                bins,
                labels=bin_labels,
                include_lowest=True,
                right=False,
            ),
            dropna=False,
        )["Holdings"]
        .sum()
        .reset_index()
    )
    science_quality["% "] = science_quality["Holdings"].apply(
        lambda x: 100 * x / science_quality["Holdings"].sum(),
    )
    science_quality = science_quality.set_index("Science View of Quality")
    science_quality.loc["Total", "Holdings"] = science_quality["Holdings"].sum()
    science_quality = science_quality.astype(
        {
            "Holdings": "Int64",
            "% ": "float",
        },
    )

    ## Top 10 Projects
    top_projects_query = text(
        """
        SELECT
                P.name as "Top 10 Projects",
                coalesce(sum(pvp.holdings), 0) AS "Holdings",
                pvp.average_cost_basis "Average Cost Basis",
                sum(pvp.price * pvp.holdings) / nullif(sum(pvp.holdings), 0) "MTM"
            FROM RUBICON.PROJECT_VINTAGES PV
            JOIN RUBICON.PROJECTS P ON P.ID = PV.PROJECT_ID
            LEFT JOIN RUBICON.project_vintage_prices_v2 PVP ON PV.ID = pvp.vintage_id
    group by 1,3
    order by 2 desc nulls last""",  # noqa: E501
    )
    top_projects, meta = await execute_sql_query_without_cache(top_projects_query)
    top_projects["% "] = top_projects["Holdings"].apply(lambda x: 100 * x / top_projects["Holdings"].sum())
    top_projects = top_projects.set_index("Top 10 Projects")
    top_projects = top_projects.head(10)
    top_projects.loc["Total", "Holdings"] = top_projects["Holdings"].sum()
    top_projects = top_projects.astype(
        {
            "Holdings": "Int64",
            "Average Cost Basis": "float",
            "MTM": "float",
            "% ": "float",
        },
    )

    pending_query = text(
        """WITH pending_trades AS
                (SELECT registry_project_id ,
                        label ,
                        sum(amount) filter (WHERE action = 'buy') AS "pending_buy" ,
                        sum(amount) filter (WHERE action in ('sell', 'purchase')) AS "pending_sell"
                FROM
                    (SELECT t.trade_key,
                            p.registry_project_id,
                            p.name,
                            pv.label,
                            af.amount,
                            af.action,
                            af.status
                    FROM rubicon.asset_flows af
                    join rubicon.trades t on af.related_id = t.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    WHERE (t.status NOT IN ('settled','canceled'))
                    UNION
                    select
                        transactions.transaction_key,
                        p.registry_project_id,
                        p.name,
                        pv.label,
                        af.amount,
                        af.action,
                        af.status
                    from rubicon.transactions 
                    join rubicon.asset_flows af on af.related_id = transactions.id
                    LEFT JOIN rubicon.project_vintages pv ON pv.id = af.asset_id
                    LEFT JOIN rubicon.projects p ON p.id = pv.project_id
                    where type = 'purchase' and transactions.status = 'processing' and af.asset_type = 'registry_vintage') AS virtual_table
                GROUP BY 1, 2),
            pending_retirements AS
                (WITH average_cost AS
                    (SELECT asset_flows.related_id AS transaction_id,
                            asset_flows.asset_id AS project_vintage_id,
                            SUM(asset_flows.amount) AS amount
                    FROM rubicon.asset_flows
                    GROUP BY asset_flows.related_id,
                            asset_flows.asset_id)
                SELECT  projects.registry_project_id AS registry_project_id,
                        project_vintages.label AS label,
                        sum(average_cost.amount) AS pending_retirement
                FROM rubicon.asset_flows
                JOIN rubicon.project_vintages ON project_vintages.id = asset_flows.asset_id
                JOIN rubicon.projects ON projects.id = project_vintages.project_id
                JOIN average_cost ON average_cost.transaction_id = asset_flows.related_id
                    AND average_cost.project_vintage_id = asset_flows.asset_id
                WHERE asset_flows.action = 'retire'
                    AND asset_flows.status NOT IN ('completed','canceled')
                GROUP BY 1, 2)
        SELECT sum(pending_buy) "Trade Buy"
                , sum(pending_sell) "Trade Sell"
                , sum(pending_retirement) "Retirement"
                FROM pending_trades
            full join pending_retirements using (registry_project_id, label)""",  # noqa: E501
    )
    pending_qty, meta = await execute_sql_query_without_cache(pending_query)
    pending_qty = pending_qty.transpose().reset_index()
    pending_qty = pending_qty.rename(columns={"index": "Pending QTY", 0: "Amount"})
    pending_qty = pending_qty.set_index("Pending QTY")

    pending_qty = pending_qty.astype(
        {"Amount": "Int64"},
    )

    buffer = BytesIO()
    with pandas.ExcelWriter(buffer) as writer:

        async def adjust_columns(
            target_worksheet: str,
            int_cols: [str],
            float_cols: [str],
            perc_cols: [str],
            start_cell: int,
            end_cell: int,
        ):
            worksheet = writer.sheets[target_worksheet]
            for column in int_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "#,##"
            for column in float_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "$#,#0.00##"
            for column in perc_cols:
                for cell_num in ["{}{}".format(column, x) for x in range(start_cell, end_cell + 2)]:
                    cell = worksheet[cell_num]
                    cell.number_format = "0.00##\%"  # noqa: W605

        startrow = 0
        category_breakdown.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns(
            "Portfolio",
            ["B"],
            ["C", "D", "E", "F"],
            ["G", "H"],
            startrow + 2,
            len(category_breakdown),
        )

        startrow += len(category_breakdown) + 2
        vintage_breakdown.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], [], ["C"], startrow, len(vintage_breakdown) + startrow)

        startrow += len(vintage_breakdown) + 2
        country_breakdown.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], [], ["C"], startrow, len(country_breakdown) + startrow)

        startrow += len(country_breakdown) + 2
        registry_breakdown.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], [], ["C"], startrow, len(registry_breakdown) + startrow)

        startrow += len(registry_breakdown) + 2
        science_quality.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], [], ["C"], startrow, len(science_quality) + startrow)

        startrow += len(science_quality) + 2
        top_projects.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], ["C", "D"], ["E"], startrow, len(top_projects) + startrow)

        startrow += len(top_projects) + 2
        # basket_comp.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        # await adjust_columns("Portfolio", ["B"], ["C", "D"], [], startrow, len(basket_comp) + startrow)

        # startrow += len(basket_comp) + 2
        # buffer_comp.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        # await adjust_columns("Portfolio", ["B"], ["C", "D"], [], startrow, len(buffer_comp) + startrow)

        # startrow += len(buffer_comp) + 2
        pending_qty.to_excel(writer, sheet_name="Portfolio", startrow=startrow, startcol=0, float_format="%.2f")
        await adjust_columns("Portfolio", ["B"], [], [], startrow, len(pending_qty) + startrow)
    buffer.seek(0)

    return buffer


async def market_report() -> BytesIO:
    """Generate Market Data file."""
    market_query = text(
        """ with project_vintage as (SELECT label
            , pv.id
            , name
            , registry_project_id
            FROM RUBICON.PROJECTS PROJECTS
            JOIN RUBICON.PROJECT_VINTAGES PV
                ON PV.PROJECT_ID = PROJECTS.ID)
	, filtered_prices as (
		select distinct on (ep.source, ep.project_id, ep.vintage, ep.date) EP.price as "price",
                EP.date as "date",
                EP.source as "source",
				ep.project_id,
				ep.vintage
		from market_data.aggregated_prices EP
		where project_id = ANY ((SELECT ARRAY_AGG(DISTINCT project_vintage.registry_project_id) FROM project_vintage)::text[])
		order by ep.source, ep.project_id, ep.vintage, ep.date desc)
            SELECT
                PROJECT_VINTAGE.NAME as "project_name",
                PROJECT_VINTAGE.REGISTRY_PROJECT_ID,
                PROJECT_VINTAGE.LABEL as "vintage_label",
                EP.price as "price",
                EP.date as "price_date",
                case when EP.source = 'index_calculated' then 'MTM' when ep.source = 'rubicon_trader' then 'trader_override' else ep.source end
                    as "source"
                FROM PROJECT_VINTAGE
				left JOIN filtered_prices EP ON PROJECT_VINTAGE.REGISTRY_PROJECT_ID = EP.PROJECT_ID AND PROJECT_VINTAGE.LABEL = EP.VINTAGE""",  # noqa: E501
    )

    market_prices, meta = await execute_sql_query_without_cache(market_query)
    market_prices["price_date"] = pandas.to_datetime(market_prices["price_date"]).dt.date

    buffer = BytesIO()
    with pandas.ExcelWriter(buffer) as writer:
        market_prices.to_excel(writer, sheet_name="Market Prices", index=False, float_format="%.2f")
    buffer.seek(0)

    return buffer
