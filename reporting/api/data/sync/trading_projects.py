"""Sync for Trading Projects."""

import contextlib
import json
from typing import Any, Dict

import numpy
import pandas
from pandas import DataFrame

from ...settings import settings
from .. import execute_sql_query_without_cache, json_serial
from . import GoogleSheetSyncAction


class TradingProjectsSync(GoogleSheetSyncAction):
    """Sync for Trading Projects."""

    HEADER = [
        "registry_project_id",
        "project_name",
        "project_type",
        "emissions_impact_type",
        "project_country",
        "bezero_rating",
        "bezero_updated_date",
        "integrity_grade_score",
        "integrity_grade_score_risk_adjusted",
        "registry_name",
        "is_science_team_approved",
        "holdings",
        "min_percentage",
        "max_percentage",
        "min_quantity",
        "max_quantity",
        "is_byorct_approved",
    ]

    async def get_google_sheet_data(self) -> DataFrame:
        """Return the data from the Google Sheet."""
        # Projects
        projects = self._open_google_spreadsheet()["Projects"]
        self.check_header(projects)

        # from google sheets
        dd = projects.get_all_values()
        dd = [[d.strip().replace("\u2019", "'") for d in row] for row in dd]
        data = pandas.DataFrame([d + [""] * (len(dd[0]) - len(d)) for d in dd[1:]], columns=dd[0])
        data = data[data["registry_project_id"] != ""].astype(str).set_index("registry_project_id")
        data = data.replace("#REF!", "")

        data[["is_science_team_approved", "is_byorct_approved"]] = (
            data[["is_science_team_approved", "is_byorct_approved"]]
            .replace(
                {"TRUE": True, "FALSE": False},
            )
            .fillna(False)
            .astype(bool)
        )
        data["integrity_grade_score"] = [
            float(x.replace(",", "")) if x else numpy.NaN for x in data["integrity_grade_score"]
        ]
        data["integrity_grade_score_risk_adjusted"] = [
            float(x.replace(",", "")) if x else numpy.NaN for x in data["integrity_grade_score_risk_adjusted"]
        ]
        data["min_percentage"] = [float(x.replace("%", "")) / 100 if x else numpy.NaN for x in data["min_percentage"]]
        data["max_percentage"] = [float(x.replace("%", "")) / 100 if x else numpy.NaN for x in data["max_percentage"]]
        data["min_quantity"] = [float(x.replace(",", "")) if x else numpy.NaN for x in data["min_quantity"]]
        data["max_quantity"] = [float(x.replace(",", "")) if x else numpy.NaN for x in data["max_quantity"]]
        data["holdings"] = [float(x.replace(",", "")) if x else numpy.NaN for x in data["holdings"]]
        data["integrity_grade_score"] = data["integrity_grade_score"].astype(float)
        data["integrity_grade_score_risk_adjusted"] = data["integrity_grade_score_risk_adjusted"].astype(float)
        data["min_percentage"] = data["min_percentage"].astype(float)
        data["max_percentage"] = data["max_percentage"].astype(float)
        data["min_quantity"] = data["min_quantity"].astype(float)
        data["max_quantity"] = data["max_quantity"].astype(float)
        data["holdings"] = data["holdings"].astype(float)
        data["bezero_rating"] = data["bezero_rating"].astype(str).replace("", numpy.NaN)
        data["bezero_updated_date"] = pandas.to_datetime(data["bezero_updated_date"], utc=True, errors="coerce").dt.date

        data["id"] = ""

        return data

    async def get_platform_data(self) -> DataFrame:
        """Return the data from the Platform."""
        query = """
        WITH allocations as (select
            p.id as project_id,
            sum(ac.holdings) as holdings
        from rubicon.asset_composition_v2 ac
        join rubicon.project_vintages pv on pv.id = ac.component_id
        join rubicon.projects p on p.id = pv.project_Id
        where ac.asset_type = 'registry_vintage' and ac.level = 1
        group by 1)
                SELECT
                    projects.registry_project_id as registry_project_id,
                    projects.name as project_name,
                    project_types.type as project_type,
                    projects.emissions_impact_type,
                    projects.country_code as project_country,
                    projects.bezero_rating,
                    projects.bezero_updated_date,
                    projects.integrity_grade_score,
                    projects.integrity_grade_score_risk_adjusted,
                    projects.registry_name as registry_name,
                    projects.is_science_team_approved,
                    allocations.holdings as holdings,
                    projects.min_percentage,
                    projects.max_percentage,
                    projects.min_quantity,
                    projects.max_quantity,
                    projects.is_byorct_approved,
                    projects.id
                FROM rubicon.projects
                LEFT JOIN rubicon.project_types ON project_type_id = project_types.id
                LEFT JOIN allocations ON projects.id = allocations.project_id
                left join rubicon.project_flags_v2 pf on pf.id = projects.id
                where pf.has_trades and projects.registry_project_id not in ('VCS 4289')
                ORDER BY projects.registry_project_id
        """  # noqa: E501

        df_db, _ = await execute_sql_query_without_cache(query)

        df_db["is_byorct_approved"] = df_db["is_byorct_approved"].astype(bool)
        df_db["integrity_grade_score"] = df_db["integrity_grade_score"].astype(float)
        df_db["integrity_grade_score_risk_adjusted"] = df_db["integrity_grade_score_risk_adjusted"].astype(float)
        df_db["min_percentage"] = df_db["min_percentage"].astype(float)
        df_db["max_percentage"] = df_db["max_percentage"].astype(float)
        df_db["min_quantity"] = df_db["min_quantity"].astype(float)
        df_db["max_quantity"] = df_db["max_quantity"].astype(float)
        df_db["holdings"] = df_db["holdings"].astype(float)
        df_db["bezero_updated_date"] = pandas.to_datetime(
            df_db["bezero_updated_date"],
            utc=True,
            errors="coerce",
        ).dt.date

        return df_db

    async def check_differences(self, sheet_data: DataFrame, db_data: DataFrame) -> Dict[str, Any]:
        """Check the differences between the two sources."""
        assert (sheet_data.columns == db_data.columns).all()
        assert (sheet_data.dtypes == db_data.dtypes).all()

        projects_diff = {
            "db": sorted(set(db_data.index.fillna("")).difference(sheet_data.index.fillna(""))),
            "new": sorted(set(sheet_data.index.fillna("")).difference(db_data.index.fillna(""))),
        }

        # focus only on the same subset
        sheet_data = sheet_data[sheet_data.index.isin(db_data.index)]
        db_data = db_data[db_data.index.isin(sheet_data.index)].reindex(index=sheet_data.index)

        diffs = (sheet_data != db_data).sum(axis=1)
        diffs = diffs[diffs > 0]

        diff_report = {}

        for pid, _ in diffs.items():
            cc = {}
            for col, v in (sheet_data != db_data).loc[pid].items():
                if not v or col in (
                    "registry_project_id",
                    "project_name",
                    "project_type",
                    "emissions_impact_type",
                    "project_country",
                    "integrity_grade_score",
                    "integrity_grade_score_risk_adjusted",
                    "registry_name",
                    "is_science_team_approved",
                    "holdings",
                    "id",
                ):
                    continue
                old = db_data.loc[pid, col]
                new = sheet_data.loc[pid, col]
                if pandas.isna(old) and pandas.isna(new):
                    continue
                cc[col] = {"db": old, "new": new}
                if col in ("is_science_team_approved", "is_byorct_approved"):
                    cc[col] = {"db": old, "new": new or False}
                    if cc[col]["db"] == cc[col]["new"]:
                        del cc[col]
            if cc:
                diff_report[str(db_data["id"].loc[pid])] = {pid: cc}

        return diff_report, projects_diff

    async def get_db_supplemental(self):  # noqa: D103
        """Get project types."""
        query = """
            select id, type
            from rubicon.project_types
        """
        df_db, _ = await execute_sql_query_without_cache(query)
        return df_db

    async def validate_google_sheet_data(self, df: DataFrame):
        """Validate the data in the Google spreadsheet."""
        errors = {}

        failed_types = df[df["project_type_id"].isna()].index.tolist()
        if failed_types:
            errors["invalid_category_types"] = failed_types

        failed_percentages = (df[df["min_percentage"] < 0]).index.tolist()
        if failed_percentages:
            errors["min_percentage"] = failed_percentages

        failed_percentages = (df[df["max_percentage"] > 1]).index.tolist()
        if failed_percentages:
            errors["max_percentage"] = failed_percentages

        failed_percentages = (df[df["min_percentage"] > df["max_percentage"]]).index.tolist()
        if failed_percentages:
            errors["invalid_percentages_range"] = failed_percentages

        failed_quantities = (df[df["min_quantity"] < 0]).index.tolist()
        if failed_quantities:
            errors["min_percentage"] = failed_quantities

        failed_quantities = (df[df["min_quantity"] > df["max_quantity"]]).index.tolist()
        if failed_quantities:
            errors["invalid_quantities_range"] = failed_quantities

        return errors

    async def compare_values(self) -> Dict[str, Any]:
        """Compare the differences between the Google Sheet data and the Platform."""
        gsheet_data = await self.get_google_sheet_data()
        db_data = (await self.get_platform_data()).set_index("registry_project_id")
        sup_types = await self.get_db_supplemental()
        gsheet_data["project_type_id"] = gsheet_data["project_type"].map(sup_types.set_index("type")["id"])
        db_data["project_type_id"] = db_data["project_type"].map(sup_types.set_index("type")["id"])

        if errors := await self.validate_google_sheet_data(gsheet_data.copy()):
            return {"errors": errors}

        diff_vals, new_projects = await self.check_differences(gsheet_data, db_data)

        result = []
        for pid, v in diff_vals.items():
            for rid, x in v.items():
                r = {"project_id": pid, "registry_project_id": rid, "diffs": x}
                result.append(r)

        result = {"errors": None, "differences": result, "new": new_projects}

        serialized = json.dumps(result, default=json_serial)

        return json.loads(serialized)

    async def update_values(self) -> None:
        """Update the Google Sheet with the data from the Platform."""
        # Projects
        projects = self._open_google_spreadsheet()["Projects"]
        self.check_header(projects)

        # DB
        df_db = await self.get_platform_data()

        # Convert date objects to strings for JSON serialization
        df_db_copy = df_db.copy()
        if "bezero_updated_date" in df_db_copy.columns:
            df_db_copy["bezero_updated_date"] = df_db_copy["bezero_updated_date"].astype(str)

        # save
        projects.clear(start="A2")
        projects.update_values("A2", df_db_copy.drop(columns=["id"]).to_numpy(na_value="").tolist())
        projects.update_values("A2", df_db_copy[["registry_project_id"]].to_numpy(na_value="").tolist(), parse=False)
        with contextlib.suppress(Exception):
            projects.delete_rows(len(df_db) + 2, 1000)


trading_projects_sync = TradingProjectsSync(settings.gsheets.id.projects_trading)
