"""Market Intelligence classes."""

from sqlalchemy import Column, Date, Integer
from sqlalchemy.dialects.postgresql import JSONB, TIMESTAMP

from ..db import Base


class Report(
    Base,
):
    """A report is just an arbitrary list of sources with a date attached."""

    __tablename__ = "report"
    __table_args__ = {"schema": "market_intelligence"}

    id = Column(Integer, primary_key=True)  # noqa: A003
    date = Column(Date)
    doc = Column(JSONB)
    updated_at = Column(TIMESTAMP)
