"""JSON Logging."""

import json
import logging
import socket
import sys
import time
import traceback
import typing
from http import HTTPStatus
from logging import Formatter, LogRecord
from typing import Any, Dict, cast

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response
from starlette.types import <PERSON><PERSON><PERSON>pp
from starlette_context import context

from ..settings import settings

logger = logging.getLogger(__name__)
hostname = socket.gethostname()


DispatchFunction = typing.Callable[[Request, RequestResponseEndpoint], typing.Awaitable[Response]]


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for JSON logging."""

    def __init__(self, app: ASGIApp, dispatch: DispatchFunction | None = None) -> None:  # noqa: D107
        log = logging.getLogger("uvicorn")
        log.info("Starting Logging Middleware")
        super().__init__(app, dispatch=dispatch)

    @staticmethod
    async def _dump_request(request: Request, body: bool = False) -> str:
        headers = dict(request.headers.items())
        dump = f"{request.method} {request.url.path} HTTP/{request.scope.get('http_version')}\r\n"
        dump += f"Host: {headers.pop('host', '')}\r\n"
        for k, v in headers.items():
            dump += f"{k.title()}: {v}\r\n"

        if body:
            dump += (await request.body()).decode()
            dump += "\r\n"

        return dump

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:  # noqa: D102
        start_time = time.time()
        path = request.url.path

        request.state.errors = []
        res = await call_next(request)

        if request.url.path in ("/api/reporting/healthz", "/api/reporting/metrics", "/healthz", "/metrics"):
            return res

        info: Dict[str, Any] = {
            "request": {
                "method": request.method,
                "url": request.url.path,
                "size": sys.getsizeof(request),
                "remote_ip": request.headers.get(
                    "x-forwarded-for",
                    request.client.host if request.client else "0.0.0.0",
                ).split(",")[0],
                "protocol": request.url.scheme,
            },
            "response": {"status_code": res.status_code, "headers": dict(res.headers.items())},
            "trace_id": context.get("trace_id"),
            "hostname": hostname,
            "service_name": settings.app.name,
            "environment": settings.app.environment,
        }

        user = context.get("user_id")
        if user:
            info["user"] = user

        if "referrer" in request.headers:
            info["request"]["referrer"] = request.headers.get("referrer")

        if "user-agent" in request.headers:
            info["request"]["user_agent"] = request.headers.get("user-agent")

        start_time = time.time_ns() / 1e6

        errors = request.state.errors
        if len(errors) > 0:
            errors.append(await self._dump_request(request))

            info["errors"] = [str(error) for error in errors]

            logger.error(path, extra=info)
        else:
            end_time = time.time_ns() / 1e6
            process_time = end_time - start_time
            info["start_time"] = start_time
            info["end_time"] = end_time
            info["process_time"] = process_time
            info["time"] = start_time

            logger.info(path, extra=info)

        return res


class RecoveryMiddleware(BaseHTTPMiddleware):
    """Recovery Middleware."""

    def __init__(self, app: ASGIApp, dispatch: DispatchFunction | None = None) -> None:  # noqa: D107
        log = logging.getLogger("uvicorn")
        log.info("Starting Recovery Middleware")
        super().__init__(app, dispatch=dispatch)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:  # noqa: D102
        try:
            return await call_next(request)
        except:  # noqa: E722
            tb = traceback.format_exc()
            request.state.errors.append(tb)
            if settings.app.get_bool("debug"):
                print(tb)  # noqa: T201
                return Response(content=tb, status_code=HTTPStatus.INTERNAL_SERVER_ERROR)
            return Response(status_code=HTTPStatus.INTERNAL_SERVER_ERROR)


class JsonFormatter(Formatter):
    """JSON Formatter class."""

    # https://docs.python.org/3/library/logging.html#logrecord-attributes
    _DEFAULT_KEYS = (
        "args",
        "asctime",
        "created",
        "exc_info",
        "exc_text",
        "filename",
        "funcName",
        "levelname",
        "levelno",
        "lineno",
        "module",
        "msecs",
        "message",
        "msg",
        "name",
        "pathname",
        "process",
        "processName",
        "relativeCreated",
        "stack_info",
        "thread",
        "threadName",
    )

    _LEVEL_TO_UPPER_NAME = {
        logging.CRITICAL: "FATAL",
        logging.ERROR: "ERROR",
        logging.WARNING: "WARN",
        logging.INFO: "INFO",
        logging.DEBUG: "DEBUG",
    }

    _LEVEL_TO_LOWER_NAME = {
        logging.CRITICAL: "fatal",
        logging.ERROR: "error",
        logging.WARNING: "warn",
        logging.INFO: "info",
        logging.DEBUG: "debug",
    }

    def format(self, record: LogRecord) -> str:  # noqa: A003, D102
        msg_dict = {
            "level": record.levelno,
            "time": record.created * 1000,
            "caller": "/".join(record.pathname.split("/")[-2:]) + f":{record.lineno}",
            "msg": record.msg,
        }

        # extra
        for k, v in record.__dict__.items():
            if k not in self._DEFAULT_KEYS and not k.startswith("_"):
                msg_dict[k] = v

        return json.dumps(msg_dict)


class IgnoreWeasyPrintWarnings(logging.Filter):  # noqa: D101
    def filter(self, record):  # noqa: D102
        return "fontTools" not in record.name


# logging in JSON format
logging.logThreads = False
logging.logMultiprocessing = False
handler = logging.StreamHandler()
handler.setFormatter(JsonFormatter())
handler.addFilter(IgnoreWeasyPrintWarnings())
logging.basicConfig(handlers=[handler], level=cast(str, settings.app.get("log.level", "INFO")).upper())

# disable uvicorn logging
uvicorn_error = logging.getLogger("uvicorn.error")
uvicorn_error.disabled = True
uvicorn_access = logging.getLogger("uvicorn.access")
uvicorn_access.disabled = True
