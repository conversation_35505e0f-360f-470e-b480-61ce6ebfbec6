"""Logic to generate a PDF for quotes."""

import logging
import os
from decimal import Decimal
from typing import Any, Dict, List

from ..settings import settings

PORTAL_URL = settings.app.urls.portal

font_matter_sq = "file://" + os.path.join(os.path.dirname(__file__), "fonts", "MatterSQ-Light.otf")
font_matter_sq_bold = "file://" + os.path.join(os.path.dirname(__file__), "fonts", "MatterSQ-Bold.otf")

rubicon_logo = "file://" + os.path.join(os.path.dirname(__file__), "images", "rubicon-logo.svg")
risk_adjusted = "file://" + os.path.join(os.path.dirname(__file__), "images", "risk-adjusted.svg")
map_pin = "file://" + os.path.join(os.path.dirname(__file__), "images", "map-pin.svg")


def header(name: str) -> str:
    """Header."""
    return f"""
            <div class="header">
                <img src="{rubicon_logo}">
                <span class="date">
                    <b>Trade Confirmation</b>
                </span>
            </div>
    """


def footer(i: int, total: int) -> str:
    """Footer."""
    return f"""
            <div class="footer">
                <img src="{rubicon_logo}">
                <span class="number">{i} / {total}</span>
            </div>
    """


def format_currency(d: Decimal) -> str:
    """Format currency as USD."""
    return "${:,.2f}".format(d) if d != 0 else "-"


def format_int(i: int) -> str:
    """Format number."""
    return "{:,.0f}".format(round(i)) if i != 0 else "-"


class TableRow:
    def render(self):
        raise NotImplementedError()


class TableTitle(TableRow):
    def __init__(self, contents):
        self.contents = contents

    def render(self):
        return f"""
            <div class="table-title">
                {self.contents}
            </div>
        """


class TableHeader(TableRow):
    def render(self):
        return """
            <div class="component heading">
                <div class="name">
                    <div>Product</div>
                </div>
                <div class="quantity">
                    <div>Quantity</div>
                </div>
                <div class="quantity">
                    <div>Unit Price</div>
                </div>
                <div class="quantity" style="display:flex; justify-content: flex-end;">
                    <div>Total Price</div>
                </div>

            </div>
        """


def table(components: List[TableRow]) -> str:
    """Create the projects table."""
    return f"""
        <div class="table">
            {"".join(component.render() for component in components)}
        </div>
    """


class TableContents(TableRow):
    def __init__(self, contents):
        self.contents = contents

    def render(self):
        """Table line."""
        component = self.contents

        name = (
            f"""<div class="project-name"><a href="{PORTAL_URL}/projects/{component["project_id"]}">{component["project_name"]}</a></div>"""
            if component["registry_project_id"]
            else f"""<div class="project-name">{component["project_name"]}</div>"""
        )
        vintage_style = (
            f"""
            <div class="name">
                {name}
                <div class="project-id">{component["registry_project_id"]} - {component["vintage_label"]}</div>
            </div>"""
            if component["registry_project_id"]
            else f"""
            <div class="name">
                {name}
            </div>"""
        )

        return f"""
                    <div class="component">
                        {vintage_style}
                        <div class="quantity" style="display:flex; align-items: center;">
                            <div class="info">{format_int(component["amount"])}</div>
                        </div>
                        <div class="quantity" style="display:flex; align-items: center;">
                            <div class="info">{format_currency(component["per_tonne"])}</div>
                        </div>
                        <div class="quantity" style="display:flex; justify-content: flex-end; align-items: center;">
                            <div>{format_currency(component["purchase_price"])}</div>
                        </div>
                    </div>
                """


class TableTotal(TableRow):
    def __init__(self, total_quantity: int, total_price: Decimal):
        self.total_quantity = total_quantity
        self.total_price = total_price

    def render(self):
        return f"""
            <div class="component total-row">
                <div class="name">
                    <div><strong>Total</strong></div>
                </div>
                <div class="quantity" style="display:flex; align-items: center;">
                    <div class="info"><strong>{format_int(self.total_quantity)}</strong></div>
                </div>
                <div class="quantity" style="display:flex; align-items: center;">
                    <div class="info">-</div>
                </div>
                <div class="quantity" style="display:flex; justify-content: flex-end; align-items: center;">
                    <div><strong>{format_currency(self.total_price)}</strong></div>
                </div>
            </div>
        """


def generate_pdf(data: Dict[str, Any], path: str) -> None:
    """Create a PDF with a project summary."""
    from weasyprint import CSS, HTML
    from weasyprint.logger import LOGGER
    from weasyprint.text.fonts import FontConfiguration

    for handler in LOGGER.handlers:
        LOGGER.removeHandler(handler)
    LOGGER.setLevel(level=logging.ERROR)
    LOGGER.disabled = True

    font_config = FontConfiguration()

    css = CSS(
        string=f"""
        @font-face {{font-family: "Matter SQ"; src: url({font_matter_sq}); font-weight: 100-400;}}
        @font-face {{font-family: "Matter SQ"; src: url({font_matter_sq_bold}); font-weight: bold;}}

        @page  {{
            margin: 0;
        }}

        .header {{
            margin-top: 1.1cm;
            margin-left: 1.1cm;
            margin-right: 1.1cm;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }}

        .header img {{
            height: 75px;
        }}

        .header .date {{
            display: inline-block;
            text-align: right;
        }}

        body {{
            font-family: "Matter SQ";
        }}

        .content {{
            margin: 1cm;
            margin-top: .5cm;
        }}

        .price {{
            border-radius: var(--borderRadius, 5px);
            background: rgba(235, 245, 244, 0.50);
            padding: 14px;
        }}

        h1 {{
            color: rgba(0, 0, 0, 0.87);
            font-family: "Matter SQ";
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }}

        .footer {{
            background: var(--rc-green-grey, #E0EDE8);
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            height: 60px;

            text-align: center;
        }}

        .footer img {{
            height: 30px;
            position: absolute;
            top: 30%;
            left: 1cm;
        }}

        .footer .number {{
            color: var(--common-black-main, #000);
            font-family: "Matter SQ";
            font-size: 10px;
            font-style: normal;
            font-weight: 300;
            line-height: normal;

            position: absolute;
            right: 1cm;
            top: 45%;
        }}

        .price .summary-item .info-header {{
            color: rgba(0, 0, 0, 0.6);
            font-family: "Matter SQ";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            margin-bottom: 5px;
        }}

        .price .summary-item .value {{
            color: rgba(0, 0, 0, 0.87);
            font-family: "Matter SQ";
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
        }}

        .price .summary-row {{
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }}

        .risk-adjustment {{
            margin-top: 20px;
            display: flex;
            align-items: center;
        }}

        .disclaimer {{
            margin-top: 20px;
            margin-bottom: 10px;
            color: rgba(0, 0, 0, 0.6);
            font-family: "Matter SQ";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
        }}

        .spacing {{
            margin-top: 30px;
        }}

        .table {{
            width: 100%;
        }}

        .component {{
            display: flex;
            justify-content: space-between;
            width: 100%;

            color: rgba(0, 0, 0, 0.87);
            font-family: "Matter SQ";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;

            margin: 10px 0;
            padding-bottom: 10px;

            border-bottom: 1px #eeeeee solid;
        }}

        .component.heading {{
            color: rgba(0, 0, 0, 0.87);
            font-family: "Matter SQ";
            font-size: 15px;
            font-style: normal;
            font-weight: 900;
            padding-bottom: 20px;
        }}

        .component .name {{
            width: 35%;
            position: relative;
        }}

        .component .type{{
            width: 23%;
        }}
        .component .risk{{
            width: 11%;
        }}
        .component .country{{
            width: 14%;
        }}
        .component .quantity{{
            width: 10%;
        }}

        .table-title {{
            color: rgba(0, 0, 0, 0.87);
            font-family: "Matter SQ";
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            margin: 30px 0 20px 0;
        }}

        .project-name {{
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }}

        .project-name a {{
            color: rgba(100, 164, 142, 1);
        }}

        .project-id {{
            color: rgba(158, 158, 158, 1);
        }}

        .type {{display: flex;}}

        .type .info {{
            max-height: 2em;
            line-height: 1em;
            display: flex;
            overflow: hidden;
            text-overflow: ellipsis;
        }}

        .chip {{
            color: var(--common-black-main, #000);
            font-feature-settings: 'clig' off, 'liga' off;
            font-family: "Matter SQ";
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px; /* 180% */
            letter-spacing: 0.16px;
            padding: 6px;
        }}

        .location {{
            border-radius: 100px;
            background: #F5D54A;
            text-overflow: ellipsis;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
        }}

        .location img {{
            height: 10px;
            margin-right: 3px;
        }}

        .risk {{
            position: relative;
            width: 100px;
            height: 2em;
        }}

        .wheel {{
            height: 2em;
            width: 2em;

            position: absolute;
            left: 20px;
            top: 0;
        }}


        .score {{
            position: absolute;
            left: 30px;
            top: 7px;
            font-size: 13px;
        }}

        .page-break-before {{
            page-break-before: always;
        }}

        .component.total-row {{
            border-top: 2px solid #000;
            border-bottom: 2px solid #000;
            font-weight: bold;
            margin-top: 10px;
            padding-top: 10px;
        }}
        """,
        font_config=font_config,
    )

    components = data["components"]

    # Calculate totals
    total_quantity = sum(component["amount"] for component in components)
    total_price = sum(component["purchase_price"] for component in components)

    components = [TableContents(x) for x in components]

    components = [TableHeader()] + components + [TableTotal(total_quantity, total_price)]

    pages = []
    target_n = 13
    while components:
        if len(components) < target_n:
            pages.append(components)
            components = []
        elif isinstance(components[target_n - 2], TableTitle):
            pages.append(components[: target_n - 2])
            components = components[target_n - 2 :]
        elif isinstance(components[target_n - 1], TableTitle):
            pages.append(components[: target_n - 1])
            components = components[target_n - 1 :]
        else:
            pages.append(components[:target_n])
            components = components[target_n:]

        target_n = 17

    first_page = pages[0]

    risk_section = f"""
                <div class="risk-adjustment">
                    <img src="{risk_adjusted}">
                    <span style="margin-left: 10px;">
                        Risk adjustment is <b>{"included" if data["risk_adjusted"] else "not included"}</b>.
                    </span>
                </div>
"""

    html = HTML(
        string=f"""
            {header(data["name"])}

            <div class="content">
            <div class="price">
                <div class="summary-row">
                    <span class="summary-item">
                        <div class="info-header">Date:</div>
                        <div class="value">{data["created_timestamp"].strftime("%m/%d/%Y %I:%M %p %Z")}</div>
                    </span>
                </div>
                <div class="summary-row">
                    <span class="summary-item">
                        <div class="info-header">Rubicon Transaction ID:</div>
                        <div class="value">{(data["ui_key"])}</div>
                    </span>
                </div>
                <div class="summary-row">
                    <span class="summary-item">
                        <div class="info-header">Buyer:</div>
                        <div class="value">{(data["name"])}</div>
                    </span>
                    <span class="summary-item">
                        <div class="info-header">Seller:</div>
                        <div class="value">Rubicon Carbon Services, LLC</div>
                    </span>
                </div>

                {risk_section if data["risk_adjusted"] else ""}

            </div>

            <div class="spacing" />

            {table(first_page)}
            </div>

            {footer(1, len(pages))}
        </div>
    """
        + "".join(
            f"""
                <div class="page-break-before" />
                {header(data["name"])}
                <div class="content" style="margin-top: 1.25cm">
                {table(comps)}
                </div>
                {footer(i + 2, len(pages))}
        """
            for i, comps in enumerate(pages[1:])
        ),
    )

    html.write_pdf(path, stylesheets=[css], font_config=font_config)
