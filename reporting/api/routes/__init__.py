"""Routes."""

import json
import logging
from typing import Any

import pandas
from fastapi import FastAPI, Response
from pandas import DataFrame, Series

from ..data import Metadata

logger = logging.getLogger("uvicorn")


class Encoder(json.JSONEncoder):
    """JSON encoder class with pandas support."""

    def default(self, obj: Any) -> Any:  # noqa: D102
        if isinstance(obj, pandas.DataFrame):
            # this is inefficient but it's the easiest way to get the dates to format properly
            return json.loads(obj.to_json(orient="records", default_handler=str) or "")
        return super().default(obj)


def with_metadata_response(*data: DataFrame | Series, metadata: Metadata) -> Response:
    """Response with metadata added to the headers."""
    return Response(
        content=json.dumps(data if len(data) > 1 else data[0], cls=Encoder),  # type: ignore
        headers={
            "X-API-DATA-TIMESTAMP-AS-OF": str(metadata["as_of"]),
            "X-API-DATA-TIMESTAMP": str(metadata["now"]),
        },
        media_type="application/json",
    )


def routes(app: FastAPI) -> None:
    """Routes."""
    from ..settings import settings
    from . import (
        chatbot,
        core,
        db_updates,
        inventory,
        market_data,
        market_intelligence,
        project_data,
        recon,
        report_downloads,
        superset,
    )

    core.routes(app)
    inventory.routes(app)
    recon.routes(app)
    market_intelligence.routes(app)
    market_data.routes(app)
    report_downloads.routes(app)
    db_updates.routes(app)
    project_data.routes(app)
    superset.routes(app)
    chatbot.routes(app)

    if settings.get("app.debug"):
        from . import dev

        dev.routes(app)

    logger.info("Loaded Routes")
