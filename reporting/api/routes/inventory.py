"""Inventory routes."""

from enum import Enum
from typing import List, Optional
from uuid import UUID

from fastapi import Depends, FastAPI, Response

from ..auth import LoggedIn, UserInfo
from ..data.inventory import positions, historical_book_prices
from . import with_metadata_response


def routes(app: FastAPI) -> None:
    """Routes."""

    @app.post("/positions-report")
    @app.get("/positions-report")
    async def route_positions_report(
        vintage_ids: Optional[List[UUID]] = None,
        registry_ids: Optional[List[str]] = None,
        _: UserInfo = Depends(LoggedIn()),
    ) -> Response:
        df, metadata = await positions(vintage_ids, registry_ids)
        return with_metadata_response(df, metadata=metadata)

    class AvailableTimePeriods(str, Enum):
        """Available Time Periods."""

        day_1 = "1 Day"
        week_1 = "1 Week"
        month_1 = "1 Month"
        month_3 = "3 Months"
        ytd = "YTD"
        year_1 = "1 Year"

    @app.get("/historical-book-prices/{book_id}")
    async def route_historical_book_prices(
        book_id: UUID,
        date_filter: AvailableTimePeriods = AvailableTimePeriods.month_1,
        _: UserInfo = Depends(LoggedIn()),
    ) -> Response:
        df, metadata = await historical_book_prices(book_id, date_filter)
        return with_metadata_response(df, metadata=metadata)
