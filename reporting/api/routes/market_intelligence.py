"""Market Intelligence routes."""

from datetime import date
from typing import Annotated, Dict, List, Optional

from fastapi import Depends, FastAPI, HTTPException, Query
from pydantic import Json

from ..auth import LoggedIn, UserInfo
from ..data.market_intelligence import Report, last_report, reports, source


def routes(app: FastAPI) -> None:
    """Routes."""

    @app.get(
        "/market-intelligence/reports",
        tags=["Market Intelligence"],
    )
    async def get_reports(
        limit: Annotated[int, Query(gt=0, le=100)] = 50,
        offset: int = 0,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        _: UserInfo = Depends(LoggedIn()),
    ) -> List[Report]:
        return await reports(limit, offset, start_date, end_date)

    @app.get(
        "/market-intelligence/reports/{date}",
        tags=["Market Intelligence"],
    )
    async def get_report_by_date(
        date: date,
        _: UserInfo = Depends(LoggedIn()),
    ) -> Report:
        r = await last_report(date)

        if not r:
            raise HTTPException(status_code=404, detail="Report not found")

        return r

    @app.get(
        "/market-intelligence/sources/{id}",
        tags=["Market Intelligence"],
    )
    async def get_source(  # type: ignore
        id: int,  # noqa: A002
        _: UserInfo = Depends(LoggedIn()),
    ) -> Dict[str, Json]:
        s = await source(id)
        if not s:
            raise HTTPException(status_code=404, detail="Source not found")

        return s
