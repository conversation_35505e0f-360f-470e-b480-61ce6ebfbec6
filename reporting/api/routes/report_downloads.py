"""Inventory routes."""

import asyncio
import concurrent.futures
import tempfile
from enum import Enum

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Response
from fastapi.responses import StreamingResponse
from starlette.responses import FileResponse

from ..auth import LoggedIn, UserInfo
from ..data.customer_sales import customer_sale_data
from ..data.model_portfolio import model_portfolio_data
from ..data.report_downloads import holding_analytics, market_report, portfolio_analytics


class AvailableReports(str, Enum):
    """Available Reports."""

    portfolio_analytics = "portfolio_analytics"
    holding_analytics = "holding_analytics"
    market_report = "market_report"


def routes(app: FastAPI) -> None:
    """Routes."""

    @app.get(
        "/report-download/{report}",
        tags=["Reports"],
    )
    async def route_report_download(
        report: AvailableReports,
        _: UserInfo = Depends(LoggedIn()),
    ) -> StreamingResponse:
        if report == "portfolio_analytics":
            buffer = await portfolio_analytics()
            return StreamingResponse(
                buffer,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment: filename=portolio.xlsx"},
            )
        if report == "holding_analytics":
            buffer = await holding_analytics()
            return StreamingResponse(
                buffer,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment: filename=holdings.xlsx"},
            )
        if report == "market_report":
            buffer = await market_report()
            return StreamingResponse(
                buffer,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={"Content-Disposition": "attachment: filename=market_report.xlsx"},
            )
        else:
            raise HTTPException(status_code=500, detail="Report not found")

    @app.get(
        "/pdf/model-portfolio",
        tags=["One Pager Documents"],
    )
    async def route_model_portfolio_pdf(
        id: str,  # noqa: A002
        background_tasks: BackgroundTasks,
        _: UserInfo = Depends(LoggedIn()),
    ) -> Response:
        from ..reports.model_portfolio import generate_pdf

        try:
            data, _ = await model_portfolio_data(id)
        except KeyError as err:
            raise HTTPException(status_code=404, detail=str(err)) from err

        with tempfile.NamedTemporaryFile(mode="w+b", suffix=".pdf", delete=False) as pdf:
            loop = asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as pool:
                await loop.run_in_executor(pool, generate_pdf, data, pdf.name)

        background_tasks.add_task(lambda: pdf.delete)

        return FileResponse(
            pdf.name,
            media_type="application/pdf",
            filename=f"{data['name']}.pdf",
        )

    @app.get(
        "/pdf/customer-sale",
        tags=["One Pager Documents"],
    )
    async def route_customer_sale_pdf(
        id: str,  # noqa: A002
        background_tasks: BackgroundTasks,
        _: UserInfo = Depends(LoggedIn()),
    ) -> Response:
        from ..reports.customer_sale import generate_pdf

        try:
            data, _ = await customer_sale_data(id)
        except KeyError as err:
            raise HTTPException(status_code=404, detail=str(err)) from err

        with tempfile.NamedTemporaryFile(mode="w+b", suffix=".pdf", delete=False) as pdf:
            loop = asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as pool:
                await loop.run_in_executor(pool, generate_pdf, data, pdf.name)

        background_tasks.add_task(lambda: pdf.delete)

        return FileResponse(
            pdf.name,
            media_type="application/pdf",
            filename=f"{data['name']}.pdf",
        )
