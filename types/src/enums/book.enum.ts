export enum BookAction {
  BUY = 'buy',
  SELL = 'sell',
  PURCHASE = 'purchase',
  RETIRE = 'retire',
  TRANSFER_OUTFLOW = 'transfer_outflow',
  INTERNAL_TRANSFER = 'transfer',
  RESERVE = 'reserve',
}

export enum BookOrderByOption {
  CREATED_AT = 'createdAt',
  IS_ENABLED = 'isEnabled',
  NAME = 'name',
  SUBTYPE = 'subtype',
  TYPE = 'type',
  UPDATED_AT = 'updatedAt',
}

export enum BookRelations {
  ASSET_ALLOCATIONS = 'asset_allocations',
  ASSET_ALLOCATIONS_NESTED = 'asset_allocations_with_nested',
  ASSET_ALLOCATIONS_BY_BOOK_TYPE = 'asset_allocations_by_book_type',
  PRICES = 'prices',
  ORGANIZATION = 'organization',
  OWNER_ALLOCATIONS = 'owner_allocations',
  OWNER_ALLOCATIONS_NESTED = 'owner_allocations_with_nested',
  OWNER_ALLOCATIONS_BY_ASSET_TYPE = 'owner_allocations_by_asset_type',
  OWNER_ALLOCATIONS_BY_PROJECT = 'owner_allocations_by_project',
  OWNER_ALLOCATIONS_BY_PROJECT_TYPE = 'owner_allocations_by_project_type',
}

export enum BookType {
  AGED_DEFAULT = 'aged:default',
  COMPLIANCE_DEFAULT = 'compliance:default',
  OPPORTUNISTIC_DEFAULT = 'opportunistic:default',
  PORTFOLIO_CUSTOM = 'portfolio:custom',
  PORTFOLIO_CUSTOMER = 'portfolio:customer',
  PORTFOLIO_DEFAULT = 'portfolio:default',
  PORTFOLIO_PUBLIC = 'portfolio:public',
  PORTFOLIO_RESERVES = 'portfolio:reserves',
  REHABILITATION_DEFAULT = 'rehabilitation:default',
}

export const AllBookTypes = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_CUSTOM,
  BookType.PORTFOLIO_CUSTOMER,
  BookType.PORTFOLIO_DEFAULT,
  BookType.PORTFOLIO_PUBLIC,
  BookType.PORTFOLIO_RESERVES,
  BookType.REHABILITATION_DEFAULT,
];

// aka non-customer books
export const AllRubiconHoldingBookTypes = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_CUSTOM,
  BookType.PORTFOLIO_DEFAULT,
  BookType.PORTFOLIO_PUBLIC,
  BookType.PORTFOLIO_RESERVES,
  BookType.REHABILITATION_DEFAULT,
];

// aka non-customer books
export const AvailableRubiconHoldingBookTypes = [
  BookType.AGED_DEFAULT,
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.PORTFOLIO_DEFAULT,
  BookType.REHABILITATION_DEFAULT,
];
