export enum AssetFlowOrderByOptions {
  ASSET_ID = 'asset_id',
  // ASSET_NAME = 'asset_name', // figure out how to implement this later
  CREATED_AT = 'created_at',
  TRANSACTION_ID = 'transaction_id',
  // maybe add source_id/name, destination_id/name
  UPDATED_AT = 'updated_at',
}

// todo : remove flow type
export enum CreditFlowType {
  ACCOUNTING = 'accounting',
  INFLOW = 'inflow',
  OUTFLOW = 'outflow',
}

export enum AssetFlowRelation {
  DESTINATION = 'destination',
  DETAILED_ASSET = 'detailed_asset',
  LINKS = 'links',
  RELATED_OBJECT = 'related_object',
  SOURCE = 'source',
}

export enum AssetFlowStatus {
  CANCELED = 'canceled',
  PENDING = 'pending',
  PRE_PENDING = 'pre-pending',
  SETTLED = 'settled',
}
