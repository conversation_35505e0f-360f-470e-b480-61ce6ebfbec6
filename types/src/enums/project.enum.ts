export enum ProjectBeZeroRating {
  AAA_PLUS = 'AAA+',
  AAA = 'AAA',
  AAA_MINUS = 'AAA-',
  AA_PLUS = 'AA+',
  AA = 'AA',
  AA_MINUS = 'AA-',
  A = 'A',
  BBB = 'BBB',
  BB = 'BB',
  B = 'B',
  C = 'C',
  D = 'D',
}

export enum ProjectEligibilityAccreditation {
  WATER_ORG = 'water_org',
  VERRA_CCB_CERTIFICATION = 'verra_ccb_certification',
  VERRA_SDVISTA_CERTIFICATION = 'verra_sdvista_certification',
  CORSIA_PHASE_PILOT = 'corsia_phase_pilot',
  CORSIA_PHASE_1 = 'corsia_phase_1',
  ICVCM_CCP_LABELED = 'icvcm_ccp_labeled',
}

export enum ProjectRiskScore {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

// note : in prod there are 8800 projects with null emissions type
export enum ProjectEmissionsImpactType {
  COMPLIANCE = 'Compliance',
  REDUCTIONS = 'Reductions',
  REDUCTIONS_REMOVALS = 'Reductions and Removals',
  REMOVALS = 'Removals',
}

export enum ProjectInelibleReasons {
  IS_SUSPENDED = 'suspended',
  NOT_RCT_STANDARD = 'not rct standard',
  NOT_SCIENCE_TEAM_APPROVED = 'not science team approved',
  RISK_BUFFER_PERCENTAGE = 'risk buffer percentage > 100',
}

export enum ProjectOrderByOption {
  CREATED_AT = 'createdAt',
  END_DATE = 'endDate',
  NAME = 'name',
  START_DATE = 'startDate',
  UPDATED_AT = 'updatedAt',
}

export enum ProjectRelations {
  ASSET_ALLOCATIONS = 'asset_allocations',
  ASSET_ALLOCATIONS_BY_BOOK_TYPE = 'asset_allocations_by_book_type',
  BUFFER_CATEGORY = 'bufferCategory',
  COUNTRY = 'country',
  MINIMUM_BYO = 'minimumBYO',
  PRICE_RANGE = 'price_range',
  PRODUCTS = 'products',
  PROJECT_TYPE = 'projectType',
  PROJECT_VINTAGES = 'projectVintages',
  SDGS = 'projectSDGs',
}
