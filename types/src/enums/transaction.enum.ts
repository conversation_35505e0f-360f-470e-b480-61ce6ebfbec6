import { ForwardStatus, ForwardType } from './forward.enum';
import { MarketingAgreementStatus } from './marketing-agreement.enum';
import { TradeStatus, TradeType, TradeUpdatableStatus } from './trade.enum';

export enum TransactionOrderByOptions {
  AMOUNT = 'amount',
  COUNTERPARTY = 'counterparty',
  CREATED_AT = 'created_at',
  STATUS = 'status',
  SUBTYPE = 'subtype',
  TOTAL_PRICE = 'total_price',
  TYPE = 'type',
  UI_KEY = 'ui_key',
  UPDATED_AT = 'updated_at',
}

export enum TransactionType {
  FORWARD_LINE_ITEM = 'forward_line_item',
  INTERNAL_TRANSFER = 'internal_transfer',
  MARKETING_AGREEMENT_LINE_ITEM = 'marketing_agreement_line_item',
  PURCHASE = 'purchase',
  RESERVE = 'reserve',
  RETIREMENT = 'retirement',
  TRADE = 'trade',
}

export const AllTransactionTypes = [
  TransactionType.FORWARD_LINE_ITEM,
  TransactionType.INTERNAL_TRANSFER,
  TransactionType.MARKETING_AGREEMENT_LINE_ITEM,
  TransactionType.PURCHASE,
  TransactionType.RESERVE,
  TransactionType.RETIREMENT,
  TransactionType.TRADE,
];

export const AssetFlowsTransactionTypes = [
  TransactionType.FORWARD_LINE_ITEM,
  TransactionType.INTERNAL_TRANSFER,
  TransactionType.MARKETING_AGREEMENT_LINE_ITEM,
  TransactionType.PURCHASE,
  TransactionType.RESERVE,
  TransactionType.RETIREMENT,
  TransactionType.TRADE,
];

export type TransactionSubtype = TradeType | RetirementType | ForwardType;

export enum PurchaseOrderByOptions {
  CREATED_AT = 'created_at',
  CUSTOMER_PORTFOLIO_ORGANIZATION_NAME = 'customer_portfolio_organization_name',
  DATE_FINISHED = 'date_finished',
  DATE_STARTED = 'date_started',
  STATUS = 'status',
  UI_KEY = 'ui_key',
  UPDATED_AT = 'updated_at',
}

export enum RetirementOrderByOptions {
  CREATED_AT = 'created_at',
  CUSTOMER_PORTFOLIO_ORGANIZATION_NAME = 'customer_portfolio_organization_name',
  DATE_FINISHED = 'date_finished',
  DATE_STARTED = 'date_started',
  STATUS = 'status',
  TYPE = 'type',
  UI_KEY = 'ui_key',
  UPDATED_AT = 'updated_at',
}

export enum RetirementType {
  RETIREMENT = 'retirement',
  TRANSFER_OUTFLOW = 'transfer_outflow',
}

export enum RetirementStatus {
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELED = 'canceled',
  PORTFOLIO_MANAGER_REVIEW = 'portfolio_manager_review',
  ADMIN_REVIEW = 'admin_review',
  FAILED = 'failed',
}

export enum PurchaseStatus {
  BINDING = 'binding',
  CANCELED = 'canceled',
  EXECUTED = 'executed',
  FIRM = 'firm',
  SETTLED = 'settled',
}

export enum PurchaseUpdateStatus {
  BIND = 'bind',
  CANCEL = 'cancel',
  DELIVER = 'deliver',
  EXECUTE = 'execute',
  PAY = 'pay',
}

export type PurchaseUpdatableStatus = TradeUpdatableStatus;

export enum RetirementUpdateStatus {
  CALCULATE = 'calculate',
  CANCEL = 'cancel',
  COMPLETE = 'complete',
  PROCESS = 'process',
}

export enum PurchaseFlowType {
  PURCHASE_TO_RETIRE = 'purchase_to_retire',
  PURCHASE_AND_HOLD = 'purchase_and_hold',
  TRANSFER_TO_CUSTOMER_ACCOUNT = 'transfer_to_customer_account',
}

export enum RetirementRelations {
  ASSETS = 'assets',
  CUSTOMER_PORTFOLIO = 'customer_portfolio',
  LINKS = 'links',
  RCT_VINTAGES = 'rct_vintages',
  REQUESTED_BY = 'requested_by',
}

export enum PurchaseRelations {
  ASSETS = 'assets',
  CUSTOMER_PORTFOLIO = 'customer_portfolio',
}

export type TransactionStatus =
  | PurchaseStatus
  | RetirementStatus
  | TradeStatus
  | ForwardStatus
  | MarketingAgreementStatus
  | 'paid' // trades & purchases
  | 'delivered' // trades & purchases
  | 'reserved' // reserves
  | 'deleted' // reserves
  | 'executed'; // transfers

export const AllPortalTransactionStatuses: TransactionStatus[] = [
  ForwardStatus.CANCELED,
  ForwardStatus.PENDING,
  ForwardStatus.SETTLED,
  PurchaseStatus.CANCELED,
  PurchaseStatus.EXECUTED,
  PurchaseStatus.SETTLED,
  RetirementStatus.PROCESSING,
  RetirementStatus.COMPLETED,
  RetirementStatus.CANCELED,
  RetirementStatus.PORTFOLIO_MANAGER_REVIEW,
  RetirementStatus.ADMIN_REVIEW,
  RetirementStatus.FAILED,
];

export const AllAdminTransactionStatuses: TransactionStatus[] = [
  ...AllPortalTransactionStatuses,
  MarketingAgreementStatus.CANCELED,
  MarketingAgreementStatus.PENDING,
  PurchaseStatus.BINDING,
  PurchaseStatus.FIRM,
  TradeStatus.BINDING,
  TradeStatus.CANCELED,
  TradeStatus.EXECUTED,
  TradeStatus.FIRM,
  TradeStatus.INDICATIVE,
  TradeStatus.SETTLED,
  'paid',
  'delivered',
  'reserved',
  'deleted',
  'executed',
];
