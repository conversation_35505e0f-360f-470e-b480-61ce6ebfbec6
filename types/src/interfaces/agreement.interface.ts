import { AgreementOrderByOption, AgreementStatus } from '../enums/agreement.enum';
import { json } from '../types/json.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';

export interface AgreementRequest {
  status: AgreementStatus;
  version?: string;
}

export interface AgreementResponse extends BaseResponse {
  acceptedDate?: Date;
  label: string;
  status: AgreementStatus;
  metadata?: Record<string, json>;
  user?: TrimmedUserResponse;
  version: string;
}

export interface AgreementQuery extends BaseQuery {
  acceptedAfterDate?: Date;
  acceptedBeforeDate?: Date;
  label?: string;
  status?: AgreementStatus;
  version?: string;
  orderBy?: AgreementOrderByOption;
}

export interface AgreementQueryResponse extends BaseQueryResponse {
  data: AgreementResponse[];
}
