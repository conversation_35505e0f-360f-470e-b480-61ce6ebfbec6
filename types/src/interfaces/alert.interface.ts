import Decimal from 'decimal.js';
import { AlertEvent, PortfolioPolicyViolationCategory } from '../enums/alert.enum';
import { uuid } from '../types/uuid.type';
import { BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedCountryResponse } from './country.interface';
import { ProjectInelibleReasons } from '../enums/project.enum';

export interface PortfolioViolationCountryConcentrationResponse {
  country: TrimmedCountryResponse;
  actualPercentage: Decimal;
  expectedMaxPercentage: Decimal;
}

export interface PortfolioViolationProjectConcentrationResponse {
  project: { id: uuid; name: string; registryProjectId: string };
  actualPercentage: Decimal;
  expectedMaxPercentage: Decimal;
}

export interface PortfolioViolationProjectVintageIneligibleResponse {
  projectVintage: { id: uuid; name: string; project: { id: uuid; name: string; registryProjectId: string } };
  ineligibleReasons: ProjectInelibleReasons[];
  isRctStandard: boolean;
  isScienceTeamApproved: boolean;
  isSuspended: boolean;
  riskBufferPercentage?: Decimal;
}

export interface AlertPortfolioPolicyViolationResponse {
  portfolio: { id: uuid; name: string; isEnabled: boolean }; // public portfolio id aka book where type = BookType.PORTFOLIO_PUBLIC
  violationCategory: PortfolioPolicyViolationCategory;
  violationDetails:
    | PortfolioViolationCountryConcentrationResponse
    | PortfolioViolationProjectConcentrationResponse
    | PortfolioViolationProjectVintageIneligibleResponse;
}

export interface AlertPortfolioStalePriceResponse {
  portfolio: { id: uuid; name: string; isEnabled: boolean }; // public portfolio id aka book where type = BookType.PORTFOLIO_PUBLIC
  lastUpdatedAt: Date;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface AlertSubscriptionRequest {
  subscribedEvents: AlertEvent[];
}

export interface AlertResponse {
  id: uuid;
  openedAt: Date;
  event: AlertEvent;
  details: AlertPortfolioPolicyViolationResponse | AlertPortfolioStalePriceResponse;
}

export interface AlertSubscriptionResponse extends BaseResponse {
  event: AlertEvent;
}

export interface AlertQueryResponse extends BaseQueryResponse {
  data: AlertResponse[];
}

export interface AlertSubscriptionQueryResponse extends BaseQueryResponse {
  data: AlertSubscriptionResponse[];
}
