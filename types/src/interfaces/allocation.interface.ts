import Decimal from 'decimal.js';
import { AssetType } from '../enums/asset.enum';
import { BookType } from '../enums/book.enum';
import { TrimmedAssetResponse } from './asset.interface';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectResponse } from './project.interface';
import { TrimmedProjectTypeResponse } from './project-type.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';

export interface AllocationResponse {
  amountAllocated: number; // amount settled
  amountAvailable: number; // settled - pending_outflow
  amountPendingBuy: number;
  amountCustomerTransferredOutflow: number;
  amountPendingCustomerTransferOutflow: number;
  amountPendingPurchase: number;
  amountPendingRetirement: number;
  amountPendingSell: number;
  amountRetired: number;
  amountSold: number;
  asset: TrimmedAssetResponse;
  detailedAsset?: TrimmedProjectVintageResponse | TrimmedBookResponse;
  averageCostBasis?: Decimal;
  currentPrice?: Decimal;
  owner: TrimmedBookResponse;
}

export interface GroupedPriceResponse {
  totalPriceAllocated: Decimal; // total settled
  totalPriceAvailable: Decimal; // settled - pending_outflow
  totalPriceCustomerTransferredOutflow: Decimal;
  totalPricePendingBuy: Decimal;
  totalPricePendingCustomerTransferOutflow: Decimal;
  totalPricePendingPurchase: Decimal;
  totalPricePendingRetirement: Decimal;
  totalPricePendingSell: Decimal;
  totalPriceRetired: Decimal;
  totalPriceSold: Decimal;
}

export interface GroupedAllocationResponse {
  groupedPrices?: GroupedPriceResponse;
  totalAmountAllocated: number;
  totalAmountAvailable: number;
  totalAmountCustomerTransferredOutflow: number;
  totalAmountPendingBuy: number;
  totalAmountPendingCustomerTransferOutflow: number;
  totalAmountPendingPurchase: number;
  totalAmountPendingRetirement: number;
  totalAmountPendingSell: number;
  totalAmountRetired: number;
  totalAmountSold: number;
}

export interface GroupedAllocationWithNestedResponse extends GroupedAllocationResponse {
  allocations: AllocationResponse[];
}

export interface AssetGroupedAllocationWithNestedResponse extends GroupedAllocationWithNestedResponse {
  asset: TrimmedAssetResponse;
}

export interface AssetTypeGroupedAllocationResponse extends GroupedAllocationResponse {
  assetType: AssetType;
}

export interface BookTypeGroupedAllocationResponse extends GroupedAllocationResponse {
  bookType: BookType;
}

export interface ProjectGroupedAllocationResponse extends GroupedAllocationResponse {
  project: TrimmedProjectResponse;
}

export interface ProjectTypeGroupedAllocationResponse extends GroupedAllocationResponse {
  projectType: TrimmedProjectTypeResponse;
}
