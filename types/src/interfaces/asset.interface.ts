import Decimal from 'decimal.js';
import { AssetType } from '../enums/asset.enum';
import { uuid } from '../types/uuid.type';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { RetirementLink } from './retirement-link.interface';
import { BaseResponse } from './base.interface';

export interface CommonAssetRequest {
  amount: number;
  assetId: uuid;
}

export interface AssetTransferRequest extends CommonAssetRequest {
  destinationId: uuid;
  sourceId: uuid;
}

export interface AssetPurchaseRequest extends CommonAssetRequest {
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  sourceId: uuid; // a purchase can be sourced from mulitple books
}

export interface AssetRetirementRequest extends CommonAssetRequest {
  otherFee?: Decimal;
  rawPrice: Decimal;
  serviceFee?: Decimal;
}

// todo : maybe eventually just change to AssetRequest
export interface VintageAssetRequest {
  amount: number;
  projectVintageId: uuid;
}

export interface VintageAssetDetailsResponse {
  isByorctApproved: boolean;
  isRctStandard: boolean;
  isScienceTeamApproved: boolean;
  isSuspended: boolean;
}

export interface TrimmedAssetResponse extends BaseResponse, Partial<VintageAssetDetailsResponse> {
  label?: string;
  name: string;
  projectId?: uuid;
  registryProjectId?: string;
  type: AssetType;
}

export interface AssetResponse extends TrimmedAssetResponse {
  isPublic: boolean;
  limitedTo?: TrimmedBookResponse;
}

export interface AssetTransferResponse {
  amount: number;
  asset: TrimmedAssetResponse;
  destination: TrimmedBookResponse;
  source: TrimmedBookResponse;
}

export interface VintageAssetResponse {
  amount: number;
  destination?: TrimmedBookResponse;
  otherFee?: Decimal;
  projectVintage: TrimmedProjectVintageResponse;
  rawPrice: Decimal;
  serviceFee?: Decimal;
  source?: TrimmedBookResponse;
  links?: RetirementLink[];
}

export interface RctAssetResponse {
  amount: number;
  associatedVintages?: VintageAssetResponse[];
  destination?: TrimmedBookResponse;
  otherFee?: Decimal;
  rawPrice: Decimal;
  rct: TrimmedBookResponse;
  serviceFee?: Decimal;
  source?: TrimmedBookResponse;
}

export interface AssetTypeFilterQuery {
  assetTypes?: AssetType[];
}
