import Decimal from 'decimal.js';
import { BookAction, BookOrderByOption, BookRelations, BookType } from '../enums/book.enum';
import { uuid } from '../types/uuid.type';
import {
  AssetTypeGroupedAllocationResponse,
  BookTypeGroupedAllocationResponse,
  GroupedAllocationResponse,
  GroupedAllocationWithNestedResponse,
  ProjectGroupedAllocationResponse,
  ProjectTypeGroupedAllocationResponse,
} from './allocation.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedOrganizationResponse } from './organization.interface';

export interface BookLimit {
  holdingAmountMax?: number;
  holdingAmountMin?: number;
  holdingPriceMax?: Decimal;
  holdingPriceMin?: Decimal;
}

export interface CustomPortfolioBookCreateRequest {
  description?: string;
  isEnabled: boolean;
  limit?: BookLimit;
  name: string;
  organizationId?: uuid;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface PublicPortfolioBookCreateRequest {
  description?: string;
  isEnabled: boolean;
  limit?: BookLimit;
  name: string;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface BookUpdateRequest {
  description?: string;
  isEnabled?: boolean;
  limit?: BookLimit;
  name?: string;
  projectTypeIds?: number[];
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface TrimmedBookResponse extends BaseResponse {
  allowedActions: BookAction[]; // not in portal
  description?: string;
  isEnabled: boolean;
  name: string;
  organization?: TrimmedOrganizationResponse; // not in portal
  type: BookType;
}

export interface AdminBookResponse extends BaseResponse, TrimmedBookResponse {
  // assetAllocations are the rct allocations of the book
  // they only exist for books that generate an asset (i.e. public/custom)
  assetAllocations?: GroupedAllocationResponse | GroupedAllocationWithNestedResponse;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
  // ownerAllocations are assets that belong to the book
  // i.e. public/custom/reserves/compliance/rehab/opportunistic = vintages, portfolioDefault/customer = vintages/rct
  ownerAllocations?: GroupedAllocationResponse | GroupedAllocationWithNestedResponse;
  ownerAllocationsByAssetType?: AssetTypeGroupedAllocationResponse[];
  ownerAllocationsByProject?: ProjectGroupedAllocationResponse[]; // will only count vintages
  ownerAllocationsByProjectType?: ProjectTypeGroupedAllocationResponse[]; // will only count vintages
  limit: BookLimit;
  priceUpdatedAt?: Date;
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export interface PortalBookResponse
  extends BaseResponse,
    Omit<Omit<TrimmedBookResponse, 'allowedActions'>, 'organization'> {
  assetAllocations?: GroupedAllocationResponse | GroupedAllocationWithNestedResponse;
  ownerAllocations?: GroupedAllocationResponse | GroupedAllocationWithNestedResponse;
  ownerAllocationsByProject?: ProjectGroupedAllocationResponse[]; // will only count vintages
  purchasePrice?: Decimal;
  purchasePriceWithBuffer?: Decimal;
}

export type MobileBookResponse = PortalBookResponse;

export interface BookEstimateRetirementResponse {
  amountTransacted: number;
  projectVintage: TrimmedProjectVintageResponse;
}

export interface BookRelationsQuery {
  includeRelations?: BookRelations[];
}

export interface PortalBookQuery extends BaseQuery, BookRelationsQuery {
  ids?: uuid[];
  name?: string;
  types?: BookType[];
  orderBy?: BookOrderByOption;
}

export interface AdminBookQuery extends PortalBookQuery {
  allowedAction?: BookAction;
  isEnabled?: boolean;
  organizationId?: uuid;
  projectVintageId?: uuid;
}

export type MobileBookQuery = PortalBookQuery;

export interface AdminBookQueryResponse extends BaseQueryResponse {
  data: AdminBookResponse[];
}

export interface PortalBookQueryResponse extends BaseQueryResponse {
  data: PortalBookResponse[];
}

export interface MobileBookQueryResponse extends BaseQueryResponse {
  data: MobileBookResponse[];
}
