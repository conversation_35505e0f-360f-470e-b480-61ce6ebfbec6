import { CountryRelation, CountryOrderByOption, CountryRegions } from '../enums/country.enum';
import { BaseQuery, BaseQueryResponse } from './base.interface';
import { PortalProjectResponse } from './project.interface';

export interface CountryRequest {
  alpha2: string;
  alpha3: string;
  name: string;
  region: CountryRegions;
  subRegion: string;
}

export type TrimmedCountryResponse = CountryRequest;

export interface CountryResponse extends TrimmedCountryResponse {
  projectCount?: number;
  projects?: PortalProjectResponse[]; // todo (TD-8) : maybe eventually this can use TrimmedProjectResponse
}

export interface CountryQuery extends BaseQuery {
  countProjects?: boolean;
  region?: CountryRegions;
  subRegion?: string;
  orderBy?: CountryOrderByOption;
}

export interface CountryRelationsQuery {
  includeRelations?: CountryRelation[];
}

export interface CountryQueryResponse extends BaseQueryResponse {
  data: CountryResponse[];
}
