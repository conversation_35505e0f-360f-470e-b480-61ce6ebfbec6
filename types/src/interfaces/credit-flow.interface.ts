import { uuid } from '../types/uuid.type';
import { BaseQueryResponse, BaseResponse } from './base.interface';

export interface CreditOutflowResponse extends BaseResponse {
  amount: number;
}

export interface CreditInflowResponse extends BaseResponse {
  amount: number;
  amountRetired?: number;
  amountSold?: number;
  outflows?: CreditOutflowResponse[];
  projectVintageId?: uuid;
}

export interface CreditInflowQueryResponse extends BaseQueryResponse {
  data: CreditInflowResponse[];
}

export interface CreditOutflowQueryResponse extends BaseQueryResponse {
  data: CreditOutflowResponse[];
}
