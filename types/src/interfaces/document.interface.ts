import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { DocumentType } from '../enums/document.enum';
import { TrimmedOrganizationResponse } from './organization.interface';

export interface DocumentUploadUrlRequest {
  filename: string;
  isPublic: boolean;
  organizationId?: uuid;
  type: DocumentType;
  relatedUiKey?: string;
}

export interface DocumentUpdateRequest {
  filename: string;
  id: uuid;
  isPublic: boolean;
  organizationId?: uuid;
  type: DocumentType;
  relatedUiKey?: string;
}

export interface DocumentUrlRefreshRequest {
  documentIds: uuid[];
}

export interface DocumentUploadUrlResponse extends BaseResponse {
  organization?: TrimmedOrganizationResponse;
  uploadUrl: string;
}

export interface PortalDocumentResponse extends BaseResponse {
  downloadUrl: string;
  filename: string;
  organization?: TrimmedOrganizationResponse;
  relatedUiKey?: string;
  type: DocumentType;
  uploadedAt: Date;
}

export interface AdminDocumentResponse extends PortalDocumentResponse {
  isPublic: boolean;
  isUploaded: boolean;
  uploadedById: uuid;
}

export interface DocumentQuery extends BaseQuery {
  organizationId?: uuid;
  types?: DocumentType[];
  relatedUiKey?: string;
}

export interface PortalDocumentQueryResponse extends BaseQueryResponse {
  data: PortalDocumentResponse[];
}

export interface AdminDocumentQueryResponse extends BaseQueryResponse {
  data: AdminDocumentResponse[];
}
