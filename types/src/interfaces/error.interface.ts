export interface ErrorResponse {
  error: string;
  details?: string;
}

// currently this is only used for UniqueConstraintViolationException
export interface ErrorDetailsResponse {
  constraint: string;
  field: string; // field of failure
  message: string;
  name: string; // exception name, e.g. UniqueConstraintViolationException
  status: number; // repeat of res.status
  value: string; // input that caused failure (if applicable), eventually might need to be any type
}
