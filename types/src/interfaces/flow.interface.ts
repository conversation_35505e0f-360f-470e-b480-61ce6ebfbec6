import { AssetType } from '../enums/asset.enum';
import { OrderByDirection } from '../enums/base.enum';
import { AssetFlowOrderByOptions, AssetFlowRelation, AssetFlowStatus } from '../enums/flow.enum';
import { uuid } from '../types/uuid.type';
import { TrimmedTransactionResponse } from './transaction.interface';
import { TrimmedAssetResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { RetirementLink } from './retirement-link.interface';
import { TransactionSubtype, TransactionType } from '../enums/transaction.enum';

// note : this is only used for retirements/transfer_outflows fo rnow
export interface AssetFlowResponse extends BaseResponse {
  amount: number;
  asset: TrimmedAssetResponse;
  detailedAsset?: TrimmedProjectVintageResponse | TrimmedBookResponse; // need country on portal retirements page :(
  destination?: TrimmedBookResponse;
  links?: RetirementLink[];
  source?: TrimmedBookResponse;
  transaction?: TrimmedTransactionResponse;
  transactionSubtype?: TransactionSubtype;
  transactionType: TransactionType;
}

export interface TrimmedAssetFlowResponse extends BaseResponse {
  amount: number;
  asset: TrimmedAssetResponse;
  lastUpdatedDeliveryDate?: Date;
  settledAt?: Date;
  status: 'pending' | 'settled' | 'canceled';
}

export interface AssetFlowQuery extends Omit<BaseQuery, 'orderBy'> {
  assetId?: uuid;
  assetType?: AssetType;
  transactionSubtypes?: TransactionSubtype[];
  transactionTypes?: TransactionType[];
  transactionId?: uuid;
  status?: AssetFlowStatus;
  includeRelations?: AssetFlowRelation[];
  orderBys?: { orderBy: AssetFlowOrderByOptions; orderByDirection: OrderByDirection }[] | string[]; // FE will pass in a string array that is formated field:direction
}

export interface AssetFlowQueryResponse extends BaseQueryResponse {
  data: AssetFlowResponse[];
}
