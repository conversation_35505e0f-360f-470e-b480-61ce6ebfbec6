import Decimal from 'decimal.js';
import { ForwardOrderByOptions, ForwardStatus, ForwardType } from '../enums/forward.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedBookResponse } from './book.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedProjectResponse } from './project.interface';
import { OrderByDirection } from '../enums/base.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { OrganizationType } from '../enums/organization.enum';

export interface ForwardOrganizationResponse extends TrimmedOrganizationResponse {
  type: OrganizationType;
}

export interface ForwardCreateRequest {
  memo?: string;
  organizationId: uuid;
  projectId: uuid;
  type: ForwardType;
}

export interface ForwardUpdateRequest {
  memo?: string;
}

export interface ForwardLineItemCreateRequest {
  bookId?: uuid;
  expectedAmount: number;
  maxAmount: number;
  minAmount: number;
  otherFee?: Decimal;
  originalDeliveryDate: Date;
  rawPrice: Decimal;
  projectVintageId?: uuid;
  serviceFee?: Decimal;
}

export interface ForwardLineItemUpdateRequest {
  bookId?: uuid;
  expectedAmount?: number;
  lastUpdatedDeliveryDate?: Date;
  maxAmount?: number;
  minAmount?: number;
  otherFee?: Decimal;
  rawPrice?: Decimal;
  projectVintageId?: uuid;
  serviceFee?: Decimal;
}

export interface ForwardLineItemSettleRequest extends ForwardLineItemUpdateRequest {
  settledAmount: number;
  settledAt: Date;
}

export interface ForwardLineItemResponse extends BaseResponse {
  book?: TrimmedBookResponse;
  docsCount: number;
  expectedAmount: number;
  lastUpdatedDeliveryDate: Date;
  maxAmount: number;
  minAmount: number;
  otherFee?: Decimal;
  originalDeliveryDate: Date;
  rawPrice: Decimal;
  projectVintage?: TrimmedProjectVintageResponse;
  serviceFee?: Decimal;
  settledAmount?: number;
  settledAt?: Date;
  status: ForwardStatus;
  uiKey: string;
}

export interface ForwardResponse extends BaseResponse {
  amount: number;
  docsCount: number;
  lineItems: ForwardLineItemResponse[];
  memo?: string;
  organization: ForwardOrganizationResponse;
  project: TrimmedProjectResponse;
  status: ForwardStatus;
  type: ForwardType;
  uiKey: string;
}

export interface ForwardOrderBy {
  orderBy: ForwardOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface ForwardQuery extends Omit<BaseQuery, 'orderBy'> {
  /* base */
  orderBys?: ForwardOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  organizationId?: uuid;
  projectId?: uuid;
  status?: ForwardStatus;
  type?: ForwardType;
  uiKey?: string;
}

export interface ForwardQueryResponse extends BaseQueryResponse {
  data: ForwardResponse[];
}
