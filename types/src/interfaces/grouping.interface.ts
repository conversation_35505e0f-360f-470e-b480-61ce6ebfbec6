import { GroupingRelations } from '../enums/grouping.enum';
import {
  AssetTypeGroupedAllocationResponse,
  GroupedAllocationResponse,
  GroupedAllocationWithNestedResponse,
} from './allocation.interface';
import { BaseQueryResponse, BaseResponse } from './base.interface';
import { BookLimit, TrimmedBookResponse } from './book.interface';

export interface GroupingParentResponse extends BaseResponse {
  ownerAllocations?: GroupedAllocationResponse | GroupedAllocationWithNestedResponse;
  ownerAllocationsByAssetType?: AssetTypeGroupedAllocationResponse[];
  books: TrimmedBookResponse[]; // note : see if FE can just use the ownerAllocations.allocations for this
  description: string;
  limit?: BookLimit;
  name: string;
}

export interface GroupingRelationsQuery {
  includeRelations?: GroupingRelations[];
}

export interface GroupingParentQueryResponse extends BaseQueryResponse {
  data: GroupingParentResponse[];
}
