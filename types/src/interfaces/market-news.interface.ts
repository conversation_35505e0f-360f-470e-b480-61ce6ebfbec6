import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { MarketNewOrderByOptions, MarketNewsScope } from '../enums/market-news.enum';
import { TrimmedProjectResponse } from './project.interface';

export interface MarketNewsCreateRequest {
  source: string;
  hitword?: string;
  score?: number;
  articleDate: Date;
  isIrrelevant?: boolean;
  header: string;
  summary: string;
  url: string;
  scopes: MarketNewsScope[];
}

export interface MarketNewsTagRequest {
  projectIds: uuid[];
}

export interface MarketNewsUpdateRequest {
  source?: string;
  hitword?: string;
  score?: number;
  articleDate?: Date;
  isIrrelevant?: boolean;
  header?: string;
  summary?: string;
  url?: string;
  scopes?: MarketNewsScope[];
}

export interface MarketNewsResponse extends BaseResponse {
  source: string;
  hitword?: string;
  score?: number;
  articleDate: Date;
  isIrrelevant?: boolean;
  header: string;
  summary: string;
  url: string;
  scopes: MarketNewsScope[];
  projects: TrimmedProjectResponse[];
}

export interface MarketNewsQuery extends BaseQuery {
  source?: string;
  hitword?: string;
  startDate?: Date;
  endDate?: Date;
  isIrrelevant?: boolean;
  scopes?: MarketNewsScope[];
  projectIds?: uuid[];
  orderby?: MarketNewOrderByOptions;
}

export interface MarketNewsQueryResponse extends BaseQueryResponse {
  data: MarketNewsResponse[];
}
