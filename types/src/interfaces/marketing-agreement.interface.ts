import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import {
  MarketingAgreementOrderByOptions,
  MarketingAgreementStatus,
  MarketingAgreementType,
} from '../enums/marketing-agreement.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectResponse } from './project.interface';
import { OrderByDirection } from '../enums/base.enum';

export interface MarketingAgreementCreateRequest {
  feeCalculatorUrl?: string;
  floorPrice: Decimal;
  memo?: string;
  organizationId: uuid;
  projectId: uuid;
  status?: MarketingAgreementStatus;
  type?: MarketingAgreementType;
}

export interface MarketingAgreementUpdateRequest {
  feeCalculatorUrl?: string;
  floorPrice?: Decimal;
  memo?: string;
  organizationId?: uuid;
  status?: MarketingAgreementStatus;
  type?: MarketingAgreementType;
}

export interface MarketingAreementLineItemCreateRequest {
  amount: number;
  amountIssued?: number;
  expirationDate?: Date;
  deliveryDate: Date;
  projectVintageId?: uuid;
  status?: MarketingAgreementStatus;
}

export interface MarketingAgreementLineItemUpdateRequest {
  amount?: number;
  amountIssued?: number;
  expirationDate?: Date;
  deliveryDate?: Date;
  projectVintageId?: uuid;
  status?: MarketingAgreementStatus;
}

export interface MarketingAgreementLineItemResponse extends BaseResponse {
  amount: number;
  amountIssued?: number;
  docsCount: number;
  expirationDate?: Date;
  lastUpdatedDeliveryDate: Date;
  originalDeliveryDate: Date;
  projectVintage?: TrimmedProjectVintageResponse;
  status: MarketingAgreementStatus;
  uiKey: string;
}

export interface MarketingAgreementResponse extends BaseResponse {
  docsCount: number;
  feeCalculatorUrl?: string;
  floorPrice: Decimal;
  lineItems: MarketingAgreementLineItemResponse[];
  memo?: string;
  organization: TrimmedOrganizationResponse;
  project: TrimmedProjectResponse;
  status: MarketingAgreementStatus;
  type?: MarketingAgreementType;
  uiKey: string;
}

export interface MarketingAgreementOrderBy {
  orderBy: MarketingAgreementOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface MarketingAgreementQuery extends Omit<BaseQuery, 'orderBy'> {
  /* base */
  orderBys?: MarketingAgreementOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  organizationId?: uuid;
  projectId?: uuid;
  status?: MarketingAgreementStatus;
  uiKey?: string;
}

export interface MarketingAgreementQueryResponse extends BaseQueryResponse {
  data: MarketingAgreementResponse[];
}
