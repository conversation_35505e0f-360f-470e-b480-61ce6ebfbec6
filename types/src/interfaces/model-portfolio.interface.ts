import Decimal from 'decimal.js';
import {
  ModelPortfolioComponentOrderByOption,
  ModelPortfolioOrderByOption,
  ModelPortfolioRelations,
  ModelPortfolioStatus,
} from '../enums/model-portfolio.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { PortalProjectVintageResponse, AdminProjectVintageResponse } from './project-vintage.interface';
import { TrimmedProjectResponse } from './project.interface';
import { BufferCategoryResponse } from './buffer-category.interface';
import { TrimmedOrganizationResponse } from './organization.interface';
import { OrderByDirection } from '../enums/base.enum';

export interface CreateModelPortfolioRequest {
  groupingId?: uuid;
  includeRiskAdjustment?: boolean;
  memo?: string;
  name?: string;
  organizationId?: uuid;
  priceEstimate?: Decimal;
  showCustomer?: boolean;
  status?: ModelPortfolioStatus;
}

export type UpdateModelPortfolioRequest = Partial<CreateModelPortfolioRequest>;

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface PublicModelPortfolioResponse extends BaseResponse {
  includeRiskAdjustment?: boolean;
  memo?: string;
  modelPortfolioComponents?: ModelPortfolioComponentResponse[];
  name: string;
  organization?: TrimmedOrganizationResponse;
  priceEstimate?: Decimal;
  status?: ModelPortfolioStatus;
  uiKey: string;
}

export interface ModelPortfolioResponse extends PublicModelPortfolioResponse {
  createdBy: TrimmedUserResponse;
  groupedPortfolios?: AdminGroupedPortfolioResponse[];
  groupingId: uuid;
  showCustomer?: boolean;
  updatedBy: TrimmedUserResponse;
}

export interface AdminGroupedPortfolioResponse
  extends BaseResponse,
    Pick<
      ModelPortfolioResponse,
      'createdBy' | 'name' | 'organization' | 'priceEstimate' | 'showCustomer' | 'status' | 'uiKey' | 'updatedBy'
    > {
  totalAmount: number;
}

export interface ModelPortfolioComponentBase {
  amountAllocated: number;
  bufferPercentage?: Decimal;
  bufferCategoryId?: uuid;
  costBasis?: Decimal;
  isBufferComponent: boolean;
  portfolioManagerEstimate?: Decimal;
  projectId?: uuid;
  projectPipeline?: boolean;
  registryProjectId?: string; // todo (TD-8) : this should be deprecated to use project.registryProjectId
  vintageId?: uuid;
  vintageInterval?: string;
  vintagePipeline?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface ModelPortfolioComponentCreate extends ModelPortfolioComponentBase {}

export interface ModelPortfolioComponentUpdate {
  id: uuid;
  amountAllocated?: number;
  bufferPercentage?: Decimal;
  bufferCategoryId?: uuid;
  costBasis?: Decimal;
  isBufferComponent?: boolean;
  portfolioManagerEstimate?: Decimal;
  registryProjectId?: string;
  vintageId?: uuid;
}

export interface ModelPortfolioComponentDelete {
  id: uuid;
}

export interface ModelPortfolioComponentUpdateRequest {
  create?: ModelPortfolioComponentCreate[];
  delete?: ModelPortfolioComponentDelete[];
  update?: ModelPortfolioComponentUpdate[];
}

export interface ModelPortfolioComponentResponse extends ModelPortfolioComponentBase, BaseResponse {
  bufferCategory?: BufferCategoryResponse;
  modelPortfolio?: ModelPortfolioResponse;
  project?: TrimmedProjectResponse;
  projectVintage?: PortalProjectVintageResponse | AdminProjectVintageResponse;
  createdBy?: TrimmedUserResponse;
  updatedBy?: TrimmedUserResponse;
  vintage?: PortalProjectVintageResponse | AdminProjectVintageResponse; // todo (TD-8) : this should be deprecated to use projectVintage
}

export interface ModelPortfolioOrderBy {
  orderBy: ModelPortfolioOrderByOption;
  orderByDirection: OrderByDirection;
}

export interface ModelPortfolioRelationsQuery {
  includeRelations?: ModelPortfolioRelations[];
}

export interface PublicModelPortfolioQuery extends BaseQuery, ModelPortfolioRelationsQuery {
  name?: string;
  projectId?: uuid;
  registryProjectId?: string;
  status?: ModelPortfolioStatus;
  uiKey?: string;
  vintageId?: uuid;
  orderBys?: ModelPortfolioOrderBy[];
}

export interface ModelPortfolioQuery extends PublicModelPortfolioQuery {
  createdById?: uuid;
  organizationId?: uuid;
  showCustomer?: boolean;
  updatedById?: uuid;
}

export interface ModelPortfolioComponentQuery extends BaseQuery {
  orderBy?: ModelPortfolioComponentOrderByOption;
}

export interface PublicModelPortfolioQueryResponse extends BaseQueryResponse {
  data: PublicModelPortfolioResponse[];
}

export interface ModelPortfolioQueryResponse extends BaseQueryResponse {
  data: ModelPortfolioResponse[];
}

export interface ModelPortfolioComponentQueryResponse extends BaseQueryResponse {
  data: ModelPortfolioComponentResponse[];
}
