import { BaseQueryResponse } from './base.interface';
import { NotificationEvent, NotificationCadence } from '../enums/notification.enum';

export interface NotificationSubscriptionResponse {
  event: NotificationEvent;
  cadence: NotificationCadence;
}

export interface NotificationSubscriptionsRequest {
  subscriptions: NotificationSubscriptionResponse[];
}

export interface NotificationSubscriptionsBulkResponse extends BaseQueryResponse {
  subscriptions: NotificationSubscriptionResponse[];
}
