import { OrderByDirection } from '../enums/base.enum';
import { OrganizationOrderByOptions, OrganizationUserRole } from '../enums/organization.enum';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import {
  AdminCounterpartyCreateRequest,
  AdminCounterpartyResponse,
  AdminCounterpartyUpdateRequest,
} from './counterparty.interface';
import {
  AdminCustomerPortfolioCreateRequest,
  AdminCustomerPortfolioResponse,
  AdminCustomerPortfolioUpdateRequest,
} from './customer-portfolio.interface';

export interface AdminOrganizationCreateRequest {
  counterparty?: AdminCounterpartyCreateRequest;
  customerPortfolio?: AdminCustomerPortfolioCreateRequest;
  externalReference?: string;
  isEnabled: boolean;
  memo?: string;
  name: string;
}

export interface AdminOrganizationUpdateRequest {
  counterparty?: AdminCounterpartyUpdateRequest;
  customerPortfolio?: AdminCustomerPortfolioUpdateRequest;
  externalReference?: string;
  isEnabled?: boolean;
  memo?: string;
  name?: string;
}

export interface AdminOrganizationUserRequest {
  organizationUserRoles: OrganizationUserRole[];
}

export interface TrimmedOrganizationResponse extends BaseResponse {
  isEnabled: boolean;
  name: string;
}

export interface AdminOrganizationResponse extends TrimmedOrganizationResponse {
  counterparty?: AdminCounterpartyResponse;
  customerPortfolio?: AdminCustomerPortfolioResponse;
  externalReference?: string;
  memo?: string;
}

// portal organization response always returns the user's roles
export interface PortalUserOrganizationResponse extends TrimmedOrganizationResponse {
  customerPortfolio: AdminCustomerPortfolioResponse; // currently, portal/mobile user will always have customer portfolio
  organizationUserRoles: OrganizationUserRole[];
}

export interface AdminOrganizationQuery extends Omit<BaseQuery, 'orderBy'> {
  /*  filters */
  isCounterparty?: boolean;
  isCustomerPortfolio?: boolean;
  isEnabled?: boolean;
  name?: string;
  /* sorting */
  orderBys?: { orderBy: OrganizationOrderByOptions; orderByDirection: OrderByDirection }[] | string[]; // FE will pass in a string array that is formated field:direction
}

export interface AdminOrganizationQueryResponse extends BaseQueryResponse {
  data: AdminOrganizationResponse[];
}

export interface PortalUserOrganizationQueryResponse extends BaseQueryResponse {
  data: PortalUserOrganizationResponse[];
}
