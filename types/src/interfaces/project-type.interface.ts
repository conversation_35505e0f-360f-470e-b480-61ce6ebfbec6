import { ProjectTypeCategory, ProjectTypeOrderByOption, ProjectTypeRelations } from '../enums/project-type.enum';
import { BookTypeGroupedAllocationResponse, GroupedAllocationResponse } from './allocation.interface';
import { BaseQuery, BaseQueryResponse } from './base.interface';

export interface ProjectTypeCreateRequest {
  name: string;
  category: ProjectTypeCategory;
  type: string;
}

export interface ProjectTypeUpdateRequest {
  name?: string;
  category?: ProjectTypeCategory;
  type?: string;
}

export interface ProjectTypeRelationsQuery {
  includeRelations?: ProjectTypeRelations[];
}

export interface ProjectTypeQuery extends BaseQuery, ProjectTypeRelationsQuery {
  category?: ProjectTypeCategory;
  name?: string;
  type?: string;
  orderBy?: ProjectTypeOrderByOption;
}

export interface TrimmedProjectTypeResponse {
  category: ProjectTypeCategory;
  id: number;
  name: string;
  type: string;
}

export interface ProjectTypeResponse extends TrimmedProjectTypeResponse {
  assetAllocations?: GroupedAllocationResponse;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
}

export interface ProjectTypeQueryResponse extends BaseQueryResponse {
  data: ProjectTypeResponse[];
}
