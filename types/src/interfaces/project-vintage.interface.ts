import Decimal from 'decimal.js';
import {
  HistoricalBufferOrderByOption,
  ProjectVintageBufferType,
  ProjectVintageOrderByOption,
  ProjectVintageRelations,
} from '../enums/project-vintage.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { CreditInflowResponse } from './credit-flow.interface';
import { TrimmedProjectResponse } from './project.interface';
import { TrimmedUserResponse } from './user.interface';
import { DateRange } from './date-range.interface';
import { BookTypeGroupedAllocationResponse, GroupedAllocationResponse } from './allocation.interface';

export interface ProjectVintageRequest {
  name: string;
  interval: string | DateRange;
  projectId: uuid;
}

export interface ProjectVintageRiskBufferRequest {
  highBufferPercentage?: Decimal;
  lowBufferPercentage?: Decimal;
  notes: string;
  projectVintageId: uuid;
  riskBufferPercentage?: Decimal;
}

export interface TrimmedProjectVintageResponse extends BaseResponse {
  averageCostBasis?: Decimal;
  highBufferPercentage?: Decimal;
  interval?: string | DateRange;
  isRctEligible: boolean;
  lowBufferPercentage?: Decimal;
  riskBufferPercentage?: Decimal;
  name: string;
  price?: Decimal;
  project?: TrimmedProjectResponse;
}

export interface AdminProjectVintageResponse extends BaseResponse, TrimmedProjectVintageResponse {
  assetAllocations?: GroupedAllocationResponse;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
  inflows?: CreditInflowResponse[];
  // project?: AdminProjectResponse; // todo : if FE needs AdminProjectResponse, can bring it back
}

export interface PortalProjectVintageResponse extends BaseResponse, TrimmedProjectVintageResponse {
  // project: PortalProjectResponse;  // todo : if FE needs PortalProjectResponse, can bring it back
}

export interface MobileProjectVintageResponse extends BaseResponse, TrimmedProjectVintageResponse {
  // project: PortalProjectResponse;  // todo : if FE needs PortalProjectResponse, can bring it back
}

export interface HistoricalBufferResponse {
  id: uuid;
  timestamp: Date;
  newPercentage?: Decimal;
  notes?: string;
  oldPercentage?: Decimal;
  projectVintage: AdminProjectVintageResponse;
  type: ProjectVintageBufferType;
  user: TrimmedUserResponse;
}

export interface ProjectVintageRelationsQuery {
  includeRelations?: ProjectVintageRelations[];
}
export interface ProjectVintageQuery extends BaseQuery, ProjectVintageRelationsQuery {
  ids?: uuid[];
  projectIds?: uuid[];
  projectTypeIds?: number[];
  orderBy: ProjectVintageOrderByOption;
}

export interface AdminProjectVintageQueryResponse extends BaseQueryResponse {
  data: AdminProjectVintageResponse[];
}

export interface HistoricalBufferQuery extends BaseQuery {
  projectVintageId?: uuid;
  type?: ProjectVintageBufferType;
  userId?: uuid;
  orderBy: HistoricalBufferOrderByOption;
}

export interface HistoricalBufferQueryResponse extends BaseQueryResponse {
  data: HistoricalBufferResponse[];
}
