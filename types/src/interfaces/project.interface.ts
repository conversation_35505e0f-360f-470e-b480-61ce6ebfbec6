import { CountryRegions } from '../enums/country.enum';
import { PriceRange } from '../enums/pricing.enum';
import { ProjectTypeCategory } from '../enums/project-type.enum';
import {
  ProjectBeZeroRating,
  ProjectEligibilityAccreditation,
  ProjectEmissionsImpactType,
  ProjectOrderByOption,
  ProjectRelations,
  ProjectRiskScore,
} from '../enums/project.enum';
import { json } from '../types/json.type';
import { uuid } from '../types/uuid.type';
import { BookTypeGroupedAllocationResponse, GroupedAllocationResponse } from './allocation.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { BufferCategoryResponse } from './buffer-category.interface';
import { TrimmedCountryResponse } from './country.interface';
import { KmlResponse } from './kml.interface';
import { ProjectTypeResponse } from './project-type.interface';
import { AdminProjectVintageResponse, PortalProjectVintageResponse } from './project-vintage.interface';
import { RegistryResponse } from './registry.interface';
import { TrimmedUserResponse } from './user.interface';

export interface ProjectCreateRequest {
  additionalityBlurb?: string;
  additionalityScore?: number;
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  bezeroRating?: ProjectBeZeroRating;
  bezeroUpdatedDate?: Date;
  bookChartDisplayGroup?: string;
  bufferCategoryId?: uuid;
  carbonAccountingBlurb?: string;
  categoryRiskScore?: ProjectRiskScore;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  countryCode?: string;
  countryRegion?: string;
  dateOfLatestVerification?: Date;
  durabilityScore?: number;
  eligibilityAccreditations?: ProjectEligibilityAccreditation[];
  emissionsImpactType?: ProjectEmissionsImpactType;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  independentVerifierName?: string;
  integrityGradeScore?: number;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  isScienceTeamApproved?: boolean;
  kmlLatitude?: number;
  kmlLongitude?: number;
  kmlUrl?: string;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  maxPercentage?: number;
  maxQuantity?: number;
  methodologyBlurb?: string;
  minPercentage?: number;
  minQuantity?: number;
  name: string;
  otherCoBenefitsBlurb?: string;
  overallRiskScore?: ProjectRiskScore;
  pddReportLink?: string;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  projectDescription?: string;
  projectDeveloperName?: string;
  projectTypeId: number;
  rctStandard?: boolean;
  registryCreditsIssued?: number;
  registryCreditsRetired?: number;
  registryLink?: string;
  registryName?: string;
  registryProjectId: string;
  sdgIds?: number[];
  squareImage1Url?: string;
  squareImage2Url?: string;
  startDate?: Date;
  suspended?: boolean;
  vvbReportLink?: string;
  waterBlurb?: string;
}

export interface ProjectUpdateRequest {
  additionalityBlurb?: string;
  additionalityScore?: number;
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  bezeroRating?: ProjectBeZeroRating;
  bezeroUpdatedDate?: Date;
  bookChartDisplayGroup?: string;
  bufferCategoryId?: uuid;
  carbonAccountingBlurb?: string;
  categoryRiskScore?: ProjectRiskScore;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  countryCode?: string;
  countryRegion?: string;
  dateOfLatestVerification?: Date;
  durabilityScore?: number;
  eligibilityAccreditations?: ProjectEligibilityAccreditation[];
  emissionsImpactType?: ProjectEmissionsImpactType;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  independentVerifierName?: string;
  integrityGradeScore?: number;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  isScienceTeamApproved?: boolean;
  kmlLatitude?: number;
  kmlLongitude?: number;
  kmlUrl?: string;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  maxPercentage?: number;
  maxQuantity?: number;
  methodologyBlurb?: string;
  minPercentage?: number;
  minQuantity?: number;
  name?: string;
  otherCoBenefitsBlurb?: string;
  overallRiskScore?: ProjectRiskScore;
  pddReportLink?: string;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  projectDescription?: string;
  projectDeveloperName?: string;
  projectTypeId?: number;
  rctStandard?: boolean;
  registryCreditsIssued?: number;
  registryCreditsRetired?: number;
  registryLink?: string;
  registryName?: string;
  registryProjectId?: string;
  sdgIds?: number[];
  squareImage1Url?: string;
  squareImage2Url?: string;
  startDate?: Date;
  suspended?: boolean;
  vvbReportLink?: string;
  waterBlurb?: string;
}

export interface BulkProjectUpdateRequest extends ProjectUpdateRequest {
  id: uuid;
  memo: string;
}

export interface ProjectUpdateLogResponseDTO extends BaseResponse {
  id: uuid;
  data: Record<string, json>;
  user: TrimmedUserResponse;
  memo: string;
}

export interface SdgTypeResponse {
  id: number;
  title: string;
  iconImagePath: string;
}

export interface ProjectSdgResponse extends BaseResponse {
  projectId: uuid;
  sdgDescriptionBlurb?: string;
  sdgType: SdgTypeResponse;
  sdgTypeId: number;
}

export interface ProjectRelationsQuery {
  includeRelations?: ProjectRelations[];
}

export interface ProjectQuery extends BaseQuery, ProjectRelationsQuery {
  byoBufferEligible?: boolean;
  hasAmount?: boolean;
  hasTrades?: boolean;
  includeBYOMinimum?: boolean; // todo : deprecate and use includeRelations param
  ids?: uuid[];
  isByorctEligible?: boolean;
  projectTypeIds?: number[];
  orderBy?: ProjectOrderByOption;
}

export interface TrimmedProjectResponse {
  bufferCategory?: BufferCategoryResponse;
  country?: TrimmedCountryResponse;
  eligibilityAccreditations: ProjectEligibilityAccreditation[];
  emissionsImpactType?: ProjectEmissionsImpactType;
  hasBalance: boolean;
  id: uuid;
  integrityGradeScore?: number;
  isScienceTeamApproved: boolean;
  name: string;
  overallRiskScore?: ProjectRiskScore;
  projectDescription?: string;
  projectSDGs?: ProjectSdgResponse[];
  projectType: ProjectTypeResponse;
  rctStandard?: boolean;
  registry?: RegistryResponse;
  registryName?: string;
  registryProjectId: string;
  suspended?: boolean;
  productIds?: uuid[];
}

export interface AdminProjectResponse extends BaseResponse, TrimmedProjectResponse {
  additionalityBlurb?: string;
  additionalityScore?: number;
  assetAllocations?: GroupedAllocationResponse;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  bezeroRating?: ProjectBeZeroRating;
  bezeroUpdatedDate?: Date;
  bookChartDisplayGroup?: string;
  carbonAccountingBlurb?: string;
  categoryRiskScore?: ProjectRiskScore;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  countryRegion?: string;
  dateOfLatestVerification?: Date;
  durabilityScore?: number;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  independentVerifierName?: string;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  kml?: KmlResponse;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  maxPercentage?: number;
  maxQuantity?: number;
  methodologyBlurb?: string;
  minPercentage?: number;
  minQuantity?: number;
  otherCoBenefitsBlurb?: string;
  pddReportLink?: string;
  pdfReady?: boolean;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  priceRange?: PriceRange;
  projectDeveloperName?: string;
  projectVintages?: AdminProjectVintageResponse[];
  rctStandard: boolean;
  registryCreditsIssued?: number;
  registryCreditsRetired?: number;
  registryLink?: string;
  squareImage1Url?: string;
  squareImage2Url?: string;
  suspended: boolean;
  startDate?: Date;
  vvbReportLink?: string;
  waterBlurb?: string;
}

export interface PortalProjectResponse extends BaseResponse, Omit<TrimmedProjectResponse, 'bufferCategory'> {
  additionalityBlurb?: string;
  additionalityScore?: number;
  analystInsightsBlurb?: string;
  analystName?: string;
  analystRole?: string;
  bezeroRating?: ProjectBeZeroRating;
  bezeroUpdatedDate?: Date;
  carbonAccountingBlurb?: string;
  certificationBlurb?: string;
  certificationScore?: number;
  climateImpact?: number;
  climateImpactBlurb?: string;
  climateImpactRiskAdjusted?: number;
  durabilityScore?: number;
  endDate?: Date;
  futureDeliveryBlurb?: string;
  futureDeliveryRisk?: number;
  illustrationImageUrl?: string;
  integrityGradeScoreRiskAdjusted?: number;
  isByorctApproved?: boolean;
  kml?: KmlResponse;
  lastReviewDate?: Date;
  mapImageUrl?: string;
  methodologyBlurb?: string;
  minQuantityBYO?: number;
  otherCoBenefitsBlurb?: string;
  pdfReady?: boolean;
  permanenceBlurb?: string;
  previewImageUrl?: string;
  priceRange?: PriceRange;
  projectDeveloperName?: string;
  projectVintages?: PortalProjectVintageResponse[];
  proportionOfBook?: number;
  registryLink?: string;
  squareImage1Url?: string;
  squareImage2Url?: string;
  startDate?: Date;
  waterBlurb?: string;
}

export type MobileProjectResponse = PortalProjectResponse;

export interface AdminProjectQueryResponse extends BaseQueryResponse {
  data: AdminProjectResponse[];
}

export interface PortalProjectQueryResponse extends BaseQueryResponse {
  data: PortalProjectResponse[];
}

export interface MobileProjectQueryResponse extends BaseQueryResponse {
  data: MobileProjectResponse[];
}

export interface ProjectSearch {
  q: string;
  name?: boolean;
  id?: boolean;
  fuzzy?: boolean;
  hasBalance?: boolean;
  limit: number;
  eligibilityAccredidations?: ProjectEligibilityAccreditation[];
  emissionsImpactTypes?: ProjectEmissionsImpactType[];
  integrityGrades?: string[];
  projectTypeCategories?: ProjectTypeCategory[];
  regions?: CountryRegions[];
}

export interface ProjectSearchResponse {
  id: uuid;
  country?: TrimmedCountryResponse;
  isScienceTeamApproved: boolean;
  name: string;
  projectType?: ProjectTypeResponse;
  bufferCategory?: BufferCategoryResponse;
  rctStandard?: boolean;
  registryProjectId: string;
  suspended?: boolean;
  integrityGradeScore?: number;
}
