import { AssetType } from '../enums/asset.enum';
import { OrderByDirection } from '../enums/base.enum';
import {
  PurchaseFlowType,
  PurchaseUpdatableStatus,
  PurchaseStatus,
  PurchaseRelations,
  PurchaseOrderByOptions,
} from '../enums/transaction.enum';
import { uuid } from '../types/uuid.type';
import { AssetPurchaseRequest, RctAssetResponse, VintageAssetResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedOrganizationResponse } from './organization.interface';

export interface PurchaseOrderBy {
  orderBy: PurchaseOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface AdminPurchaseRequest {
  assets: AssetPurchaseRequest[];
  assetType: AssetType;
  organizationId: uuid;
  flowType: PurchaseFlowType;
  memo?: string;
  needsRiskAdjustment: boolean;
  paymentDueDate?: Date;
}

export interface AdminPurchaseExecuteRequest {
  updatableStatusOrder: PurchaseUpdatableStatus[];
}

export interface AdminPurchaseDeliverRequest {
  assetsDeliveredAt: Date;
}

export interface PurchaseResponse extends BaseResponse {
  amount: number;
  assets: (RctAssetResponse | VintageAssetResponse)[];
  assetType: AssetType;
  organization: TrimmedOrganizationResponse; // todo : @kofi/@adir to tell me if you need customerPortfolio specific details
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  flowType: PurchaseFlowType;
  isDelivered: boolean;
  isPaid: boolean;
  memo?: string;
  needsRiskAdjustment?: boolean;
  paymentDueDate?: Date;
  status: PurchaseStatus;
  uiKey: string;
  updatableStatusOrder: PurchaseUpdatableStatus[];
}

export interface PurchaseRelationsQuery {
  includeRelations?: PurchaseRelations[];
}

export interface AdminPurchaseQuery extends PurchaseRelationsQuery, BaseQuery {
  /* base */
  orderBys?: PurchaseOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  assetId?: uuid;
  organizationId?: uuid;
  statuses?: PurchaseStatus[];
}

export interface AdminPurchaseQueryResponse extends BaseQueryResponse {
  data: PurchaseResponse[];
}
