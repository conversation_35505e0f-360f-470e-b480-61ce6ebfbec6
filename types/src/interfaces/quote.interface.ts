import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { QuoteOrderByOption } from '../enums/quote.enum';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';

export interface QuoteCreateRequest {
  amount?: number;
  expirationDate?: Date;
  from?: string;
  price?: Decimal;
  projectName: string;
  registryName?: string;
  registryProjectId: string;
  vintageInterval?: string;
}

export interface QuoteUpdateRequest {
  amount?: number;
  expirationDate?: Date;
  from?: string;
  price?: Decimal;
  projectName?: string;
  registryName?: string;
  registryProjectId?: string;
  vintageInterval?: string;
}

export interface QuoteQuery extends BaseQuery {
  updatedById?: uuid;
  orderBy?: QuoteOrderByOption;
}

export interface QuoteResponse extends BaseResponse {
  amount?: number;
  expirationDate?: Date;
  from?: string;
  price?: Decimal;
  projectName: string;
  registryName?: string;
  registryProjectId: string;
  updatedBy: TrimmedUserResponse;
  vintageInterval?: string;
}

export interface QuoteQueryResponse extends BaseQueryResponse {
  data: QuoteResponse[];
}
