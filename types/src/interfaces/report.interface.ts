export interface MarketIntelligenceReportClassification {
  reason: string;
  category: string;
}

export interface MarketIntelligenceReportSummary {
  summary: string;
  sentiment: string;
  sentiment_reason: string;
}

export interface MarketIntelligenceReportAnalysis {
  summaries: MarketIntelligenceReportSummary[];
  rank_score: number;
  keyword_tags: string[];
  classifications: MarketIntelligenceReportClassification[];
  sentiment_score: number;
  semantic_commentary: string;
}

export interface MarketIntelligenceReportFullSource {
  id: string;
  url: string;
  error: string;
  title: string;
  analysis: MarketIntelligenceReportAnalysis;
  analyzed_at: string;
  created_at: string;
  updated_at: string;
  rank_score: number;
  from_search: string;
  source_label: string;
}

export type MarketIntelligenceReportLiteSource = Pick<
  MarketIntelligenceReportFullSource,
  'id' | 'url' | 'title' | 'from_search' | 'source_label'
>;

export interface MarketIntelligenceReportCategorySource {
  category_id: number;
  sources_top: MarketIntelligenceReportFullSource[];
  category_name: 'Market News' | 'Project News';
  sources_remainder: MarketIntelligenceReportLiteSource[];
}

export interface MarketIntelligenceReportDocument {
  errors: string[];
  date_format: string;
  generated_at: string;
  category_sources: MarketIntelligenceReportCategorySource[];
}

export interface MarketIntelligenceReportResponse {
  id: number;
  date: string;
  updated_at: string;
  doc: MarketIntelligenceReportDocument;
}
