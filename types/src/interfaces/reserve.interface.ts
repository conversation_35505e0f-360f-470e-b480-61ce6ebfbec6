import { ReserveOrderByOption, ReserveRelation } from '../enums/reserve.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';
import { TrimmedUserResponse } from './user.interface';

export interface ReserveCreateRequest {
  amount: number;
  memo?: string;
  organizationId?: uuid;
  projectVintageId: uuid;
}

export interface ReserveUpdateRequest {
  amount?: number;
  memo?: string;
}

export interface ReserveResponse extends BaseResponse {
  amount: number;
  docsCount: number;
  memo?: string;
  organization?: TrimmedOrganizationResponse;
  projectVintage?: TrimmedProjectVintageResponse;
  updatedBy?: TrimmedUserResponse;
}

export interface ReserveQuery extends BaseQuery, ReserveRelationsQuery {
  memo?: string;
  organizationId?: uuid;
  projectVintageId?: uuid;
  updatedById?: uuid;
  orderBy?: ReserveOrderByOption;
}

export interface ReserveRelationsQuery {
  includeRelations?: ReserveRelation[];
}

export interface ReserveQueryResponse extends BaseQueryResponse {
  data: ReserveResponse[];
}
