import { AssetType } from '../enums/asset.enum';
import {
  RetirementRelations,
  RetirementType,
  RetirementStatus,
  RetirementOrderByOptions,
} from '../enums/transaction.enum';
import { uuid } from '../types/uuid.type';
import { AssetRetirementRequest, RctAssetResponse, VintageAssetResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { OrderByDirection } from '../enums/base.enum';
import { TrimmedOrganizationResponse } from './organization.interface';
import { TrimmedProjectVintageResponse } from './project-vintage.interface';

export interface RetirementOrderBy {
  orderBy: RetirementOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface RetirementRequest {
  assets: AssetRetirementRequest[];
  assetType: AssetType;
  beneficiary?: string;
  isPublic: boolean;
  memo?: string;
  token?: string;
  registryAccount?: string;
  type: RetirementType.RETIREMENT | RetirementType.TRANSFER_OUTFLOW;
}

export interface AdminRetirementRequest extends RetirementRequest {
  organizationId: uuid;
}

export interface SuggestedBufferRetirementRequest {
  retirementIds: uuid[];
}

export interface SuggestedBufferElement {
  projectVintage: TrimmedProjectVintageResponse;
  amount: number;
}

export interface SuggestedBufferComposition {
  category: string;
  suggestedBufferRetirements: SuggestedBufferElement[];
}

export interface SuggestedBufferRetirementResponse {
  suggestedAllocations: SuggestedBufferComposition[];
}

export interface UpdateRetirementAmountsRequest {
  projectVintageId: uuid;
  amountTransacted: number;
  sourceId: uuid;
}

export interface RetirementResponse extends BaseResponse {
  amount: number;
  assets: (RctAssetResponse | VintageAssetResponse)[];
  assetType: AssetType;
  beneficiary?: string;
  organization: TrimmedOrganizationResponse; // todo : @kofi/@adir to tell me if you need customerPortfolio specific details
  dateFinished?: Date;
  dateStarted: Date;
  docsCount: number;
  isPublic: boolean;
  memo?: string;
  registryAccount?: string;
  requestedBy?: TrimmedUserResponse;
  status: RetirementStatus;
  type: RetirementType;
  uiKey: string;
}

export interface RetirementRelationsQuery {
  includeRelations?: RetirementRelations[];
}

export interface AdminRetirementQuery extends RetirementRelationsQuery, BaseQuery {
  /* base */
  orderBys?: RetirementOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  assetId?: uuid;
  organizationId?: uuid;
  statuses?: RetirementStatus[];
  types?: RetirementType[];
}

export interface AdminRetirementQueryResponse extends BaseQueryResponse {
  data: RetirementResponse[];
}
