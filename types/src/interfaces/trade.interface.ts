import Decimal from 'decimal.js';
import { uuid } from '../types/uuid.type';
import { TradeOrderByOptions, TradeRelation, TradeStatus, TradeType, TradeUpdatableStatus } from '../enums/trade.enum';
import { CounterpartyRole } from '../enums/counterparty-role.enum';
import { VintageAssetRequest, VintageAssetResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { AdminBookResponse } from './book.interface';
import { TrimmedUserResponse } from './user.interface';
import { AdminOrganizationResponse } from './organization.interface';
import { OrderByDirection } from '../enums/base.enum';

export interface TradeCounterpartyRequest {
  actualServiceFee: Decimal;
  calculatedServiceFee?: Decimal;
  comments?: string;
  organizationId: uuid;
  defaultFeeId?: uuid; // the fee id used to calculate calculatedServiceFee
  isPrimary: boolean;
  role: CounterpartyRole;
}

export interface TradeCounterpartyResponse {
  actualServiceFee?: Decimal; // only optional because of backfilling
  calculatedServiceFee?: Decimal;
  comments?: string;
  isPrimary: boolean;
  organization: AdminOrganizationResponse;
  role: CounterpartyRole;
}

export interface TradeCreateRequest {
  bookId: uuid;
  counterparties: TradeCounterpartyRequest[];
  goodUntilDate?: Date;
  memo?: string;
  otherFee?: Decimal;
  poid: string;
  projectVintages: VintageAssetRequest[];
  rawPrice: Decimal;
  type: TradeType;
}

export interface TradeUpdateRequest {
  counterparties?: TradeCounterpartyRequest[];
  memo?: string;
  otherFee?: Decimal;
  rawPrice?: Decimal;
}

export interface TradeConfirmationRequest {
  recipientEmails: string[];
  paymentTerms?: string;
  deliveryTerms?: string;
}

export interface TradeConfirmationProductsResponse {
  productId: uuid;
  registryProjectId: string;
  projectName: string;
  vintageName: string;
  quantity: string;
  pricePerTonne: string;
  totalPrice: string;
}

export interface TradeConfirmationResponse extends BaseResponse {
  tradeId: uuid;
  uiKey: string;
  recipientEmails: string[];
  formattedDate: string;
  seller: string;
  buyer: string;
  products: TradeConfirmationProductsResponse[];
  totalQuantity: string;
  totalPrice: string;
  paymentTerms?: string;
  deliveryTerms?: string;
}

export interface TradeExecuteRequest {
  updatableStatusOrder: TradeUpdatableStatus[];
}

export interface TradeDeliverRequest {
  assetsDeliveredAt: Date;
}

export interface TradeResponse extends BaseResponse {
  amount: number;
  assetsDeliveredAt?: Date;
  book?: AdminBookResponse;
  counterparties: TradeCounterpartyResponse[];
  docsCount: number;
  goodUntilDate?: Date;
  isDelivered: boolean;
  isPaid: boolean;
  memo?: string;
  poid?: string;
  projectVintages?: VintageAssetResponse[];
  settledAt?: Date;
  status: TradeStatus;
  type: TradeType;
  uiKey: string;
  updatedBy?: TrimmedUserResponse;
  updatableStatusOrder: TradeUpdatableStatus[];
}

export interface TrimmedTradeResponse extends BaseResponse {
  amount: number;
  status: TradeStatus;
  type: TradeType;
  uiKey: string;
}

export interface TradeRelationsQuery {
  includeRelations?: TradeRelation[];
}

export interface TradeOrderBy {
  orderBy: TradeOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface TradeQuery extends BaseQuery, TradeRelationsQuery {
  /* base */
  orderBys?: TradeOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  bookId?: uuid;
  organizationIds?: uuid[];
  poid?: string;
  projectVintageId?: uuid;
  status?: TradeStatus;
  type?: TradeType;
  updatedById?: uuid;
}

export interface TradeQueryResponse extends BaseQueryResponse {
  data: TradeResponse[];
}
