import Decimal from 'decimal.js';
import {
  TransactionOrderByOptions,
  TransactionStatus,
  TransactionSubtype,
  TransactionType,
} from '../enums/transaction.enum';
import { OrderByDirection } from '../enums/base.enum';
import { uuid } from '../types/uuid.type';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedAssetFlowResponse } from './flow.interface';

export interface TransactionOrderBy {
  orderBy: TransactionOrderByOptions;
  orderByDirection: OrderByDirection;
}

export interface TrimmedTransactionResponse extends BaseResponse {
  counterpartyName?: string;
  counterpartyId?: uuid;
  docsCount: number;
  parentId?: uuid; // forward or marketing agreement id for the given line item
  settledAt?: Date;
  status: TransactionStatus;
  subtype?: TransactionSubtype;
  totalPrice: Decimal;
  totalQuantity: number;
  type: TransactionType;
  uiKey: string;
}

export interface TransactionResponse extends TrimmedTransactionResponse {
  assetFlows: TrimmedAssetFlowResponse[];
}

export interface TransactionQuery extends BaseQuery {
  /* base */
  orderBys?: TransactionOrderBy[] | string[]; // FE will pass in a string array that is formated field:direction
  /* filters */
  assetIds?: uuid[];
  assetName?: string;
  ids?: uuid[];
  counterparty?: string;
  statuses?: TransactionStatus[];
  subtypes?: TransactionSubtype[];
  types?: TransactionType[];
  uiKey?: string;
}

export interface TransactionSearchQuery {
  q: string; // string to search against
  fuzzy?: boolean; // fuzzy search on counterparty and/or asset?
  asset?: boolean; // include assets in search query?
  counterparty?: boolean; // include counterparty in search query?
  uiKey?: boolean; // include uiKey in search query?
  transactionStatuses?: TransactionStatus[];
  transactionTypes?: TransactionType[];
  limit: number;
}

export interface TransactionQueryResponse extends BaseQueryResponse {
  data: TransactionResponse[];
}
