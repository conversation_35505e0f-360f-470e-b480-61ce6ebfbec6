import { uuid } from '../types/uuid.type';
import { AssetTransferRequest, AssetTransferResponse } from './asset.interface';
import { BaseQuery, BaseQueryResponse, BaseResponse } from './base.interface';
import { TrimmedUserResponse } from './user.interface';
import { TransferOrderByOption, TransferRelation } from '../enums/transfer.enum';

export interface TransferRequest {
  assetTransfers: AssetTransferRequest[];
  memo?: string;
}

export interface TransferResponse extends BaseResponse {
  assetTransfers?: AssetTransferResponse[];
  docsCount: number;
  memo?: string;
  totalAmount: number;
  user: TrimmedUserResponse;
}

export interface TrimmedTransferResponse extends BaseResponse {
  totalAmount: number;
}

export interface TransferRelationsQuery {
  includeRelations?: TransferRelation[]; // currently does not work
}

export interface TransferQuery extends TransferRelationsQuery, BaseQuery {
  assetId?: uuid;
  destinationId?: uuid;
  ownerId?: uuid; // maybe bookId is a beter name?
  sourceId?: uuid;
  userId?: uuid;
  orderBy?: TransferOrderByOption;
}

export interface TransferQueryResponse extends BaseQueryResponse {
  data: TransferResponse[];
}
