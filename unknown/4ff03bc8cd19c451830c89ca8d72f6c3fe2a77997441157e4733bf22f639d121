

.Container {
    margin-top: 10px;
    
    .ActionButton {
        border-radius: 5px;
    }
}

.Dialog {
    width: 100%;
    max-width: 800px;
}

.DialogInnerContent {
    padding: 10px !important;
}

.DialogAction {
    justify-content: flex-start;

    div {
        width: 100%;
        justify-content: space-between;
    }

    .NegativeAction {
        color: #C90005 !important;
    }
}

.InterruptTitle {
    background-color: #C90005 !important;

    * {
        color: white;
    }
}

.InterruptPositiveAction {
    background-color: #C90005 !important;
}