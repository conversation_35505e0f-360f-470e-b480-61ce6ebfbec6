.container {
    background-color: rgba(238, 238, 238, 1);
    padding: 10px 5px 5px 10px;
    border-radius: 4px;
}

.title {
    margin-top: 15px;
    padding-right: 10px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.15000000596046448px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(0, 0, 0, 1);
}

.anchorButton {
    width: 150px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    line-height: 22px !important;
    text-transform: none !important;
    padding: 18px, 10px, 4px, 10px !important;
    margin-top: 10px !important;
    margin-left: 30px !important;
    border-radius: 5px !important;
    color: rgba(0, 0, 0, 1);
}

.buttonPrimary {
    background-color: rgba(22, 122, 143, 1) !important;
    color: rgba(255, 255, 255, 1) !important;
    font-size: 13px;
    font-weight: 500;
    line-height: 22px;
    letter-spacing: 0px;
    text-align: left;
    padding: 4px, 10px, 4px, 10px;
    border-radius: 5px !important;
    border: 1px solid rgba(0, 0, 0, 0.23) !important;
    text-transform: capitalize !important;
    width: 80px;
}

.chip {
    border: 2px solid rgba(22, 122, 143, 1) !important;
    height: 25;
    margin-top: 8px !important;
}

.filterLabel {
    font-size: 14px !important;
    font-weight: 500 !important;
    line-height: 22px;
    letter-spacing: 0.10000000149011612px;
    text-align: center;
    padding-left: 30px;
    display: flex;
}

.activeFilterContainer {
    background-color: rgba(238, 238, 238, 1);
    padding: 12px 0px 12px 0px;
}