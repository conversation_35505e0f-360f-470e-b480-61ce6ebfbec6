import "server-only";

import { DocumentQuery, PortalDocumentQueryResponse } from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { ssrFetch } from "../data-server";
import { generateQueryParams, Params } from "@app/utilities/fetch";

export const getDocuments = async (query?: DocumentQuery): Promise<MaybeNothing<PortalDocumentQueryResponse>> =>
  ssrFetch(`documents${!!query ? `?${generateQueryParams(query as Params)}` : ""}`);
