"use client";

import PageContentHeading from "../page-components/heading";
import Grid from "@mui/material/Grid";
import { DataTable } from "@app/components/data-table";
import {
  GridColDef,
  GridSortItem,
  GridSortModel,
  GridValueFormatterParams,
  NoRowsOverlayPropsOverrides,
} from "@mui/x-data-grid";
import { use, useState } from "react";
import { utcDateFormat } from "@app/utilities/date";
import { DocumentType, Maybe, PortalDocumentQueryResponse } from "@rubiconcarbon/shared-types";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { DocumentTypeUILabel } from "@app/constants/documents";
import { BLANK } from "@app/constants/common";
import { Attachment } from "@app/components/attachment-list/attachment-list";
import ContentContainer from "../components/content-container/content-container";
import HeadingContainer from "../components/heading-container/heading-container";

type ContentProps = {
  documents: Maybe<PortalDocumentQueryResponse>;
};

type DocumentsProps = {
  documents: Promise<Maybe<PortalDocumentQueryResponse>>;
};

const columns: GridColDef[] = [
  {
    field: "type",
    headerName: "Type",
    type: "singleSelect",
    valueOptions: [
      { value: DocumentType.RECEIPT, label: DocumentTypeUILabel[DocumentType.RECEIPT] },
      { value: DocumentType.INVOICE, label: DocumentTypeUILabel[DocumentType.INVOICE] },
      { value: DocumentType.MASTER_AGREEMENT, label: DocumentTypeUILabel[DocumentType.MASTER_AGREEMENT] },
      { value: DocumentType.PURCHASE_AGREEMENT, label: DocumentTypeUILabel[DocumentType.PURCHASE_AGREEMENT] },
      { value: DocumentType.ADDENDUM, label: DocumentTypeUILabel[DocumentType.ADDENDUM] },
      { value: DocumentType.RETIREMENT_CERTIFICATE, label: DocumentTypeUILabel[DocumentType.RETIREMENT_CERTIFICATE] },
      { value: DocumentType.PURCHASE_ORDER, label: DocumentTypeUILabel[DocumentType.PURCHASE_ORDER] },
      { value: DocumentType.CONTRACT, label: DocumentTypeUILabel[DocumentType.CONTRACT] },
      { value: DocumentType.PROOF_OF_CONFIRMATION, label: DocumentTypeUILabel[DocumentType.PROOF_OF_CONFIRMATION] },
      { value: DocumentType.PROOF_OF_DELIVERY, label: DocumentTypeUILabel[DocumentType.PROOF_OF_DELIVERY] },
      { value: DocumentType.OTHER, label: DocumentTypeUILabel[DocumentType.OTHER] },
    ],
    width: 200,
  },
  {
    field: "uploadedAt",
    headerName: "Upload date",
    type: "dateTime",
    valueFormatter: (params: GridValueFormatterParams<string>) => utcDateFormat(params.value, { defaultValue: BLANK }),
    width: 150,
  },
  {
    field: "Link",
    headerName: "",
    hideable: false,
    renderHeader: () => null,
    renderCell: ({ row }) => <Attachment document={{ ...row, renderLabel: false }} />,
    width: 300,
    sortable: false,
    filterable: false,
  },
];

const defaultSortModel: GridSortItem = { field: "uploadedAt", sort: "desc" };

const Content = ({ documents }: ContentProps): JSX.Element => {
  const [sortModel, setSortModel] = useState<GridSortModel>([defaultSortModel]);

  const { data: rows = [] } = documents ?? {};

  const handleSort = (model: GridSortModel): void => setSortModel(model);

  return (
    <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", ">*": { pt: "10px" } }}>
      <DataTable
        dataGridProps={{
          "aria-label": "Organization documents data grid",
          columns,
          rows,
          components: {
            NoRowsOverlay,
          },
          slotProps: {
            noRowsOverlay: {
              message: "There are no documents to show.",
            } as NoRowsOverlayPropsOverrides,
          },
          sortModel,
          onSortModelChange: handleSort,
        }}
      />
    </Grid>
  );
};

export default function DocumentCenterPage({ documents: documentsPromise }: DocumentsProps): JSX.Element {
  const documents = use(documentsPromise);

  return (
    <>
      <HeadingContainer>
        <PageContentHeading headingId="document-center-page-heading" headingText="Document center" />
      </HeadingContainer>
      <ContentContainer>
        <Content documents={documents} />
      </ContentContainer>
    </>
  );
}
