"use client";

import { use<PERSON><PERSON>back, use<PERSON>ontext, useMemo, useState } from "react";
import {
  PortalProjectQueryResponse,
  PortalProjectResponse,
  PortalProjectVintageResponse,
  ProjectRelations,
  Maybe as MaybeType,
} from "@rubiconcarbon/shared-types";
import { Box, Button, Stack, Typography } from "@mui/material";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { useLogger } from "@app/providers/logging-provider";
import ProjectsTable from "../projects/components/projects-table/projects-table";
import CircularProgress from "@mui/material/CircularProgress";
import AddIcon from "@mui/icons-material/Add";
import { FavoritesContext } from "@app/providers/favorites-provider";
import { BYO_SELECTED_PROJECTS_STATE_KEY } from "@app/constants/byo";
import { StoreContext } from "@app/providers/state/store";
import { RCTProject, RCTVintage } from "../products/byo-rct/custom-rct/model";
import { isEmpty } from "lodash";
import { useRouter } from "next/navigation";
import { BYO_PAGE } from "./model";
import { useMount } from "react-use";

import classes from "./styles.module.scss";

const sortVintages = (
  vintages: MaybeType<PortalProjectVintageResponse[]>,
): MaybeType<PortalProjectVintageResponse[]> => {
  return vintages?.sort((a, b) => (a.name > b.name ? 1 : -1));
};

const getDefaultSelectedVintage = (
  availableVintages: MaybeType<PortalProjectVintageResponse[]>,
): MaybeType<RCTVintage[]> => {
  if (!isEmpty(availableVintages)) {
    return availableVintages
      ?.map((v) => {
        return {
          createdAt: v?.createdAt,
          updatedAt: v?.updatedAt,
          id: v?.id,
          interval: v?.interval,
          name: v?.name,
          selected: true,
        };
      })
      .sort();
  }
  return [];
};

export default function Favorites(): JSX.Element {
  const [favoriteProjects, setFavoriteProjects] = useState<PortalProjectResponse[]>();
  const { currentFavoritesCount } = useContext(FavoritesContext);
  const { updatePersistentState } = useContext(StoreContext);
  const { push } = useRouter();

  const { logger } = useLogger();

  const { trigger: getFavoriteProjects, isMutating: loadingProjects } = useTriggerRequest<PortalProjectQueryResponse>({
    url: "users/projects",
    queryParams: {
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeRelations: [
        ProjectRelations.PRICE_RANGE,
        ProjectRelations.SDGS,
        ProjectRelations.COUNTRY,
        ProjectRelations.PROJECT_TYPE,
        ProjectRelations.PRODUCTS,
        ProjectRelations.PROJECT_VINTAGES,
      ],
    },
    swrOptions: {
      onSuccess: (data: PortalProjectQueryResponse): void => {
        setFavoriteProjects(data?.data);
      },
      onError: (error: any): void => {
        logger.error(`Error fetching user favorite projects ids.${error?.message} `, {});
      },
    },
  });

  useMount(() => {
    getFavoriteProjects();
  });

  const createPortfolioHandler = useCallback(() => {
    let projects: RCTProject[] = [];
    if (!!favoriteProjects) {
      projects = favoriteProjects?.map((p) => {
        return {
          ...p,
          projectVintages: getDefaultSelectedVintage(sortVintages(p?.projectVintages)) ?? [],
          preselected: true,
        };
      });

      push(BYO_PAGE);
    }

    updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, projects);
  }, [favoriteProjects, updatePersistentState, push]);

  const allowCreatePortfolio = useMemo(
    () => currentFavoritesCount > 4 && currentFavoritesCount < 13,
    [currentFavoritesCount],
  );

  return (
    <>
      <Stack className={classes.favoritesPageContainer}>
        <Stack direction="row" className={classes.header} sx={{ justifyContent: "space-between" }}>
          <Typography component="h6" variant="body2" className={classes.title}>
            Favorites
          </Typography>
          <Box sx={{ marginTop: "5px" }}>
            <Button
              className={allowCreatePortfolio ? classes.actionButton : classes.actionButtonDisabled}
              variant="contained"
              startIcon={<AddIcon />}
              onClick={createPortfolioHandler}
              disabled={!allowCreatePortfolio}
            >
              Create Portfolio
            </Button>
          </Box>
        </Stack>
        <Box className={classes.container}>
          <Box className={classes.tableContainer}>
            {loadingProjects ? (
              <Box sx={{ textAlign: "center" }}>
                <CircularProgress />
              </Box>
            ) : (
              <ProjectsTable projectsResponse={favoriteProjects} refreshFavorites={getFavoriteProjects} />
            )}
          </Box>
        </Box>
      </Stack>
    </>
  );
}
