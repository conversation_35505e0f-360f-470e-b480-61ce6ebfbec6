import {
  AssetType,
  MarketNewsQueryResponse,
  RetirementStatus,
  TransactionResponse,
  TransactionType,
  uuid,
} from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";

export const marketNewsResponseMock: MarketNewsQueryResponse = {
  page: {
    limit: 50,
    offset: 0,
    size: 50,
  },
  data: [
    {
      id: uuid("a5006fe1-6eb7-4926-859b-786805def94e"),
      createdAt: new Date("2025-01-24T17:19:07.113Z"),
      updatedAt: new Date("2025-01-24T17:19:07.113Z"),
      articleDate: new Date("2025-01-24T00:00:00.000Z"),
      header: "Reforestation stands out among carbon projects as most beneficial for wildlife biodiversity",
      source: "Carbon Herald",
      hitword: "voluntary carbon market",
      isIrrelevant: false,
      summary:
        "According to a study by the New York Botanical Garden, reforestation is the most beneficial climate mitigation strategy for biodiversity. Researchers modeled the local and global impacts of different climate mitigation strategies on over 14,000 animal species. Reforestation had the greatest benefits by increasing habitat and mitigating climate change, while afforestation and bioenergy cropping carried the greatest risks of negative impacts.",
      url: "",
      scopes: [],
      projects: [],
    },
  ],
};

export const transactionsMock: TransactionResponse[] = [
  {
    id: uuid("98561f98-f474-4aa7-940b-98bf71bb3e65"),
    createdAt: new Date("2024-08-13T14:37:32.048Z"),
    updatedAt: new Date("2024-08-13T14:38:43.366Z"),
    totalPrice: new Decimal(4500),
    totalQuantity: 50,
    docsCount: 1,
    settledAt: new Date("2024-08-13T14:38:13.847Z"),
    status: RetirementStatus.COMPLETED,
    type: TransactionType.PURCHASE,
    uiKey: "PR-240813-1JDP1",
    assetFlows: [
      {
        id: uuid("d7bab952-6233-4907-8c08-0232b0682ee3"),
        createdAt: new Date("2024-08-13T14:37:32.048Z"),
        updatedAt: new Date("2024-08-13T14:38:13.881Z"),
        amount: 50,
        asset: {
          id: uuid("b2e58c9a-fa0f-4042-b011-17b9e1e771d9"),
          createdAt: new Date("2024-06-24T22:47:54.000Z"),
          updatedAt: new Date("2024-06-24T22:47:54.000Z"),
          name: "Product 1",
          type: AssetType.RCT,
        },
        status: "settled",
      },
    ],
  },
  {
    id: uuid("67ed073b-9fe2-46e3-98a5-a141228c0327"),
    createdAt: new Date("2024-08-13T14:38:43.301Z"),
    updatedAt: new Date("2024-08-28T20:56:11.520Z"),
    totalPrice: new Decimal(0),
    totalQuantity: 50,
    docsCount: 8,
    settledAt: new Date("2024-08-13T14:39:25.815Z"),
    status: RetirementStatus.COMPLETED,
    type: TransactionType.PURCHASE,
    uiKey: "RR-240813-8Q3PQ",
    assetFlows: [
      {
        id: uuid("423375d2-1eb9-4a55-86b5-10833abff71b"),
        createdAt: new Date("2024-08-13T14:38:43.301Z"),
        updatedAt: new Date("2024-08-13T14:39:25.871Z"),
        amount: 50,
        asset: {
          id: uuid("b2e58c9a-fa0f-4042-b011-17b9e1e771d9"),
          createdAt: new Date("2024-06-24T22:47:54.000Z"),
          updatedAt: new Date("2024-06-24T22:47:54.000Z"),
          name: "Product 2",
          type: AssetType.RCT,
        },
        status: "pending",
      },
    ],
  },
];
