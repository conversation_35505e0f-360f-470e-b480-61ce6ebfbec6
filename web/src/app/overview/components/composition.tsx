import { useMemo, useState } from "react";
import { DataTable } from "@app/components/data-table";
import { Box, Stack, Typography } from "@mui/material";
import {
  GridColDef,
  GridRenderCellParams,
  GridSortItem,
  GridSortModel,
  GridValueFormatterParams,
  GridValueGetterParams,
} from "@mui/x-data-grid";
import { BLANK } from "@app/constants/common";
import { percentageFormat, numberFormat, Maybe, classcat } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";
import type { Composition } from "../types/holdings";
import BuyButton from "@app/components/buy-button/buy-button";
import { useMeasure } from "react-use";

import classes from "../styles/composition.module.scss";
import tableClasses from "../styles/table.module.scss";

type CompositionProps = {
  compositions: Composition[];
};

const defaultSortModel: GridSortItem = { field: "quantity", sort: "desc" };

const columns: GridColDef[] = [
  {
    field: "product",
    headerName: "Product",
    valueGetter: ({ row }: GridValueGetterParams<Composition>): string => {
      const { assetName, assetDetailName, assetDetailRegistryProjectId, type } = row || {};
      return `${assetName}${type === "Project" ? `, ${assetDetailRegistryProjectId}, ${assetDetailName}` : ""}`;
    },
    renderCell: ({ row }: GridRenderCellParams<Composition>): JSX.Element => {
      const { id, assetDetailId, assetName, assetDetailName, assetDetailRegistryProjectId, type } = row || {};

      const canRender = !!assetName && (!!id || !!assetDetailId);

      return (
        <>
          <Maybe condition={canRender}>
            <Stack width="100%">
              <Link
                className={classes.Link}
                href={`/${type === "Project" ? "projects" : "products"}/${!!assetDetailId ? assetDetailId : id}`}
                target="_blank"
              >
                <Typography>{assetName}</Typography>
              </Link>
              <Maybe condition={type === "Project"}>
                <Typography className={classes.SubLabel} variant="body2">
                  {assetDetailRegistryProjectId} - {assetDetailName}
                </Typography>
              </Maybe>
            </Stack>
          </Maybe>
          <Maybe condition={!canRender}>{BLANK}</Maybe>
        </>
      );
    },
    width: 350,
  },
  {
    field: "type",
    headerName: "Type",
    valueFormatter: (params: GridValueFormatterParams<string>) => params.value || BLANK,
    width: 80,
  },
  {
    field: "quantity",
    headerName: "Holding",
    cellClassName: classes.QuantityCell,
    valueFormatter: (params: GridValueFormatterParams<number>) => numberFormat(params.value, { fallback: BLANK }),
    width: 130,
    align: "right",
  },
  {
    field: "percentage",
    headerName: "% of Portfolio",
    valueFormatter: (params: GridValueFormatterParams<number | string>) =>
      percentageFormat(params.value, { fallback: BLANK }),
    width: 120,
  },
];

const NoRows = (): JSX.Element => (
  <Stack width={200} alignItems="center" gap={2}>
    <Typography variant="body2" color="GrayText">
      Start exploring our products
    </Typography>
    <BuyButton sx={{ width: 100 }} />
  </Stack>
);

const Composition = ({ compositions }: CompositionProps): JSX.Element => {
  const [table, { width }] = useMeasure();
  const isSmallScreen = useMemo(() => width < 655, [width]);

  const [sortModel, setSortModel] = useState<GridSortModel>([defaultSortModel]);

  const handleSort = (model: GridSortModel): void => setSortModel(model);

  return (
    <Stack component={Box} ref={table} width="100%" height="calc(100% - (42px + 16px))" gap={1}>
      <DataTable
        dataGridProps={{
          "aria-label": "Composition",
          columns: isSmallScreen ? columns?.filter(({ field }) => ["product", "quantity"].includes(field)) : columns,
          rows: compositions,
          slots: {
            noRowsOverlay: NoRows,
          },
          columnBuffer: 9,
          initialState: {
            pagination: {
              paginationModel: {
                pageSize: 25,
              },
            },
          },
          sortModel,
          hideFooter: isSmallScreen,
          classes: {
            root: tableClasses.TableContainer,
            columnHeader: tableClasses.TableHeader,
            virtualScroller: classcat([tableClasses.TableBody, { [tableClasses.NoData]: !compositions?.length }]),
            overlayWrapper: tableClasses.TableOverlayWrapper,
            overlayWrapperInner: tableClasses.TableOverlayWrapperInner,
          },
          onSortModelChange: handleSort,
        }}
      />
    </Stack>
  );
};

export default Composition;
