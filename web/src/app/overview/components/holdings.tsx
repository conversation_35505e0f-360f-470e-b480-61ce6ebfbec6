import COLORS from "@app/theme/colors";
import { Button, Grid, Paper, Stack, Typography } from "@mui/material";
import { Maybe, MaybeNothing, numberFormat, toNumber } from "@rubiconcarbon/frontend-shared";
import {
  AssetType,
  TransactionResponse,
  AllocationResponse,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import { useMemo } from "react";
import Composition from "./composition";
import Transactions from "./transactions";
import Link from "next/link";
import useIsMobile from "@app/providers/mobile";
import Awaited from "@app/components/await/components/awaited";

import classes from "../styles/holdings.module.scss";

type HoldingsProps = {
  loading: boolean;
  allocations: MaybeNothing<AllocationResponse[]>;
  transactions: TransactionResponse[];
};

const Holdings = ({ loading, allocations = [], transactions = [] }: HoldingsProps): JSX.Element => {
  const isMobile = useIsMobile(1149);

  const totalAllocation = useMemo(
    () => allocations?.reduce((sum, { amountAllocated }) => sum + toNumber(amountAllocated), 0),
    [allocations],
  );

  const compositions = useMemo(
    () =>
      allocations?.reduce(
        (accum: Composition[], { amountAllocated, asset, detailedAsset }) => [
          ...accum,
          {
            id: asset!.id,
            assetDetailId: detailedAsset!.id,
            assetName: asset!.name,
            assetDetailName: detailedAsset!.name,
            assetDetailRegistryProjectId: (detailedAsset as TrimmedProjectVintageResponse)?.["project"]
              ?.registryProjectId,
            type: asset?.type === AssetType.RCT ? "Portfolio" : "Project",
            quantity: toNumber(amountAllocated, { parserBlacklist: [","] }),
            percentage: toNumber(amountAllocated, { parserBlacklist: [","] }) / (totalAllocation || 0),
          },
        ],
        [],
      ) || [],
    [allocations, totalAllocation],
  );

  const totalHoldings = useMemo(() => compositions?.reduce((sum, row) => sum + row?.quantity || 0, 0), [compositions]);

  return (
    <Maybe
      condition={!loading}
      fallback={
        <Awaited
          className={classes.Skeleton}
          repeat={2}
          variant="rectangular"
          sx={{ width: "100%", height: 450, borderRadius: "10px" }}
        />
      }
    >
      <Maybe condition={!!totalHoldings}>
        <Paper
          className="data-tour-step-holdings"
          component={Grid}
          item
          xs={isMobile || transactions?.length === 0 ? 12 : 5.93}
          container
          padding={2}
          gap={2}
          alignItems="flex-start"
          height={isMobile ? 500 : "inherit"}
        >
          <Grid
            item
            container
            xs={12}
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexGrow={1}
            height={42}
          >
            <Typography variant="h6">Holdings</Typography>
            <Stack direction="row" gap={1} alignItems="center" color={COLORS.rubiconGreen}>
              <Typography>Total Holdings: </Typography>
              <Typography fontWeight="bold">{numberFormat(totalHoldings)}</Typography>
            </Stack>
          </Grid>
          <Grid item xs={12} height="100%">
            <Composition compositions={compositions} />
          </Grid>
        </Paper>
      </Maybe>
      <Maybe condition={transactions?.length > 0}>
        <Paper
          className="data-tour-step-transactions"
          component={Grid}
          item
          xs={isMobile || !totalHoldings ? 12 : 5.93}
          container
          padding={2}
          gap={2}
          alignItems="flex-start"
          height={isMobile ? 500 : "inherit"}
        >
          <Grid
            item
            container
            xs={12}
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexGrow={1}
            height={42}
          >
            <Typography variant="h6">Transactions</Typography>
            <Link href="/transactions">
              <Button variant="contained">All Transactions</Button>
            </Link>
          </Grid>
          <Grid item xs={12} height="100%">
            <Transactions transactions={transactions} />
          </Grid>
        </Paper>
      </Maybe>
    </Maybe>
  );
};

export default Holdings;
