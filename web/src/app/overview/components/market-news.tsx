import { Typography } from "@mui/material";
import NewsList from "./news-list";
import { MarketNewsQueryResponse } from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";

type MarketNewsProps = {
  news: MaybeNothing<MarketNewsQueryResponse>;
};

const MarketNews = ({ news }: MarketNewsProps): JSX.Element => {
  return (
    <>
      <Typography variant="displayS" fontWeight="bold">
        Market News
      </Typography>
      <NewsList news={news} />
    </>
  );
};

export default MarketNews;
