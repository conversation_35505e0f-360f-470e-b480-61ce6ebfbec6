"use client";

import { MarketNewsQueryResponse, MarketNewsResponse } from "@rubiconcarbon/shared-types";
import {
  FETCHER_NODE,
  generate<PERSON><PERSON><PERSON><PERSON>ara<PERSON>,
  GenericAsyncList,
  GenericAsyncListFetch<PERSON>uery,
  MaybeNothing,
  Pageable,
} from "@rubiconcarbon/frontend-shared";
import { Box, Button, Divider, Stack, Typography } from "@mui/material";
import { momentByDays, utcDateFormat } from "@app/utilities/date";
import COLORS from "@app/theme/colors";
import { useContext } from "react";
import { AxiosContext } from "@app/providers/axios-provider";
import { useLogger } from "@app/providers/logging-provider";
import { useToggle } from "react-use";

import classes from "../styles/news-list.module.scss";

type ArticleProp = {
  article: MarketNewsResponse;
};

type NewsListProps = {
  news: MaybeNothing<MarketNewsQueryResponse>;
};

const extractBaseUrl = (url: string): string => {
  try {
    if (!url) return "";

    const parsed = new URL(url);

    return `${parsed.protocol}//${parsed.hostname}`;
  } catch {
    return "";
  }
};

const Article = ({ article }: ArticleProp): JSX.Element => {
  const { source, articleDate, header, summary, url } = article || {};

  return (
    <Stack className={classes.Article} gap={2}>
      <Stack className={classes.Heading} direction="row" alignItems="center" gap={1}>
        <Typography
          className={classes.Source}
          component="a"
          variant="body1"
          color={COLORS.rubiconGreen}
          href={extractBaseUrl(url)}
          target="_blank"
        >
          {source}
        </Typography>
        <Divider sx={{ height: 20, m: 0.5 }} orientation="vertical" />
        <Typography variant="body1" color="GrayText">
          {utcDateFormat(articleDate?.toISOString ? articleDate?.toISOString() : (articleDate as unknown as string))}
        </Typography>
      </Stack>

      <Typography className={classes.Header}>{header}</Typography>

      <Typography className={classes.Summary} component="p" variant="body2">
        {summary}
      </Typography>

      <Button className={classes.Button} variant="outlined">
        <Typography className={classes.ButtonText} component="a" color={COLORS.rubiconGreen} href={url} target="blank">
          View Full Article
        </Typography>
      </Button>
    </Stack>
  );
};

const NewsList = ({ news }: NewsListProps): JSX.Element => {
  const { api } = useContext(AxiosContext);
  const { logger } = useLogger();
  const [isFetching, setIsFetching] = useToggle(false);

  const fetchMore = async (query?: GenericAsyncListFetchQuery): Promise<MaybeNothing<Pageable<MarketNewsResponse>>> => {
    try {
      setIsFetching(true);

      const data = await api.get<Pageable<MarketNewsResponse>>(
        `/market-news?${generateQueryParams({
          offset: query?.offset,
          limit: query?.limit,
          includeTotalCount: true,
          startDate: momentByDays(-90).toISOString()?.split("T")[0] as any,
        })}`,
      );

      setTimeout(() => setIsFetching(false), 2000);
      return data?.data;
    } catch (error: any) {
      logger.error(`Error fetching more market news: ${error?.message}`, {});
      setIsFetching(false);
    }
  };

  return (
    <Box component={Stack} className={classes.NewsList} gap={3}>
      <GenericAsyncList
        id="markert-news"
        data={news as any}
        uniqueKey={["id"]}
        isFetching={isFetching}
        styles={{
          item: (item: any) =>
            (item !== FETCHER_NODE && {
              padding: "15px 0",
            }) ||
            {},
        }}
        renderItem={(item: MarketNewsResponse) => <Article article={item} />}
        renderFetchTrigger={(fetcher) => <Button onClick={fetcher}>Load more</Button>}
        fetch={fetchMore}
      />
    </Box>
  );
};

export default NewsList;
