import ImageJumbotron from "@app/components/image-jumbotron/image-jumbotron";
import { <PERSON><PERSON>, Stack, Typography } from "@mui/material";
import { FeaturedProjects } from "@app/constants/projects";
import { StaticImageData } from "next/image";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useGetSet } from "react-use";
import COLORS from "@app/theme/colors";
import { PROJECTS_PAGE } from "@app/app/projects/components/projects-table/constants";

const OurProjectsJumbotron = (): JSX.Element => {
  const [name, setName] = useState<string>("");
  const [link, setLink] = useState<string>("");
  const [nextName, setNextName] = useGetSet<string>("");
  const [transitionProgress, setTransitionProgress] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const featuredProjects = Object.entries(FeaturedProjects)
    .sort(([, { index: aIndex }], [, { index: bIndex }]) => aIndex - bIndex)
    .reduce(
      (accum, [id, { name, image }]) => ({
        ...accum,
        images: [...accum?.images, image],
        names: [...accum?.names, name],
        links: [...accum?.links, `/projects/${id}`],
      }),
      {
        images: [],
        names: [],
        links: [],
      } as { images: StaticImageData[]; names: string[]; links: string[] },
    );

  useEffect(() => {
    if (!name) setName(featuredProjects.names.at(0)!);
    if (!link) setLink(featuredProjects.links.at(0)!);
  }, [featuredProjects.links, featuredProjects.names, link, name]);

  const onImageChange = (index: number): void => {
    if (!!featuredProjects?.links?.at(index)) setLink(featuredProjects.links.at(index)!);
    setIsTransitioning(false);
  };

  const onTransitionProgress = (progress: number, nextIndex: number): void => {
    setTransitionProgress(progress);
    setIsTransitioning(true);

    if (progress < 0.1 && !!featuredProjects.names?.at(nextIndex)) setNextName(featuredProjects.names.at(nextIndex)!);

    if (progress > 0.5) setName(nextName());
  };

  return (
    <ImageJumbotron
      data={{
        images: featuredProjects.images,
        text: "",
        topLeftAction: (
          <Stack gap={1}>
            <Typography variant="displayS" component="h2" fontWeight="bold" color="white">
              Our Projects
            </Typography>
            <Link href={PROJECTS_PAGE}>
              <Button
                variant="contained"
                size="extraSmall"
                sx={{
                  backgroundColor: "white",
                  color: COLORS.rubiconGreen,
                  "&:hover": {
                    color: "white",
                  },
                }}
              >
                Explore Projects
              </Button>
            </Link>
          </Stack>
        ),
        bottomRightAction: (
          <Link href={link as any} style={{ textDecoration: "unset" }}>
            <Typography
              variant="h6"
              color="white"
              sx={{
                opacity: isTransitioning
                  ? transitionProgress < 0.5
                    ? 1 - transitionProgress * 2
                    : (transitionProgress - 0.5) * 2
                  : 1,
                transition: "opacity 0.2s ease-in-out",
              }}
            >
              {name}
            </Typography>
          </Link>
        ),
        fullWidth: true,
        fullHeight: true,
        animation: {
          duration: 1750,
        },
        onImageChange,
        onTransitionProgress,
      }}
      style={{
        backgroundColor: "rgba(0, 0, 0, 0.2)",
        backgroundBlendMode: "darken",
        minHeight: 300,
        height: 300,
      }}
    />
  );
};

export default OurProjectsJumbotron;
