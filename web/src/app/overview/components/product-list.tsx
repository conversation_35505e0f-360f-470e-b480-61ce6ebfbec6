"use client";

import { BookFetchQueryParams } from "@app/app/data-server";
import ImageMinitron from "@app/components/image-minitron/image-minitron";
import { reconcileProduct, ImageMinitronProductsData } from "@app/constants/image-minitron-products-data";
import { Minitron } from "@app/types/static-content";
import useRequest from "@app/hooks/useRequest";
import { PortalBookQueryResponse, BookType } from "@rubiconcarbon/shared-types";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { useLogger } from "@app/providers/logging-provider";
import ProductPriceDisplay from "@app/components/product-price-display/product-price-display";
import { currencyFormat, Maybe, px } from "@rubiconcarbon/frontend-shared";
import EmptySlot from "@app/components/empty-slot/empty-slot";
import ProductTitle from "@app/components/product-title/product-title";
import { ProductIds, ProductMapping } from "@app/constants/products";
import { Stack } from "@mui/material";

import classes from "../styles/product-list.module.scss";
import { DoubleHexagonIcon, IndustrialPollutantIcon, StockChartIcon, TreesIcon } from "@app/components/custom-icons";

const ProductList = (): JSX.Element => {
  const { logger } = useLogger();

  const { data: books } = useRequest<PortalBookQueryResponse, null, BookFetchQueryParams>({
    url: "/books",
    queryParams: {
      limit: DEFAULT_MAX_PAGING_LIMIT,
      types: [BookType.PORTFOLIO_PUBLIC],
    },
    swrOptions: {
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  const products = [
    ...((books?.data || [])
      ?.map((book) => {
        const product = reconcileProduct(book, ImageMinitronProductsData);

        if (!product) return null;

        return {
          ...product,
          title: <ProductTitle value={product?.title} trademark={product?.text !== ProductIds.RUBICON_RATED_TONNE} />,
          bottomSlot: (
            <Stack justifyContent="space-between" alignItems="center" direction="row">
              <Maybe condition={ProductIds.NATURE === product?.text}>
                <TreesIcon />
              </Maybe>
              <Maybe condition={ProductIds.SUPERPOLLUTANT_ELIMINATION === product?.text}>
                <IndustrialPollutantIcon />
              </Maybe>
              <Maybe condition={ProductIds.CARBON_REMOVALS === product?.text}>
                <DoubleHexagonIcon />
              </Maybe>
              <Maybe condition={ProductIds.RUBICON_RATED_TONNE === product?.text}>
                <StockChartIcon />
              </Maybe>
              <ProductPriceDisplay value={currencyFormat(product?.bottomSlot?.toString() ?? "")} />
            </Stack>
          ),
        };
      })
      .filter((product) => !!product) as Minitron[]),
    // Add BYO
    {
      ...ImageMinitronProductsData[4],
      title: <ProductTitle value={ImageMinitronProductsData[4]?.title} />,
      bottomSlot: (
        <EmptySlot
          sx={{
            display: "flex",
            height: "36px",
          }}
        />
      ),
    },
  ].sort((a, b) => ProductMapping?.[a.text]?.order - ProductMapping?.[b.text]?.order);

  return (
    <div className={classes.ImageMinitronsContainer}>
      {products.map((product) => (
        <ImageMinitron
          key={product.text}
          classes={{ link: classes.LinkTagContainer, tag: classes.LinkTag }}
          data={product}
          style={{
            ...px({
              borderColor: product?.text === ProductIds.RUBICON_RATED_TONNE && "#9747FF",
              "&:hover": product?.text === ProductIds.RUBICON_RATED_TONNE && {
                background: `linear-gradient(${
                  !product?.disabled
                    ? "180deg, rgba(151, 71, 255, 0.5) 0%, rgba(176, 116, 255, 0.26) 100%"
                    : "rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)"
                }), url(${product?.image?.src ?? ""})`,
                backgroundSize: "280%",
                backgroundPosition: "center center",
                borderColor: "#9747FF",
              },
            }),
          }}
        />
      ))}
    </div>
  );
};

export default ProductList;
