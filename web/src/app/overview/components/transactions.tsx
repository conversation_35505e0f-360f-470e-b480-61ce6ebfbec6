import { useMemo, useState } from "react";
import { DataTable } from "@app/components/data-table";
import { Box, Stack } from "@mui/material";
import { GridColDef, GridRenderCellParams, GridSortItem, GridSortModel, GridValueGetterParams } from "@mui/x-data-grid";
import { BLANK } from "@app/constants/common";
import { numberFormat, classcat } from "@rubiconcarbon/frontend-shared";
import type { Composition as Transactions } from "../types/holdings";
import { TransactionResponse, TransactionType } from "@rubiconcarbon/shared-types";
import { utcDateFormat } from "@app/utilities/date";
import { TransactionStatus, TransactionStatusTypeName, TransactionTypeName } from "@app/constants/transactions";
import { getProductName } from "@app/utilities/get-product-name";
import ProductName from "@app/components/styled/product-name";
import StatusChip from "@app/components/styled/status-chip";
import { useMeasure } from "react-use";

import tableClasses from "../styles/table.module.scss";

type TransactionsProps = {
  transactions: TransactionResponse[];
};

const defaultSortModel: GridSortItem = { field: "amount", sort: "desc" };

const columns: GridColDef[] = [
  {
    field: "createdAt",
    headerName: "Date",
    sortComparator: (a: string, b: string): number => {
      const date1 = new Date(a).getTime();
      const date2 = new Date(b).getTime();
      return date1 - date2;
    },
    valueGetter: ({ row }: GridValueGetterParams<TransactionResponse>): string =>
      utcDateFormat(row?.createdAt?.toISOString ? row?.createdAt?.toISOString() : row?.createdAt?.toString(), {
        defaultValue: BLANK,
      }),
    width: 105,
  },
  {
    field: "type",
    headerName: "Type",
    type: "singleSelect",
    valueOptions: [
      { value: TransactionType.PURCHASE, label: TransactionTypeName.PURCHASE },
      { value: TransactionType.RETIREMENT, label: TransactionTypeName.RETIREMENT },
    ],
    valueGetter: ({ row }: GridValueGetterParams<TransactionResponse>): string => row?.type,
    width: 95,
  },
  {
    field: "assetFlows",
    headerName: "Product",
    valueGetter: ({ row }: GridValueGetterParams<TransactionResponse>) => getProductName(row),
    renderCell: ({ row }: GridRenderCellParams<TransactionResponse>): JSX.Element => <ProductName transaction={row} />,
    minWidth: 250,
    maxWidth: 350,
  },
  {
    field: "amount",
    headerName: "Quantity",
    type: "number",
    valueGetter: ({ row }: GridValueGetterParams<TransactionResponse>): number => row?.totalQuantity,
    renderCell: ({ row }: GridRenderCellParams<TransactionResponse>) =>
      numberFormat(row?.totalQuantity ?? 0, { separator: "thousand" }),
    width: 90,
  },
  {
    field: "status",
    headerName: "Status",
    type: "singleSelect",
    valueOptions: [
      { value: TransactionStatus.INDICATIVE, label: TransactionStatusTypeName.INDICATIVE },
      { value: TransactionStatus.FIRM, label: TransactionStatusTypeName.FIRM },
      { value: TransactionStatus.BINDING, label: TransactionStatusTypeName.BINDING },
      { value: TransactionStatus.EXECUTED, label: TransactionStatusTypeName.EXECUTED },
      { value: TransactionStatus.PAID, label: TransactionStatusTypeName.PAID },
      { value: TransactionStatus.DELIVERED, label: TransactionStatusTypeName.DELIVERED },
      { value: TransactionStatus.SETTLED, label: TransactionStatusTypeName.SETTLED },
      { value: TransactionStatus.COMPLETED, label: TransactionStatusTypeName.COMPLETED },
      { value: TransactionStatus.CANCELED, label: TransactionStatusTypeName.CANCELED },
      { value: TransactionStatus.PROCESSING, label: TransactionStatusTypeName.PROCESSING },
      { value: TransactionStatus.ADMIN_REVIEW, label: TransactionStatusTypeName.ADMIN_REVIEW },
      { value: TransactionStatus.PORTFOLIO_MANAGER_REVIEW, label: TransactionStatusTypeName.PORTFOLIO_MANAGER_REVIEW },
      { value: TransactionStatus.FAILED, label: TransactionStatusTypeName.FAILED },
      { value: TransactionStatus.REVERTED, label: TransactionStatusTypeName.REVERTED },
    ],
    valueGetter: ({ row }: GridValueGetterParams<TransactionResponse>): string => row?.status,
    renderCell: ({ value }: GridRenderCellParams<TransactionResponse, string>) =>
      value && <StatusChip status={value} />,
    minWidth: 125,
    maxWidth: 200,
    flex: 1,
  },
];

const Transactions = ({ transactions }: TransactionsProps): JSX.Element => {
  const [table, { width }] = useMeasure();
  const isSmallScreen = useMemo(() => width < 655, [width]);

  const [sortModel, setSortModel] = useState<GridSortModel>([defaultSortModel]);

  const handleSort = (model: GridSortModel): void => setSortModel(model);

  return (
    <Stack component={Box} ref={table} width="100%" height="calc(100% - (42px + 16px))" gap={1}>
      <DataTable
        dataGridProps={{
          "aria-label": "Transactions",
          columns: isSmallScreen ? columns?.filter(({ field }) => ["assetFlows", "status"].includes(field)) : columns,
          rows: transactions,
          columnBuffer: 9,
          initialState: {
            pagination: {
              paginationModel: {
                pageSize: 25,
              },
            },
          },
          sortModel,
          hideFooter: isSmallScreen,
          classes: {
            root: tableClasses.TableContainer,
            columnHeader: tableClasses.TableHeader,
            virtualScroller: classcat([tableClasses.TableBody, { [tableClasses.NoData]: !transactions?.length }]),
            overlayWrapper: tableClasses.TableOverlayWrapper,
            overlayWrapperInner: tableClasses.TableOverlayWrapperInner,
          },
          onSortModelChange: handleSort,
        }}
      />
    </Stack>
  );
};

export default Transactions;
