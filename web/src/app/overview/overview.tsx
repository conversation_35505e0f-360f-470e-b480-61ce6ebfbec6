"use client";

import { <PERSON>, Button, Paper, Stack, Typography } from "@mui/material";
import { PortalUpdaterContextProvider } from "../portal-updater-context";
import { classcat, MaybeNothing, useRequest } from "@rubiconcarbon/frontend-shared";
import {
  GroupedAllocationWithNestedResponse,
  MarketNewsQuery,
  MarketNewsQueryResponse,
  TransactionQuery,
  TransactionQueryResponse,
  TransactionResponse,
  TransactionType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useLogger } from "@app/providers/logging-provider";
import Holdings from "./components/holdings";
import MarketNews from "./components/market-news";
import Products from "./components/products";
import { useContext, useEffect, useMemo } from "react";
import HeadingContainer from "../components/heading-container/heading-container";
import ContentContainer from "../components/content-container/content-container";
import { momentByDays } from "@app/utilities/date";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { useTour } from "@reactour/tour";
import { marketNewsResponseMock, transactionsMock } from "../guided-tour/mock-data";
import { isShowGuidedTour, turnTourOff } from "../guided-tour/utilities";
import OurProjectsJumbotron from "./components/our-projects-jumbotron";
import BuyButton from "@app/components/buy-button/buy-button";
import RetireButton from "@app/components/retire-button/retire-button";
import { StoreContext } from "@app/providers/state/store";
import Image from "next/image";
import useIsMobile from "@app/providers/mobile";

import classes from "./styles/overview.module.scss";
import { IsoscelesTriangleIcon } from "@app/components/custom-icons";
import image from "@assets/images/explore-projects.webp";

type ContentProps = {
  loadingHoldings: boolean;
  allocations: MaybeNothing<GroupedAllocationWithNestedResponse>;
  transactions: MaybeNothing<TransactionResponse[]>;
  news: MaybeNothing<MarketNewsQueryResponse>;
};

type OverviewProps = {
  id: uuid;
};

const Content = ({ loadingHoldings, allocations, transactions, news }: ContentProps): JSX.Element => {
  const { ephemeralState } = useContext(StoreContext);

  const isMobile = useIsMobile(450);
  const { isOpen } = useTour();
  const hasHoldings = useMemo(
    () => isOpen || !!allocations?.allocations.length || !!transactions?.length,
    [allocations?.allocations.length, transactions?.length, isOpen],
  );
  const hasNews = useMemo(
    // only show news section when you have 5 articles or more
    () => isOpen || (news?.data?.length ?? 0) >= 5,
    [news?.data?.length, isOpen],
  );

  return (
    <Box className={classes.Overview}>
      <Stack data-tour="step-portfolio-management" className={classes.ProductsItem} gap={2} padding={2}>
        <Typography variant="displayM" component="h2">
          Our Portfolios
        </Typography>
        <Products />
      </Stack>
      <Paper
        component={Stack}
        className={classcat([classes.NewsItem, { [classes.NoNews]: !hasNews }])}
        gap={2}
        padding={2}
        borderRadius="8px !important"
      >
        {isOpen ? <MarketNews news={marketNewsResponseMock} /> : <MarketNews news={news} />}
      </Paper>
      <Stack
        className={classcat([classes.BuyRetireButtonsItem, { [classes.NoNews]: !hasNews }])}
        direction="row"
        gap={1}
      >
        <BuyButton />
        <RetireButton />
      </Stack>
      <Box
        className={classcat([classes.HoldingsItem, { [classes.NoNews]: !hasNews, [classes.NoHoldings]: !hasHoldings }])}
      >
        <Holdings
          loading={loadingHoldings}
          allocations={allocations?.allocations}
          transactions={isOpen ? transactionsMock : (transactions ?? [])}
        />
      </Box>
      <Box
        className={classcat([
          classes.Informational,
          { [classes.NoNews]: !hasNews, [classes.NoHoldings]: !hasHoldings },
        ])}
      >
        <Stack className={classes.WhereToStart} gap={1.5}>
          <Typography variant={isMobile ? "h5" : "h4"}>Not sure where to start?</Typography>
          <Typography variant="caption">Contact your Account Manager for a personalized advice</Typography>
          <Button
            className={classes.ContactAccountManagerButton}
            variant="contained"
            size={isMobile ? "extraSmall" : "small"}
          >
            <Typography
              component="a"
              href={`mailto:${ephemeralState?.organization?.customerPortfolio?.rubiconManager?.email}`}
              className={classes.Label}
              variant="caption"
            >
              Contact Account Manager
            </Typography>
          </Button>
        </Stack>
        <Image className={classes.ExploreProjectsImage} src={image} alt="Explore Projects" priority />
      </Box>
    </Box>
  );
};

export default function Overview({ id }: OverviewProps): JSX.Element {
  const { logger } = useLogger();
  const { setIsOpen } = useTour();
  const isMedium = useIsMobile(900);
  const isSmall = useIsMobile(660);

  const {
    data: allocations,
    mutate: refreshAllocations,
    isLoading: loadingAllocations,
  } = useRequest<GroupedAllocationWithNestedResponse, object, { id: uuid }>({
    url: `/organizations/{id}/holdings`,
    pathParams: { id },
    swrOptions: {
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  const {
    data: transactionsResponse,
    mutate: refreshTransactions,
    isLoading: loadingTransactions,
  } = useRequest<TransactionQueryResponse, object, object, TransactionQuery>({
    url: `/transactions`,
    queryParams: {
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      types: [TransactionType.PURCHASE, TransactionType.RETIREMENT],
    },
    swrOptions: {
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  useEffect(() => {
    if (isShowGuidedTour()) {
      setIsOpen(true);
      turnTourOff();
    }
  }, [setIsOpen]);

  const loadingHoldings = useMemo(
    () => loadingAllocations || loadingTransactions,
    [loadingAllocations, loadingTransactions],
  );

  const { data: news, mutate: refreshNews } = useRequest<MarketNewsQueryResponse, object, object, MarketNewsQuery>({
    url: "/market-news",
    queryParams: {
      offset: 0,
      limit: 5,
      includeTotalCount: true,
      startDate: momentByDays(-90).toISOString()?.split("T")[0] as any,
    },
    swrOptions: {
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  return (
    <Box position="relative">
      <OurProjectsJumbotron />
      <Box position="absolute" top={305} left={0} right={0} display="flex" justifyContent="center">
        <IsoscelesTriangleIcon
          width={isSmall ? 200 : isMedium ? 400 : 600}
          height={isSmall ? 40 : isMedium ? 50 : 70}
          rotate="180deg"
          zIndex={-1}
        />
      </Box>
      <HeadingContainer>
        <PortalUpdaterContextProvider
          updater={async () => {
            await refreshAllocations();
            await refreshNews();
            await refreshTransactions();
          }}
        />
      </HeadingContainer>
      <Box>
        <ContentContainer>
          <Content
            {...{
              loadingHoldings,
              allocations,
              transactions: transactionsResponse?.data,
              news,
            }}
          />
        </ContentContainer>
      </Box>
    </Box>
  );
}
