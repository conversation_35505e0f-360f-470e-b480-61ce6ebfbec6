import useTriggerRequest from "@app/hooks/useTriggerRequest";
import { useAuth } from "@app/providers/auth-provider";
import { AgreementLabel, AgreementRequest, AgreementResponse, AgreementStatus } from "@rubiconcarbon/shared-types";
import { ChangeEvent, MouseEvent, PropsWithChildren, useMemo, useRef, useState } from "react";
import { environment } from "@app/environment";
import { Maybe, NO_OP, px, Undefinable, useScroll } from "@rubiconcarbon/frontend-shared";
import GenericDialog from "@app/components/generic-dialog/generic-dialog";
import { useDebounce } from "react-use";
import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import Link from "next/link";
import { useLogger } from "@app/providers/logging-provider";
import { RubiconSecondaryIcon } from "@app/components/custom-icons";
import Paragraph from "@app/components/paragraph/paragraph";
import { CONSENT_TREE } from "./consent";
import { CloseRounded } from "@mui/icons-material";

import classes from "./styles.module.scss";

type PortalAgreementProps = {
  loggedIn: boolean;
};

const CURRENT_VERSION = environment.settings.agreementConsentVersion;
const SUPPORT_EMAIL = "<EMAIL>";

const PortalAgreement = ({ loggedIn, children }: PropsWithChildren<PortalAgreementProps>): JSX.Element => {
  const { user, logout } = useAuth();
  const { logger } = useLogger();
  const isSmallerScreen = useMediaQuery("(max-width: 450px)");
  const ref = useRef<HTMLDivElement>(null);
  const { yPercent } = useScroll(ref);

  const [continueToPortal, setContinueToPortal] = useState<boolean>();
  const [accepted, setAccepted] = useState<boolean>(false);
  const [agreementPayload, setAgreementPayload] = useState<AgreementRequest>();
  const [showConsentVerbiage, setShowConsentVerbiage] = useState<boolean>(false);

  const openConsent = useMemo(() => continueToPortal === false, [continueToPortal]);

  const { trigger: getAgreement } = useTriggerRequest<AgreementResponse[]>({
    url: "users/agreements",
    queryParams: {
      label: AgreementLabel.CUSTOMER_CONSENT,
      version: CURRENT_VERSION,
    },
    swrOptions: {
      onSuccess: (data: Undefinable<AgreementResponse[]>): void => {
        if (!!data?.length && data.at(0)?.status === AgreementStatus.ACCEPTED) setContinueToPortal(true);
        else setContinueToPortal(false);
      },
      onError: (error: any): void => {
        logger.error(`Error fetching user consent agreement for ${user?.email}: ${error?.message}`, {});
        logout(); // user will be in an indeterminate state if not logged out.
      },
    },
  });

  const { trigger: saveAgreement } = useTriggerRequest<AgreementResponse, AgreementRequest>({
    url: "users/agreements/{label}",
    method: "put",
    pathParams: {
      label: AgreementLabel.CUSTOMER_CONSENT,
    },
    requestBody: agreementPayload,
    swrOptions: {
      onSuccess: (data: Undefinable<AgreementResponse>): void => {
        const canContinue = data?.status === AgreementStatus.ACCEPTED;

        if (canContinue) setContinueToPortal(canContinue);
        else logout();
      },
      onError: (error: any): void => {
        logger.error(`Error saving user consent agreement for ${user?.email}: ${error?.message}`, {});
        logout(); // user will be in an indeterminate state if not logged out.
      },
    },
  });

  useDebounce(
    () => {
      if (loggedIn) setTimeout(async () => await getAgreement());
    },
    400,
    [loggedIn],
  );

  const handleAccept = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();

    setAgreementPayload({
      status: AgreementStatus.ACCEPTED,
      version: CURRENT_VERSION,
    });

    setTimeout(async () => await saveAgreement());
  };

  const handleDecline = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();

    setAgreementPayload({
      status: AgreementStatus.DECLINED,
      version: CURRENT_VERSION,
    });

    setTimeout(async () => await saveAgreement());
  };

  return (
    <>
      <Maybe condition={!loggedIn || !!continueToPortal}>{children}</Maybe>
      <GenericDialog
        open={openConsent}
        fullscreen={isSmallerScreen}
        onClose={showConsentVerbiage ? (): void => setShowConsentVerbiage(false) : NO_OP}
        classes={{
          root: `${classes.Dialog}${showConsentVerbiage ? ` ${classes.DialogConsent}` : ""}`,
          content: `${classes.DialogContent}${showConsentVerbiage ? ` ${classes.DialogContentConsent}` : ""}`,
        }}
        {...px({
          title: showConsentVerbiage && <Typography variant="displayS">Terms of Use and Privacy Policy</Typography>,
          dismissIcon: showConsentVerbiage && <CloseRounded />,
          negativeAction: showConsentVerbiage && {
            buttonText: "RETURN TO CONSENT",
            onClick: (): void => setShowConsentVerbiage(false),
          },
        })}
      >
        <Box
          className={classes.Container}
          ref={ref}
          height={showConsentVerbiage ? (!isSmallerScreen ? "calc(80vh - 60px)" : "calc(100% - 60px)") : "unset"}
        >
          <Maybe condition={showConsentVerbiage}>
            <LinearProgress className={classes.Progress} variant="determinate" value={yPercent || 0} />
          </Maybe>
          <Maybe condition={!showConsentVerbiage}>
            <Box className={classes.Consent}>
              <Stack alignItems="center" gap={2}>
                <RubiconSecondaryIcon
                  classes={{ root: classes.Icon }}
                  width={200}
                  height={"auto"}
                  viewBox="0 0 24 16"
                />
                <Typography variant="h6" textAlign="center">
                  Acceptance of Terms of Use and Privacy Policy
                </Typography>
                <Typography variant="body2">
                  Before you continue, please carefully review and accept our{" "}
                  <Link href="#" onClick={() => setShowConsentVerbiage(true)}>
                    Terms of Use and Privacy Policy.
                  </Link>
                </Typography>
                <Typography variant="body2">
                  By checking the box below and clicking &quot;ACCEPT&quot;, you{" "}
                  <strong>confirm your understanding and agreement of these guidelines</strong>.
                </Typography>
                <Typography variant="body2">
                  Feel free to reach out at <Link href={`mailto:${SUPPORT_EMAIL}`}>{SUPPORT_EMAIL}</Link> should you
                  have any questions or need further clarity.
                </Typography>
              </Stack>
              <Stack gap={2} pt={2}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={accepted}
                      size="small"
                      onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) => setAccepted(checked)}
                    />
                  }
                  label={
                    <Typography variant="body2">
                      I have read and agree to the Terms of Use and Privacy Policy
                    </Typography>
                  }
                />
                <Stack direction="row" justifyContent="center" gap={2}>
                  <Button variant="contained" disabled={!accepted} onClick={handleAccept}>
                    Accept
                  </Button>
                  <Button onClick={handleDecline}>Decline</Button>
                </Stack>
              </Stack>
            </Box>
          </Maybe>
          <Maybe condition={showConsentVerbiage}>
            <Box className={classes.ConsentVerbiage}>
              {CONSENT_TREE.map((data, index) => (
                <Paragraph key={index} data={data} />
              ))}
            </Box>
          </Maybe>
        </Box>
      </GenericDialog>
    </>
  );
};

export default PortalAgreement;
