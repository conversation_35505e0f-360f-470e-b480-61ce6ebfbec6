"use client";

import {
  Box,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { CommonProductProps } from "../types/product";
import ProductHeader from "./product-header";
import { useEffect, MouseEvent, useState, CSSProperties, useMemo } from "react";
import { useAxios } from "@app/providers/axios-provider";
import {
  AssetType,
  GroupedAllocationWithNestedResponse,
  PortalProjectQueryResponse,
  PortalProjectResponse,
  ProjectEligibilityAccreditation,
  ProjectRelations,
  TrimmedProjectResponse,
  TrimmedProjectVintageResponse,
  UserActionDataType,
} from "@rubiconcarbon/shared-types";
import Link from "next/link";
import IntegrityScoreModal from "../../components/integrity-score/components/integrity-score-modal";
import TagChip from "@app/components/tag-chip/tag-chip";
import SDGsBox from "@app/components/sdgs-box/sdgs-box";
import { classcat, LinearBar, Maybe, percentageFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import InteractiveWaterOrgLogo from "../../components/water-org/interactive-logo";
import IntegrityScore from "../../components/integrity-score/integrity-score";
import { groupBy, sum } from "lodash";
import { useMeasure, useUpdateEffect } from "react-use";
import FavoriteChip from "@app/app/favorites/favorite-chip/favorite-chip";
import { useLogger } from "@app/providers/logging-provider";
import Awaited from "@app/components/await/components/awaited";
import { ProductIds } from "@app/constants/products";

import classes from "../styles/product-details.module.scss";

const COLUMNS = [
  {},
  { label: "Project" },
  { label: "Price Range" },
  { label: "UN SDGs", style: { textAlign: "left" } },
  { label: "Source", style: { textAlign: "left" } },
  { label: "Country", style: { textAlign: "left" } },
  { label: "% of Allocation", style: { textAlign: "left" } },
];

interface ExtendedProject extends TrimmedProjectResponse {
  proportionOfBook: number;
}
const getTags = (project: TrimmedProjectResponse): JSX.Element | "" => {
  if (!project?.country?.name) return "";
  return project.country?.name && <TagChip label={project.country?.name} />;
};

const Row = (props: {
  row: ExtendedProject;
  isMobile: boolean;
  priceRange?: string;
  isRTTProduct?: boolean;
}): JSX.Element => {
  const { row, isMobile, priceRange, isRTTProduct } = props;
  const [isScoreModalOpen, setIsScoreModalOpen] = useState<boolean>(false);
  const isWaterOrgEligible = row.eligibilityAccreditations.includes(ProjectEligibilityAccreditation.WATER_ORG);

  return (
    <>
      <TableRow>
        {/* adding check for rubicon rated tonne */}
        <Maybe condition={!isRTTProduct}>
          <TableCell align="right" sx={{ textAlign: "right", width: "45px" }}>
            <Box onClick={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}>
              <IntegrityScore
                score={Math.round(row?.integrityGradeScore ?? 0)}
                separate
                hideSubtext
                className={classes.integrityGrade}
              />
            </Box>
          </TableCell>
        </Maybe>
        <TableCell align="right" sx={{ textAlign: "left", textUnderlineOffset: 4 }}>
          <Stack direction="row" gap={2}>
            <Stack direction="row" gap={0}>
              <Stack direction="column">
                <Link
                  href={`/projects/${row.id}` as any}
                  style={{ textDecorationColor: "#000000" }}
                  passHref
                  legacyBehavior
                >
                  <a onClick={(event: MouseEvent<HTMLAnchorElement>): void => event.stopPropagation()} target="_blank">
                    <Typography variant="body2" className={classes.mainInfo}>
                      {row?.name}
                    </Typography>
                  </a>
                </Link>
                <Typography mt={0.5} variant="body2" className={classes.supportiveInfo}>
                  {row?.projectType?.category}
                </Typography>
                <Typography variant="body2" className={classes.supportiveInfo}>
                  {row?.registryProjectId}
                </Typography>
              </Stack>
              <FavoriteChip project={row as PortalProjectResponse} iconSx={{ marginTop: "-2px" }} />
            </Stack>
            <Maybe condition={isWaterOrgEligible}>
              <Box sx={{ marginTop: -0.5 }}>
                <InteractiveWaterOrgLogo width={90} />
              </Box>
            </Maybe>
          </Stack>
        </TableCell>
        {/* adding check for rubicon rated tonne */}
        <Maybe condition={!isRTTProduct}>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            <Typography variant="body2">{priceRange}</Typography>
          </TableCell>
        </Maybe>
        <TableCell align="right" sx={{ textAlign: "left" }}>
          <SDGsBox height={35} projectSDGs={row?.projectSDGs?.sort((a, b) => (a.sdgTypeId < b.sdgTypeId ? -1 : 1))} />
        </TableCell>
        <TableCell>
          <Typography variant="body2" component="div">
            {row?.registryName ?? ""}
          </Typography>
        </TableCell>
        <TableCell align="right" sx={{ textAlign: "left" }}>
          {getTags(row)}
        </TableCell>
        <TableCell>
          <Stack direction="row" gap={1}>
            <Maybe condition={!isMobile}>
              <Box mt={1} sx={{ width: "100%" }}>
                <LinearBar
                  animate
                  height={8}
                  value={!!row?.proportionOfBook ? row.proportionOfBook * 100 : 0}
                  color="rgba(76, 158, 184, 1)"
                  baseStrokeColor="rgba(217, 217, 217, 1)"
                />
              </Box>
            </Maybe>
            <Typography variant="body2" component="div">
              {!!row?.proportionOfBook ? percentageFormat(row?.proportionOfBook) : ""}
            </Typography>
          </Stack>
        </TableCell>
      </TableRow>
      <IntegrityScoreModal
        open={isScoreModalOpen}
        handleClose={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}
      />
    </>
  );
};

const ProductDetails = ({ product }: CommonProductProps): JSX.Element => {
  const [projects, setProjects] = useState<ExtendedProject[]>([]);

  const allocations = useMemo(
    () => (product?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations,
    [product?.ownerAllocations],
  );
  const isRRTProduct = useMemo(() => product?.id === ProductIds.RUBICON_RATED_TONNE, [product?.id]);
  const hasNoAllocations = useMemo(() => !allocations?.length, [allocations?.length]);

  const [projectsWithPrice, setProjectsWithPrice] = useState<PortalProjectResponse[]>([]);
  const { api } = useAxios();
  const isMobile = useMediaQuery("(max-width: 500px)");
  const [rctContainer, { height: rctContainerHeight }] = useMeasure();
  const [tableContainer, { height: tableContainerHeight }] = useMeasure<HTMLDivElement>();
  const { logger } = useLogger();

  const { trigger: getProjectsWithPrice, isMutating: isLoadingPrices } = useTriggerRequest<PortalProjectQueryResponse>({
    url: "/projects",
    queryParams: {
      ids: projects?.map((p) => p.id),
      includeRelations: [ProjectRelations.PRICE_RANGE],
    },
    swrOptions: {
      onSuccess: (data: PortalProjectQueryResponse): void => {
        setProjectsWithPrice(data?.data);
      },
      onError: (error: any): void => {
        logger.error(`Error fetching projects with price range. ${error?.message}`, {});
      },
    },
  });

  const columns = useMemo(() => {
    if (product?.id === ProductIds.RUBICON_RATED_TONNE)
      return (
        COLUMNS
          // Filtering out certain columns for Rubicon Rated Portfolio (Integrity Score & Price Range)
          .filter((column, index) => index !== 0 && column?.label !== "Price Range")
          // Change Source to Accreditor for Rubicon Rated Portfolio
          .map(({ label, ...rest }) => (label === "Source" ? { label: "Accreditor", ...rest } : { label, ...rest }))
      );
    return COLUMNS;
  }, [product?.id]);

  useEffect(() => {
    if (product?.id) {
      const action: UserActionDataType = { version: 1, type: "portfolio_viewed", data: { productId: product?.id } };
      api.post("/user-actions", action).then();
    }
  }, [api, product?.id]);

  useEffect(() => {
    if (!!product?.ownerAllocations) {
      const newProjects: ExtendedProject[] = Object.values(
        groupBy(
          (product?.ownerAllocations as GroupedAllocationWithNestedResponse | undefined)?.allocations
            .filter((x) => x.asset.type == AssetType.REGISTRY_VINTAGE)
            .map((ap) => {
              const proportion = ap.amountAllocated;
              const project = (ap.detailedAsset! as TrimmedProjectVintageResponse).project as TrimmedProjectResponse;
              return {
                ...project,
                proportionOfBook: proportion,
              };
            }) ?? [],
          (x) => x.id,
        ),
      ).map((v) => ({ ...v[0], proportionOfBook: sum(v.map((x) => x.proportionOfBook)) }));

      const total = sum(newProjects.map((x) => x.proportionOfBook));
      newProjects.forEach((x) => (x.proportionOfBook = x.proportionOfBook / total));

      setProjects(newProjects);
    }
  }, [product?.ownerAllocations]);

  useUpdateEffect(() => {
    if (!isRRTProduct && !!projects && projects?.length > 0) {
      getProjectsWithPrice();
    }
  }, [projects, isRRTProduct]);

  return (
    <Stack
      className={classcat([classes.Container, "no-padding"])}
      height={!hasNoAllocations ? rctContainerHeight + tableContainerHeight : "100%"}
    >
      <ProductHeader product={product} />
      <Maybe
        condition={!isLoadingPrices}
        fallback={
          <Awaited
            className={classes.Skeleton}
            variant="rectangular"
            sx={{
              width: "100%",
              height: rctContainerHeight,
              borderRadius: "10px",
            }}
          />
        }
      >
        <Box ref={rctContainer} className={classcat([classes.RCTContainer, { [classes.RRTMargin]: isRRTProduct }])}>
          <Maybe condition={!hasNoAllocations}>
            <TableContainer ref={tableContainer} component={Paper}>
              <Table aria-label="product-projects-table">
                <TableHead>
                  <TableRow sx={{ verticalAlign: "top" }}>
                    {columns.map((column, idx) => (
                      <TableCell key={idx} sx={{ paddingLeft: "16px", ...(column.style as CSSProperties) }}>
                        <Typography variant="body2" component="h4" fontWeight="700">
                          {column.label}
                        </Typography>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {projects
                    ?.sort((a, b) => (a.proportionOfBook > b.proportionOfBook ? -1 : 1))
                    .map((p) => (
                      <Row
                        key={p.id}
                        row={p}
                        isMobile={isMobile}
                        priceRange={projectsWithPrice?.find((pwp) => pwp?.id === p?.id)?.priceRange ?? ""}
                        isRTTProduct={isRRTProduct}
                      />
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Maybe>
        </Box>
      </Maybe>
    </Stack>
  );
};

export default ProductDetails;
