import Maybe from "@app/components/maybe/maybe";
import useIsMobile from "@app/providers/mobile";
import { <PERSON>rid, <PERSON>ack, Divider, Typography, Grow, Box } from "@mui/material";
import { useRef, useState, useMemo, useEffect, Dispatch, SetStateAction } from "react";
import { CommonProductProps } from "../types/product";
import {
  GroupedAllocationWithNestedResponse,
  Maybe as MaybeType,
  TrimmedProjectResponse,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import { ArrowDownwardRounded, ArrowForwardRounded } from "@mui/icons-material";
import HeaderAndContentSection from "@app/components/header-and-content-section/header-and-content-section";
import GenericPieChart, { PieDataItem } from "@app/components/generic-pie-chart/generic-pie-chart";
import { GenericTabKey, GenericTabs, useScroll } from "@rubiconcarbon/frontend-shared";

import classes from "../styles/product-explorer.module.scss";

type ExpandDirection = "right" | "bottom";

type PieProps = {
  type: string;
  data: PieDataItem[];
  selectedSection: string;
  expandDirection: ExpandDirection;
  onSectionChange: Dispatch<SetStateAction<string>>;
};

type AllocationItemRecordWithoutSegment = Record<string, { percentage: number }>;

type AllocationItemSegment<S extends string> = Record<S, AllocationItemRecordWithoutSegment>;

type AllocationItemRecordWithSegment<S extends string> = Record<
  string,
  { percentage: number } & AllocationItemSegment<S>
>;

type AllocationItem<F extends string, S extends string> = Record<F, AllocationItemRecordWithSegment<S>>;

type Allocation<F extends string, S extends string> = AllocationItem<F, S>;

type BreakDownProps = {
  header: string;
  columns: string[];
  data: Record<string, any>[];
};

type VizualizationProps = {
  activeTab: "category" | "geography";
  allocation: MaybeType<Allocation<"categories", "types"> & Allocation<"regions", "countries">>;
};

const BreakDown = ({ header, columns, data }: BreakDownProps): JSX.Element => {
  return (
    <Stack className={classes.Breakdown} gap={2}>
      <Typography variant="bodyCopyB">{header}</Typography>
      <Grid container flexDirection="row" gap={1}>
        <Grid item xs={12} container justifyContent="space-between">
          {columns.map((column, index) => (
            <Grid
              key={column}
              item
              xs={index === 0 ? 8 : 4}
              container
              justifyContent={index === 0 ? "flex-start" : "flex-end"}
            >
              <Typography variant="body2" textTransform="capitalize">
                {column}
              </Typography>
            </Grid>
          ))}
        </Grid>
        <Divider sx={{ width: "100%" }} />
        <Grid item xs={12} container justifyContent="space-between" gap={1}>
          {data.map((row) =>
            columns.map((column, index) => (
              <Grid
                key={column}
                item
                xs={index === 0 ? 7 : 4}
                container
                justifyContent={index === 0 ? "flex-start" : "flex-end"}
                alignItems="center"
              >
                <Typography variant="body2">{row?.[column]}</Typography>
              </Grid>
            )),
          )}
        </Grid>
      </Grid>
    </Stack>
  );
};

const Pie = ({ type, data, selectedSection, expandDirection, onSectionChange }: PieProps): JSX.Element => {
  const isMobile = useIsMobile(500);

  return (
    <GenericPieChart
      name="basket-pie"
      title={`Inventory by ${type === "geography" ? "region" : "category"}`}
      size="60%"
      donut="40%"
      separate={2}
      selectable
      data={data}
      label={{
        alignTo: "edge",
        breathingRoom: 10,
        template: {
          b: {
            content: "{b}",
            style: {
              align: "left",
              fontSize: isMobile ? 10 : 13,
              fontWeight: "bold",
              padding: [0, 0, 8, 0],
            },
            newLine: true,
          },
          d: {
            content: "{d}%",
            style: {
              align: "left",
              fontSize: isMobile ? 10 : 13,
            },
          },
        },
      }}
      tooltip={{
        template: (params): JSX.Element => {
          const { name, color } = params;
          const expand = name !== selectedSection;

          return (
            <Stack gap={1} sx={{ padding: 1, color: "black" }}>
              <Stack direction="row" alignItems="center" gap={1}>
                <Typography variant="h5" fontWeight="bold">
                  Click to {expand ? "expand" : "collapse"}
                </Typography>
                <Maybe condition={expandDirection === "right"}>
                  <ArrowForwardRounded fontSize="large" sx={{ rotate: expand ? "0deg" : "180deg" }} />
                </Maybe>
                <Maybe condition={expandDirection === "bottom"}>
                  <ArrowDownwardRounded fontSize="large" sx={{ rotate: expand ? "0deg" : "180deg" }} />
                </Maybe>
              </Stack>
              <Stack direction="row" alignItems="center" gap={1}>
                <Box sx={{ width: 20, height: 20, backgroundColor: color, borderRadius: 1 }}></Box>
                <Typography variant="h5">{name}</Typography>
              </Stack>
            </Stack>
          );
        },
        inlineStyleText: "border-radius: 10px; border-color: transparent",
      }}
      hover={{
        focus: "self",
        blurRestOf: "series",
      }}
      select={{
        unSelectedSegmentStyle: {
          opacity: 0.7,
        },
      }}
      style={{
        width: 500,
        height: 500,
      }}
      events={{
        selectchanged: (action): void => {
          const { selected } = action;
          const index = selected?.at(0)?.dataIndex?.at(0);
          onSectionChange(index !== undefined ? (data.at(index)?.name ?? "") : "");
        },
      }}
    />
  );
};

const Vizualization = ({ activeTab, allocation }: VizualizationProps): JSX.Element => {
  const isMobile = useIsMobile(1199);

  const container = useRef<HTMLDivElement>(null);
  const breakdown = useRef<HTMLElement>(null);

  const { scrollIntoView } = useScroll(container);

  const [selectedSection, setSelectedSection] = useState<string>("");

  const breakdownType = activeTab === "category" ? "type" : "country";

  const pieData = useMemo(
    () =>
      Object.entries(allocation ?? {}).reduce((record, [kind, breakdown]) => {
        record = {
          ...record,
          [kind]: Object.entries(breakdown).map(([name, { percentage }]) => ({
            name,
            value: percentage,
            selected: name === selectedSection,
          })),
        };

        return record;
      }, {}) as { categories: PieDataItem[]; regions: PieDataItem[] },
    [allocation, selectedSection],
  );

  const breakdownData = useMemo(
    () =>
      Object.entries(allocation ?? {}).reduce(
        (kindRecord, [kind, breakdown]) => ({
          ...kindRecord,
          [kind]: Object.entries(breakdown).reduce(
            (
              breakdownRecord,
              [name, { [kind === "regions" ? "countries" : "types"]: sections }]: [
                string,
                AllocationItemSegment<"types" | "countries">,
              ],
            ) => ({
              ...breakdownRecord,
              [name]: Object.entries(sections).map(([key, { percentage }]) => ({
                [kind === "regions" ? "country" : "type"]: key,
                composition: `${((percentage / 1) * 100).toFixed(2)}%`,
              })),
            }),
            {},
          ),
        }),
        {},
      ) as { categories: Record<string, []>; regions: Record<string, []> },
    [allocation],
  );

  const currentPieData = useMemo(
    () => pieData[activeTab === "category" ? "categories" : "regions"],
    [activeTab, pieData],
  );

  const currentBreadownData = useMemo(
    () => breakdownData[activeTab === "category" ? "categories" : "regions"]?.[selectedSection],
    [activeTab, breakdownData, selectedSection],
  );

  useEffect(() => {
    if (isMobile && !!selectedSection && !!breakdown) scrollIntoView(breakdown, "nearest", "nearest");
  }, [isMobile, scrollIntoView, selectedSection]);

  return (
    <Grid
      ref={container}
      component={Box}
      className={classes.VisualizationAndAllocation}
      item
      xs={12}
      lg={10}
      container
      gap={1}
      justifyContent="center"
      alignItems="center"
    >
      <Grid item xs={isMobile || !currentBreadownData ? 12 : 7} container justifyContent="center">
        <Pie
          type={activeTab?.toString()}
          data={currentPieData}
          selectedSection={selectedSection}
          expandDirection={isMobile ? "bottom" : "right"}
          onSectionChange={setSelectedSection}
        />
      </Grid>
      <Maybe condition={!!currentBreadownData}>
        <Grow in={!!currentBreadownData} unmountOnExit>
          <Grid className={classes.BreakdownContainer} ref={breakdown} component={Box} item xs={isMobile ? 12 : 4}>
            <BreakDown
              header={`Breakdown by ${breakdownType}`}
              columns={[breakdownType, "composition"]}
              data={currentBreadownData}
            />
          </Grid>
        </Grow>
      </Maybe>
    </Grid>
  );
};

const ProductExplorer = ({ product }: CommonProductProps): JSX.Element => {
  const isMobile = useIsMobile(1199);

  const [activeTab, setActiveTab] = useState<"category" | "geography">("geography");

  const total = product?.ownerAllocations?.totalAmountAllocated as number;

  const allocation: MaybeType<Allocation<"categories", "types"> & Allocation<"regions", "countries">> = useMemo(
    () =>
      (product?.ownerAllocations as GroupedAllocationWithNestedResponse).allocations
        .map(
          (x) =>
            [(x.detailedAsset as TrimmedProjectVintageResponse).project, x.amountAllocated] as [
              TrimmedProjectResponse,
              number,
            ],
        )
        .reduce(
          (accum, [project, amount]) => {
            const { categories, regions } = accum;
            const { country, projectType } = project || {};
            const { category, type: pType } = projectType || {};
            const { region, name } = country || {};

            if (!!category) {
              const root = categories[category] || {};
              const types = root.types || {};
              let type;

              if (!!pType) {
                type = types[pType] || {};
                type.percentage = (type.percentage || 0) + amount / total;

                types[pType] = type;
              }

              root.percentage = (root.percentage || 0) + amount / total;
              root.types = types;

              categories[category] = root;
            }

            if (!!region) {
              const root = regions[region] || {};
              const countries = root.countries || {};
              let country;

              if (!!name) {
                country = countries[name] || {};
                country.percentage = (country.percentage || 0) + amount / total;

                countries[name] = country;
              }

              root.percentage = (root.percentage || 0) + amount / total;
              root.countries = countries;

              regions[region] = root;
            }

            return {
              categories,
              regions,
            };
          },
          {
            categories: {},
            regions: {},
          } as Allocation<"categories", "types"> & Allocation<"regions", "countries">,
        ),
    [product?.ownerAllocations, total],
  );

  return (
    <HeaderAndContentSection
      header="Explore"
      content={
        <Grid container className={classes.ProductExplorer} flexDirection="row">
          <Grid item xs={12} lg={2}>
            <GenericTabs
              orientation={isMobile ? "horizontal" : "vertical"}
              tabs={[
                {
                  key: "category",
                  data: "By category",
                },
                {
                  key: "geography",
                  data: "By geography",
                },
              ]}
              value={activeTab}
              renderTab={(data: any, key?: GenericTabKey): JSX.Element => (
                <Stack
                  direction={isMobile ? "column" : "row"}
                  gap={isMobile ? 1 : 2}
                  justifyContent={isMobile ? "center" : "flex-start"}
                  alignItems="center"
                >
                  <Maybe condition={key === activeTab && !isMobile}>
                    <Divider className={classes.Divider} orientation="vertical" flexItem />
                  </Maybe>
                  <Typography
                    sx={{
                      paddingLeft: key !== activeTab && !isMobile ? 2.4 : 0,
                      paddingBottom: key !== activeTab && isMobile ? 1.4 : 0,
                    }}
                    textAlign={isMobile ? "center" : "start"}
                  >
                    {data}
                  </Typography>
                  <Maybe condition={key === activeTab && isMobile}>
                    <Divider className={classes.Divider} flexItem />
                  </Maybe>
                </Stack>
              )}
              classes={{
                tab: isMobile ? classes.HorizontalTab : classes.VerticalTab,
                active: isMobile ? classes.HorizontalActive : classes.VerticalActive,
              }}
              onTabChange={(key: GenericTabKey): void => setActiveTab(key as "category" | "geography")}
            />
          </Grid>
          <Vizualization activeTab={activeTab} allocation={allocation} />
        </Grid>
      }
      sx={{ backgroundColor: "white" }}
    />
  );
};

export default ProductExplorer;
