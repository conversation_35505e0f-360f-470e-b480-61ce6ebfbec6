import { Grid, Typography } from "@mui/material";
import { CommonProductProps } from "../types/product";
import ProjectCard from "./project-card";
import HeaderAndContentSection from "@app/components/header-and-content-section/header-and-content-section";
import { useMemo } from "react";
import {
  GroupedAllocationWithNestedResponse,
  TrimmedProjectResponse,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";

const Projects = ({ product }: CommonProductProps): JSX.Element => {
  const productId = useMemo(() => product?.id, [product?.id]);
  const projects: [TrimmedProjectResponse, number][] = useMemo(
    () =>
      (product?.ownerAllocations as GroupedAllocationWithNestedResponse).allocations.map((x) => [
        (x.detailedAsset as TrimmedProjectVintageResponse).project as TrimmedProjectResponse,
        x.amountAllocated,
      ]),
    [product?.ownerAllocations],
  );

  const total = product?.ownerAllocations?.totalAmountAllocated as number;

  return (
    <HeaderAndContentSection
      header="Projects"
      content={
        <Grid container justifyContent="space-between" gap={{ xs: 3 }}>
          <Grid item xs={12}>
            <Typography>
              Rubicon Carbon has evaluated hundreds of projects to ensure our products include the highest quality
              projects.
            </Typography>
          </Grid>
          <Grid item xs={12} container rowGap={3}>
            {projects
              ?.sort((a, b) => {
                if (b[1] !== a[1]) return b[1] - a[1];
                return b[0].name.localeCompare(a[0].name);
              })
              .map(([project, amount]) => (
                <ProjectCard
                  key={project.id}
                  proportionOfBook={amount / total}
                  productId={productId!}
                  project={project}
                />
              ))}
          </Grid>
        </Grid>
      }
    />
  );
};

export default Projects;
