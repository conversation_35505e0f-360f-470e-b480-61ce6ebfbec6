import "server-only";

import { BookRelations, PortalBookResponse, uuid } from "@rubiconcarbon/shared-types";
import { getToken } from "@app/providers/auth-provider/server";
import { isUUID } from "class-validator";
import { notFound } from "next/navigation";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { getBook } from "@app/app/data-server";

export async function getProduct(id: uuid): Promise<MaybeNothing<PortalBookResponse>> {
  const token = getToken();

  if (!token) return;

  if (!isUUID(id)) return notFound();

  return getBook(id, {
    includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED],
  });
}
