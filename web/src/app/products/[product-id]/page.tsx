import { uuid } from "@rubiconcarbon/shared-types";
import { getProduct } from "./data-server";
import { getUser, RequirePermissions } from "@app/providers/auth-provider/server";
import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import ProductDetails from "./components/product-details";
import { Suspense } from "react";
import Error from "@app/app/error";
import RRTDisclaimer from "./components/rrt-disclaimer";
import { ProductIds } from "@app/constants/products";

type ProductPageProps = {
  params: { "product-id": uuid };
};

const ProductPage = async ({ params }: ProductPageProps): Promise<JSX.Element> => {
  try {
    const product = await getProduct(params["product-id"]);

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <ProductDetails product={product} />
          <RRTDisclaimer isRRTProduct={product?.id === ProductIds.RUBICON_RATED_TONNE} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
};

export default ProductPage;
