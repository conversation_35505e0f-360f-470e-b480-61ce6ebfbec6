import Error from "@app/app/error";
import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import { Suspense } from "react";
import { getProject } from "@app/app/projects/[project-id]/data-server";
import ProjectDetails from "@app/app/projects/[project-id]/components/project-details";

type ProjectPageProps = {
  params: {
    "product-id": string;
    "project-id": string;
  };
};

const ProjectPage = async ({ params }: ProjectPageProps): Promise<JSX.Element> => {
  try {
    const productId = params["product-id"];
    const projectId = params["project-id"];
    const project = await getProject(projectId, { includeRelations: ["country", "projectSDGs", "projectType"] });

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <ProjectDetails productId={productId} project={project} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
};

export default ProjectPage;
