"use client";

import { Box, Stack } from "@mui/material";
import PurchaseStepper from "./purchase-stepper";
import {
  EmailPurchaseRequest,
  UserActionDataType,
  PortalBookResponse,
  GroupedAllocationWithNestedResponse,
  TrimmedProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Match } from "@rubiconcarbon/frontend-shared";
import { useCallback, useState } from "react";
import PurchaseForm from "./purchase-form";
import { PurchaseModel } from "../models/purchase";
import { useRouter } from "next/navigation";
import PurchaseReview from "./purchase-review";
import PurchaseRequestSummary from "./purchase-request-summary";
import useTriggerRequest from "@app/hooks/useTriggerRequest";
import { useLogger } from "@app/providers/logging-provider";
import { useAxios } from "@app/providers/axios-provider";
import { useMeasure } from "react-use";

import classes from "../styles/purchase.module.scss";

type PurchaseProps = {
  product: PortalBookResponse;
};

const STEPS = ["Configuration", "Review", "Request Live Quote", "Back to Products"];

const Purchase = ({ product }: PurchaseProps): JSX.Element => {
  const { replace } = useRouter();
  const { logger } = useLogger();
  const { api } = useAxios();
  const [contentContainer, { height: contentContainerHeight }] = useMeasure<HTMLDivElement>();

  const [step, setStep] = useState<number>(0);
  const [form, setForm] = useState<PurchaseModel>({ rctId: product?.id });

  const skipLiveQuote = step === 1;
  const backToProduct = step === 3;

  const { trigger, isMutating } = useTriggerRequest<any, EmailPurchaseRequest>({
    url: "emails/purchase",
    method: "post",
    requestBody: form as EmailPurchaseRequest,
    swrOptions: {
      onSuccess: () => {
        setStep(step + 2);
        logger.info(`Successfully create a purchase quote for portfolio: ${form?.rctId}`, {});
      },
      onError: (error) => {
        logger.error(`Error creating a purchase quote: ${error?.message}`, {});
      },
    },
  });

  const logUserAction = useCallback(async () => {
    const { purchasePrice, purchasePriceWithBuffer } = product || {};

    const action: UserActionDataType = {
      version: 1,
      type: "rct_quote_request",
      data: {
        productId: form.rctId,
        price: Number(form?.isRiskAdjusted ? purchasePriceWithBuffer : purchasePrice),
        quantity: form.amount!,
        riskAdjusted: form.isRiskAdjusted!,
        forRetirement: form.isPurchaseToRetire!,
        projectIds: (product.ownerAllocations as GroupedAllocationWithNestedResponse).allocations!.map(
          (x) => (x.detailedAsset as TrimmedProjectVintageResponse).project?.id as uuid,
        ),
        percentages: (product.ownerAllocations as GroupedAllocationWithNestedResponse).allocations!.map(
          (x) => x.amountAllocated / (product.ownerAllocations?.totalAmountAllocated as number),
        ),
      },
    };
    return api.post("/user-actions", action);
  }, [api, form.amount, form.isPurchaseToRetire, form.isRiskAdjusted, form.rctId, product]);

  const handlePreviousStep = (form: PurchaseModel): void => {
    setForm(form);
    setStep(step - 1);
  };

  const handleNextStep = async (form: PurchaseModel): Promise<void> => {
    setForm(form);
    if (!backToProduct) {
      if (skipLiveQuote) {
        await trigger();
        await logUserAction();
      } else setStep(step + 1);
    } else replace("/overview");
  };

  if (!product) return <></>;

  return (
    <Stack className={classes.Container} gap={3} height={`calc(100% + ${contentContainerHeight}px)`}>
      <PurchaseStepper active={step} steps={STEPS} />
      <Box ref={contentContainer} className={classes.Paper}>
        <Match
          value={step}
          cases={[
            {
              case: 0,
              component: <PurchaseForm product={product} form={form} onNext={handleNextStep} />,
            },
            {
              case: 1,
              component: (
                <PurchaseReview
                  product={product}
                  form={form}
                  submitting={isMutating}
                  onPrevious={handlePreviousStep}
                  onNext={handleNextStep}
                />
              ),
            },
            {
              case: 3,
              component: <PurchaseRequestSummary product={product} form={form} onNext={handleNextStep} />,
            },
          ]}
        />
      </Box>
    </Stack>
  );
};

export default Purchase;
