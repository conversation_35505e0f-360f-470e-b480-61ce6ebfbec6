import { BookRelations, BookType, uuid } from "@rubiconcarbon/shared-types";
import { ImageMinitronProductsData, reconcileProduct } from "@app/constants/image-minitron-products-data";
import { getBook } from "@app/app/data-server";
import ProductTitle from "@app/components/product-title/product-title";
import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import ImageJumbotron from "@app/components/image-jumbotron/image-jumbotron";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import { Jumbotron } from "@app/types/static-content";
import { Suspense } from "react";
import Purchase from "./components/purchase";
import { ProductIds } from "@app/constants/products";

import rubiconRatedTonneLandscape from "@assets/images/rubicon-rated-tonne-landscape.webp";

type PurchasePageProps = {
  params: { "product-id": uuid };
};

const PurchasePage = async ({ params }: PurchasePageProps): Promise<JSX.Element> => {
  const { "product-id": id } = params;

  const book = await getBook(id, {
    includeRelations: [BookRelations.OWNER_ALLOCATIONS_BY_PROJECT, BookRelations.OWNER_ALLOCATIONS_NESTED],
  });

  if (!book) return <></>;

  const product = reconcileProduct(book, ImageMinitronProductsData, {
    imageOverride: (_, minitron) =>
      minitron?.text === ProductIds.RUBICON_RATED_TONNE ? rubiconRatedTonneLandscape : undefined,
  });

  const header = {
    images: [product?.image],
    text: <ProductTitle value={product?.title} trademark={id !== ProductIds.RUBICON_RATED_TONNE} />,
    subText: "Purchase Request",
    fullWidth: true,
  };

  return (
    <RequirePermissions
      permissions={["verified"]}
      guard={{
        canActivate: () => !!book?.isEnabled && book?.type !== BookType.PORTFOLIO_CUSTOM,
        requirementNotMet: "You cannot purchase custom portfolios.",
      }}
      user={getUser()}
      forbidden={<ForbiddenErrorPage />}
      unauthenticated={<UnauthenticatedErrorPage />}
    >
      <div className="no-padding">
        <ImageJumbotron
          data={header as Jumbotron}
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            backgroundBlendMode: "darken",
            height: 300,
          }}
        />
      </div>
      <Suspense>
        <Purchase product={book} />
      </Suspense>
    </RequirePermissions>
  );
};

export default PurchasePage;
