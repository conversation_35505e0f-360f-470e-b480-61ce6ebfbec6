.Container {
    display: flex;
    flex-direction: column;
    background-color: #DFF5F2;

    .Skeleton {
        padding: 24px;
        > * {
            width: 100%;
        }
    }
}

div:has(.Container) {
    gap: 0;
}

.RCTContainer {
    padding-left: 24px;
    padding-right: 24px;
    padding-top: 20px !important;
    padding-bottom: 24px;
}

.mainInfo {
    font-weight: 500;
    color: #000000;
}

.supportiveInfo {
    font-weight: 400;
    color: rgba(158, 158, 158, 1);
}