"use client";

import ProjectsSelection from "./projects-selection";
import { Box, Grid, Button, Stack, Tooltip, CircularProgress, DialogContentText } from "@mui/material";
import {
  uuid,
  Maybe as MaybeType,
  CustomPricingResponse,
  CustomPricingRequest,
  UserActionDataType,
  ErrorResponse,
  PortalProjectResponse,
} from "@rubiconcarbon/shared-types";
import { useEffect, useState, useCallback, useRef, useContext, useMemo } from "react";
import StepsIndicator, { Indicator, StepsStatus } from "@app/components/steps-indicator/steps-indicator";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import Maybe from "@app/components/maybe/maybe";
import ConfigureRCT from "./rct-configuration";
import { StoreContext } from "@app/providers/state/store";
import RCTSummary from "./rct-summary";
import { AxiosContext } from "@app/providers/axios-provider";
import InformationDialog from "@app/components/dialog/information-dialog";
import COLORS from "@app/theme/colors";
import { BooleanFormField, NumericFormField, RCTData, RCTProject } from "./model";
import { fillVintagesEvenly, getTotalAttribute, isSelectionValid, recalculateVintagesPercents } from "./utils";
import { BYO_SELECTED_PROJECTS_STATE_KEY, BYOSteps } from "@app/constants/byo";
import RegisteredTrademark from "./registered-trademark/registered-trademark";
import classes from "./styles.module.scss";

interface CustomRCTProps {
  byoProjects?: MaybeType<PortalProjectResponse[]>;
}

export default function CustomRCT(props: CustomRCTProps): JSX.Element {
  const { persistentState, updatePersistentState } = useContext(StoreContext);
  const { byoProjects } = props;
  const stepsRef = useRef<null | HTMLDivElement>(null);
  const [isLoadingEstimates, setIsLoadingEstimates] = useState<boolean>(false);
  const [isErrorModalOpen, setIsErrorModalOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const [pricingData, setPricingData] = useState<CustomPricingResponse | ErrorResponse>();
  const [steps, setSteps] = useState<Indicator[]>([
    { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.CURRENT },
    {
      name: "2",
      label: (
        <span>
          {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
        </span>
      ),
      status: StepsStatus.TODO,
    },
    { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.TODO },
  ]);

  const [customRCTData, setCustomRCTData] = useState<RCTData>({
    includeRiskAdj: {
      value: undefined,
      error: false,
      message: "",
    },
    retireAtPurchase: {
      value: undefined,
      error: false,
      message: "",
    },
  });
  const [allowSummaryMsg, setAllowSummaryMsg] = useState<string>();
  const [allowSummaryStep, setAllowSummaryStep] = useState<boolean>(false);
  const [tonnesRequested, setTonnesRequested] = useState<NumericFormField>({
    value: 0,
    error: false,
    message: "",
  });

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  // track changes in selected projects and update project locations in ephemeral state
  /*
  useTracked({
    value: selectedProjects,
    onChange: ({ current }) => {
      const projectLocations = Array.from(new Set(current?.map((p) => p.country?.name)));
      updateEphemeralState("custom-rct.projectLocations", projectLocations, { upsert: true });
    },
  });
  */

  const buildRequestBody = useCallback((): CustomPricingRequest | undefined => {
    if (
      !!customRCTData &&
      !!selectedProjects &&
      customRCTData?.includeRiskAdj?.value !== undefined &&
      customRCTData?.retireAtPurchase?.value !== undefined &&
      selectedProjects.length > 0
    ) {
      const allocations: { vintageId: uuid; quantity: number }[] = [];

      selectedProjects.forEach((p) => {
        p?.projectVintages?.forEach((pv) => {
          if (pv.selected) {
            allocations.push({
              vintageId: pv.id,
              quantity: pv.co2eTonnes ?? 0,
            });
          }
        });
      });

      const requestBody: CustomPricingRequest = {
        allocation: allocations,
        riskAdjusted: customRCTData.includeRiskAdj.value,
        forRetirement: customRCTData.retireAtPurchase.value,
      };

      return requestBody;
    }
    return undefined;
  }, [customRCTData, selectedProjects]);

  const getTotalValue = useCallback((): number => {
    if (!!selectedProjects) {
      return selectedProjects?.reduce(
        (accumulator, currentValue) =>
          accumulator + (!!currentValue && currentValue?.co2eTonnes ? currentValue?.co2eTonnes : 0),
        0,
      );
    } else {
      return 0;
    }
  }, [selectedProjects]);

  const getSummaryEnabledValue = useCallback(
    (data: RCTData): boolean => {
      if (!!tonnesRequested && tonnesRequested?.value < 1000) {
        setAllowSummaryMsg("Tonnes requested for purchase value should be greater or equal to 1,000");
        return false;
      }

      if (getTotalValue() !== tonnesRequested?.value) {
        setAllowSummaryMsg("Tonnes requested for purchase value should be equal to total of tonnes of co₂e");
        return false;
      }

      if (selectedProjects?.some((p) => !p.co2eTonnes || p.co2eTonnes === 0)) {
        setAllowSummaryMsg("Project tonnes value should be greater than 0");
        return false;
      }

      if (data?.includeRiskAdj?.value === undefined) {
        setAllowSummaryMsg("Include risk adjustment must be selected");
        return false;
      }

      if (data?.retireAtPurchase?.value === undefined) {
        setAllowSummaryMsg("Retire at purchase must be selected");
        return false;
      }

      if (!isSelectionValid(selectedProjects)) {
        setAllowSummaryMsg("Selected vintage value must be greater than 0");
        return false;
      }

      return true;
    },
    [tonnesRequested, getTotalValue, selectedProjects],
  );

  useEffect(() => {
    setAllowSummaryStep(getSummaryEnabledValue(customRCTData));
  }, [customRCTData, tonnesRequested, getSummaryEnabledValue]);

  const stepHandler = (steps: Indicator[]): void => {
    setSteps(steps);
    if (!!stepsRef && !!stepsRef.current) stepsRef.current.scrollIntoView();
  };

  const addProjectsHandler = (): void => {
    stepHandler([
      { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.CURRENT },
      {
        name: "2",
        label: (
          <span>
            {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
          </span>
        ),
        status: StepsStatus.TODO,
      },
      { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.TODO },
    ]);
  };

  const changeHandler = useCallback(
    (id: uuid, fieldName: string, value: number, vintageId: uuid): void => {
      if (!!selectedProjects) {
        const newProjects = selectedProjects.map((p) => p);
        const updatedProject = newProjects?.find((p) => p.id === id);
        const updatedVintage = updatedProject?.projectVintages?.find((v) => v.id === vintageId);

        if (!!updatedVintage) {
          if (fieldName === "co2eTonnes") {
            const vintagePercent = (value * 100) / tonnesRequested.value;
            updatedVintage.co2eTonnes = Math.round(value);
            updatedVintage.rctPercent = vintagePercent;
          } else {
            const vintageTonnes = (value * tonnesRequested.value) / 100;
            updatedVintage.rctPercent = value;
            updatedVintage.co2eTonnes = Math.round(vintageTonnes);
          }
          if (!!updatedProject && !!updatedProject?.projectVintages) {
            updatedProject.co2eTonnes = getTotalAttribute(updatedProject?.projectVintages, "co2eTonnes");
            updatedProject.rctPercent = getTotalAttribute(updatedProject?.projectVintages, "rctPercent");
          }
          // saving to persistent store
          updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, newProjects);
        }
      }
    },
    [selectedProjects, tonnesRequested.value, updatePersistentState],
  );

  const distributeBetweenProjects = useCallback((): void => {
    if (!!selectedProjects && selectedProjects.length > 0) {
      const value = tonnesRequested.value / selectedProjects.length;
      const roundedValue = Math.round(value);
      const length = selectedProjects.length - 1;
      const calculatedRCTPercent = (roundedValue * 100) / tonnesRequested.value;

      //Dividing evenly between projects
      const currentProjects = selectedProjects?.map((p, idx) => {
        if (idx === length) {
          const lastValue = tonnesRequested.value - roundedValue * length;
          const lastRCTPercent = (lastValue * 100) / tonnesRequested.value;

          return {
            ...p,
            co2eTonnes: lastValue,
            rctPercent: lastRCTPercent,
            projectVintages: fillVintagesEvenly(lastValue, lastRCTPercent, p?.projectVintages),
          };
        }

        return {
          ...p,
          co2eTonnes: roundedValue,
          rctPercent: calculatedRCTPercent,
          projectVintages: fillVintagesEvenly(roundedValue, calculatedRCTPercent, p?.projectVintages),
        };
      });

      // saving to persistent store
      updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, currentProjects);
    }
  }, [selectedProjects, tonnesRequested.value, updatePersistentState]);

  const distributeBetweenVintages = useCallback((): void => {
    if (!!selectedProjects && selectedProjects.length > 0) {
      //the number of checked vintages
      let checkedVintagesCount = 0;
      selectedProjects.forEach((p) =>
        p?.projectVintages?.forEach((pv) => {
          if (pv.selected) {
            checkedVintagesCount++;
          }
        }),
      );

      const roundedValue = Math.round(tonnesRequested.value / checkedVintagesCount);
      const calculatedRCTPercent = (roundedValue * 100) / tonnesRequested.value;
      //Dividing evenly between vintages

      let counter = 0;

      const currentProjects = selectedProjects?.map((p) => {
        const updatedVintages = p?.projectVintages?.map((pv) => {
          if (pv.selected) {
            counter = counter + 1;
            if (counter === checkedVintagesCount) {
              //last vintage
              const lastValue = tonnesRequested.value - roundedValue * (checkedVintagesCount - 1);
              const lastRCTPercent = (lastValue * 100) / tonnesRequested.value;
              return {
                ...pv,
                co2eTonnes: lastValue,
                rctPercent: lastRCTPercent,
              };
            }

            return {
              ...pv,
              co2eTonnes: roundedValue,
              rctPercent: calculatedRCTPercent,
            };
          } else {
            return pv;
          }
        });

        return {
          ...p,
          projectVintages: updatedVintages,
          co2eTonnes: getTotalAttribute(updatedVintages, "co2eTonnes"),
          rctPercent: getTotalAttribute(updatedVintages, "rctPercent"),
        };
      });

      // saving to persistent store
      updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, currentProjects);
    }
  }, [selectedProjects, tonnesRequested.value, updatePersistentState]);

  const autoFillHandler = useCallback(
    (entity: string): void => {
      if (!!selectedProjects && selectedProjects.length > 0) {
        if (entity === "projects") {
          distributeBetweenProjects();
        } else {
          distributeBetweenVintages();
        }
      }
    },
    [distributeBetweenProjects, distributeBetweenVintages, selectedProjects],
  );

  const resetHandler = useCallback((): void => {
    if (!!selectedProjects && selectedProjects.length > 0) {
      const currentProjects = selectedProjects?.map((p) => {
        return {
          ...p,
          co2eTonnes: 0,
          rctPercent: 0,
          projectVintages: p?.projectVintages?.map((pv) => {
            return {
              ...pv,
              rctPercent: 0,
              co2eTonnes: 0,
            };
          }),
        };
      });
      // saving to persistent store
      updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, currentProjects);
    }
  }, [selectedProjects, updatePersistentState]);

  const deleteHandler = useCallback(
    (id: uuid): void => {
      if (!!selectedProjects && selectedProjects.length > 0) {
        const currentProjects = selectedProjects?.filter((p) => p.id !== id);
        // saving to persistent store
        updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, currentProjects);
      }
    },
    [selectedProjects, updatePersistentState],
  );

  const tonnesHandler = useCallback(
    (newTonnes: NumericFormField): void => {
      const newTotal = newTonnes.value;

      //recalculating percent values
      if (!!selectedProjects && selectedProjects.length > 0) {
        const newProjects = recalculateVintagesPercents(selectedProjects, newTotal);

        // saving to persistent store
        updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, newProjects);
        setTonnesRequested(newTonnes);
      }
    },
    [setTonnesRequested, selectedProjects, updatePersistentState],
  );

  const riskAdjHandler = useCallback(
    (newValue: BooleanFormField): void => {
      setCustomRCTData({ ...customRCTData, includeRiskAdj: newValue });
    },
    [customRCTData],
  );

  const retireAtPurchaseHandler = useCallback(
    (newValue: BooleanFormField): void => {
      setCustomRCTData({ ...customRCTData, retireAtPurchase: newValue });
    },
    [customRCTData],
  );

  const priceChangeHandler = useCallback(
    (customPricingResponse: CustomPricingResponse | undefined): void => {
      setCustomRCTData({ ...customRCTData, customPricingResponse: customPricingResponse });
    },
    [setCustomRCTData, customRCTData],
  );

  const getPriceEstimate = useCallback(async (): Promise<void> => {
    setIsLoadingEstimates(true);
    const requestBody = buildRequestBody();

    try {
      const response = await api.post<CustomPricingResponse>(`books/pricing`, requestBody);
      setPricingData(response.data);
      stepHandler([
        { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.DONE },
        {
          name: "2",
          label: (
            <span>
              {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
            </span>
          ),
          status: StepsStatus.DONE,
        },
        { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.CURRENT },
      ]);
    } catch (error) {
      setIsErrorModalOpen(true);
    } finally {
      setIsLoadingEstimates(false);

      const action: UserActionDataType = {
        version: 1,
        type: "byorct_price_estimate",
        data: {
          forRetirement: requestBody?.forRetirement ?? false,
          riskAdjusted: requestBody?.riskAdjusted ?? false,
          vintageIds: requestBody?.allocation.map((x) => x.vintageId) ?? [],
          quantities: requestBody?.allocation.map((x) => x.quantity) ?? [],
        },
      };
      api.post("/user-actions", action).then();
    }
  }, [api, buildRequestBody]);

  return (
    <>
      <Box ref={stepsRef} mt={2} mb={2}>
        <StepsIndicator steps={steps} />
      </Box>
      <Maybe condition={steps[0].status === StepsStatus.CURRENT}>
        <ProjectsSelection projects={byoProjects} stepHandler={stepHandler} />
      </Maybe>
      <Maybe condition={steps[1].status === StepsStatus.CURRENT}>
        <Grid container direction="row" className={classes.rctContainer}>
          <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
            <Box className={classes.rctBox} sx={{ width: "100%" }}>
              <ConfigureRCT
                projects={selectedProjects}
                availableProjects={byoProjects}
                onAddProjects={addProjectsHandler}
                changeHandler={changeHandler}
                onAutoFill={autoFillHandler}
                onReset={resetHandler}
                deleteHandler={deleteHandler}
                tonnesHandler={tonnesHandler}
                retireAtPurchaseHandler={retireAtPurchaseHandler}
                riskAdjHandler={riskAdjHandler}
                tonnesRequested={tonnesRequested}
                includeRiskAdj={customRCTData.includeRiskAdj}
                retireAtPurchase={customRCTData.retireAtPurchase}
              />
            </Box>
          </Grid>
          <Grid
            className={classes.nextButtonContainer}
            item
            xs={12}
            container
            direction="row"
            sx={{ position: "relative", flexGrow: "100%" }}
          >
            <Stack direction="row" spacing={2}>
              <Button
                className={classes.backButton}
                variant="contained"
                startIcon={<ArrowBackIosIcon />}
                onClick={(): void =>
                  stepHandler([
                    { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.CURRENT },
                    {
                      name: "2",
                      label: (
                        <span>
                          {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
                        </span>
                      ),
                      status: StepsStatus.TODO,
                    },
                    { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.TODO },
                  ])
                }
              >
                Previous: Select your Projects
              </Button>
              <Tooltip title={!allowSummaryStep ? allowSummaryMsg : ""} placement="top">
                <span>
                  <Button
                    className={allowSummaryStep ? classes.nextButton : classes.nextButtonDisabled}
                    disabled={!allowSummaryStep || isLoadingEstimates}
                    variant="contained"
                    endIcon={!isLoadingEstimates && <ArrowForwardIosIcon />}
                    startIcon={
                      isLoadingEstimates && <CircularProgress size="22px" variant="indeterminate" thickness={6} />
                    }
                    onClick={getPriceEstimate}
                  >
                    {isLoadingEstimates ? "Generating your quote..." : "Next: Get Price Estimate"}
                  </Button>
                </span>
              </Tooltip>
            </Stack>
          </Grid>
        </Grid>
      </Maybe>
      <Maybe
        condition={steps[2].status === StepsStatus.CURRENT && allowSummaryStep && !isLoadingEstimates && !!pricingData}
      >
        <RCTSummary
          customRCTData={customRCTData}
          priceChangeHandler={priceChangeHandler}
          stepHandler={stepHandler}
          pricingData={pricingData!}
        />
      </Maybe>
      <InformationDialog open={isErrorModalOpen} onClose={() => setIsErrorModalOpen(false)}>
        <DialogContentText
          component="p"
          color={COLORS.black}
          px="5rem"
          width="400px"
          textAlign="center"
          variant="bodyCopy"
        >
          {`We were unable to generate a quote at this time. Please try again later.`}
        </DialogContentText>
      </InformationDialog>
    </>
  );
}
