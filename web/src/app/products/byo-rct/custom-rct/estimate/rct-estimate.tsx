"use client";

import { useState, useMemo, useContext, useEffect } from "react";
import { Maybe as MaybeType, CustomPricingResponse, ErrorResponse } from "@rubiconcarbon/shared-types";
import RCTPriceEstimate, { PriceEstimate } from "./rct-price-estimate";
import Maybe from "@app/components/maybe/maybe";
import { StoreContext } from "@app/providers/state/store";
import classes from "../styles.module.scss";
import { Box, Grid, Grow } from "@mui/material";
import LiveQuoteRequest from "../live-quote-request";
import { Indicator } from "@app/components/steps-indicator/steps-indicator";
import { RCTData, RCTProject } from "../model";

const getTotalValue = (projects: RCTProject[]): number => {
  if (!!projects) {
    return projects?.reduce(
      (accumulator, currentValue) =>
        accumulator + (!!currentValue && currentValue?.co2eTonnes ? currentValue?.co2eTonnes : 0),
      0,
    );
  } else {
    return 0;
  }
};

interface RCTChartsProps {
  customData: RCTData;
  pricingData: CustomPricingResponse | ErrorResponse | undefined;
  portfolioName: string;
  onPriceChange: (customPricingResponse: CustomPricingResponse | undefined) => void;
  stepHandler: (steps: Indicator[]) => void;
}

export default function RCTEstimate(props: RCTChartsProps): JSX.Element {
  const { persistentState } = useContext(StoreContext);
  const { customData, pricingData, portfolioName, onPriceChange, stepHandler } = props;
  const [priceEstimateData, setPriceEstimateData] = useState<PriceEstimate>();

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  useEffect(() => {
    if (!!pricingData) {
      if (Object.hasOwn(pricingData, "error")) {
        onPriceChange(undefined);
        setPriceEstimateData({
          error: (pricingData as unknown as ErrorResponse).error,
          details: (pricingData as unknown as ErrorResponse)?.details,
        });
      } else {
        const data = pricingData as CustomPricingResponse;
        onPriceChange(data);
        const responseData: PriceEstimate = {
          ...data,
          total: !!selectedProjects ? getTotalValue(selectedProjects) : 0,
        };
        setPriceEstimateData(responseData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pricingData, selectedProjects]);

  return (
    <>
      {/* No alternatives  or error */}
      <Maybe
        condition={
          !!priceEstimateData &&
          (!!(priceEstimateData as ErrorResponse)?.error || !!(priceEstimateData as CustomPricingResponse)?.price)
        }
      >
        <Grid item xs={12} mt={2} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
          <Grow in={true} style={{ transformOrigin: "0 0 0" }} {...{ timeout: 1500 }}>
            <Box className={classes.rctBox} sx={{ width: "100%" }}>
              <RCTPriceEstimate priceData={priceEstimateData} portfolioName={portfolioName} />
            </Box>
          </Grow>
        </Grid>
      </Maybe>

      <Grid item xs={12} mt={2} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
        <Grow in={true} style={{ transformOrigin: "0 0 0" }} {...{ timeout: 1500 }}>
          <Box className={classes.rctBox} sx={{ width: "100%" }}>
            <LiveQuoteRequest
              customRCTData={customData}
              priceData={
                (priceEstimateData as ErrorResponse)?.error
                  ? undefined
                  : (priceEstimateData as CustomPricingResponse & {
                      total: number;
                    })
              }
              stepHandler={stepHandler}
              portfolioName={portfolioName}
            />
          </Box>
        </Grow>
      </Grid>
    </>
  );
}
