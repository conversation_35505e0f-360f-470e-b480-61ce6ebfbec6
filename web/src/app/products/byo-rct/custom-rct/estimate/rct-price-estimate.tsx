"use client";

import { Box, Grid, Stack, Typography } from "@mui/material";
import Maybe from "@app/components/maybe/maybe";
import { CustomPricingResponse, ErrorResponse, uuid } from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";
import RCTRequestLiveQuote from "../rct-request-live-quote";
import EstimateDisplay from "./estimate-display";
import useIsMobile from "@app/providers/mobile";
import RegisteredTrademark from "../registered-trademark/registered-trademark";

import classes from "../styles.module.scss";
import { extractJsonFeild } from "../utils";

const toFormattedNumberString = (num: number | Decimal | string): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const integerFormat = (x: number): string => {
  return x.toLocaleString();
};

export type PriceEstimate =
  | (CustomPricingResponse & {
      total: number;
    })
  | ErrorResponse;

interface RCTPriceEstimateProps {
  priceData?: PriceEstimate;
  portfolioName: string;
}

export default function RCTPriceEstimate(props: RCTPriceEstimateProps): JSX.Element {
  const { priceData, portfolioName } = props;
  const isMobile = useIsMobile(630);
  const total = (priceData as { total: number })?.total ?? 0; //Original total
  const inputMax = new Decimal((priceData as CustomPricingResponse)?.price ?? 0);
  const perTonneMax = inputMax.dividedBy(total);
  const totalMax = inputMax.toFixed(2, Decimal.ROUND_HALF_UP);

  return (
    <>
      <Maybe condition={!!priceData}>
        <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
          <Stack direction={"column"}>
            <Typography className={classes.sectionTitle} sx={{ flexGrow: "100%" }}>
              Estimated Price
            </Typography>
            <Maybe condition={!(priceData as ErrorResponse)?.error}>
              <Stack direction={isMobile ? "column" : "row"} gap={isMobile ? 1 : 15} mt={3}>
                <EstimateDisplay
                  label={
                    <span>
                      Per RCT <RegisteredTrademark />
                    </span>
                  }
                  value={`$${toFormattedNumberString(perTonneMax.toFixed(2, Decimal.ROUND_HALF_UP))}`}
                />
                <EstimateDisplay label="Quantity" value={`${integerFormat(total)}`} />
                <EstimateDisplay label="Total" value={`$${toFormattedNumberString(totalMax)}`} />
              </Stack>
              <Typography
                className={classes.inputNote}
                sx={{ flexGrow: "100%", verticalAlign: "middle", height: "100%" }}
              >
                *The above price estimate is indicative only. It is non-binding.
              </Typography>
            </Maybe>
            <Maybe condition={!!(priceData as ErrorResponse)?.error}>
              <Typography
                className={classes.inputLabelLg}
                sx={{ flexGrow: "100%", verticalAlign: "middle", height: "100%" }}
              >
                {`Further review of your bespoke portfolio is required before we can provide a price estimate. Please request a live quote below. A member of our team will contact you shortly.`}
              </Typography>
            </Maybe>
            <Box>
              <RCTRequestLiveQuote
                estimateId={
                  (priceData as CustomPricingResponse)?.id ??
                  uuid(extractJsonFeild((priceData as ErrorResponse)?.details, "id"))
                }
                portfolioName={portfolioName}
              />
            </Box>
          </Stack>
        </Grid>
      </Maybe>
    </>
  );
}
