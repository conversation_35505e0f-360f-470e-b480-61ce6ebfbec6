"use client";

import {
  <PERSON>rid,
  <PERSON>po<PERSON>,
  TableContainer,
  Table,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Stack,
  Box,
} from "@mui/material";
import classes from "./styles.module.scss";
import { BLANK } from "@app/constants/common";
import Maybe from "@app/components/maybe/maybe";
import { StoreContext } from "@app/providers/state/store";
import { useContext, useMemo } from "react";
import { Maybe as MaybeType, CustomPricingResponse } from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import PricingOptions from "./price-alternatives/pricing-options";
import { Indicator } from "@app/components/steps-indicator/steps-indicator";
import { RiskAdjIcon } from "@app/components/custom-icons/risk-adj";
import { CloudIcon } from "@app/components/custom-icons/cloud";
import SummaryTable from "./summary-table";
import type { AlternativeRow, RCTData, RCTProject } from "./model";
import { getAlternativeSummaryRows } from "./utils";

const cellStyle = {
  verticalAlign: "middle",
  borderBottom: "none",
  borderLeft: "none",
  borderRight: "none",
  borderTop: "none",
  height: "40px",
  marginBotton: 20,
};

const cellLabelStyle = {
  fontSize: "16px",
  fontWeight: 400,
  lineHeight: "24px",
  letterSpacing: "0.15px",
};

const projectCellStyle = {
  display: "inlineBlock",
  whiteSpace: "nowrap",
  overflow: "hidden !important",
  textOverflow: "ellipsis",
  width: "95%",
};

function AlternativeRow(props: { row: AlternativeRow }): JSX.Element {
  const { row } = props;
  return (
    <>
      <TableRow sx={{ verticalAlign: "top" }}>
        <TableCell align="left" sx={{ ...cellStyle, paddingLeft: "0px" }}>
          <Typography variant="body2" component="div" sx={{ ...cellLabelStyle, ...projectCellStyle }}>
            {row.projectName ?? BLANK}
          </Typography>
        </TableCell>
        <TableCell align="left" sx={{ ...cellStyle, paddingLeft: "0px" }}>
          <Typography variant="body2" component="div" sx={{ ...cellLabelStyle, ...projectCellStyle }}>
            {row.vintageName ?? BLANK}
          </Typography>
        </TableCell>
      </TableRow>
    </>
  );
}

export type PriceEstimate = CustomPricingResponse & {
  total: number;
};

interface LiveQuoteRequestProps {
  customRCTData: RCTData;
  priceData?: PriceEstimate;
  portfolioName: string;
  stepHandler: (steps: Indicator[]) => void;
}

export default function LiveQuoteRequest(props: LiveQuoteRequestProps): JSX.Element {
  const { persistentState } = useContext(StoreContext);
  const { customRCTData, priceData, portfolioName, stepHandler } = props;

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  const priceDataMsg: JSX.Element = useMemo(
    () => (
      <>
        {isEmpty(priceData?.alternatives) ? (
          <Typography className={classes.quoteSubLabel} sx={{ flexGrow: "100%" }}>
            Below is a summary of your request.
          </Typography>
        ) : (
          <Box
            className={classes.alternativedMsg}
            sx={{
              width: "100%",
              paddingTop: "10px",
              paddingRight: "8px",
              paddingBottom: "10px",
              paddingLeft: "8px",
            }}
          >
            <Stack direction="row">
              <WarningAmberIcon sx={{ height: "18px", paddingTop: "2px", color: "#ED6C02" }} />
              <Stack direction="column">
                <Typography
                  variant="body2"
                  component="h4"
                  fontWeight="400"
                  sx={{ color: "rgba(0, 0, 0, 0.6)", fontSize: "14px" }}
                >
                  {`Due to high demand, some of the projects you selected have limited inventory. We have adjusted your requested amount and suggested some alternative portfolios below. Once you request a live quote, our sales team will follow up to discuss the best way to meet your needs.`}
                </Typography>
              </Stack>
            </Stack>
          </Box>
        )}
      </>
    ),
    [priceData?.alternatives],
  );

  const hasAlternatives: boolean = useMemo(
    () => !!priceData?.alternatives && priceData?.alternatives?.length > 0,
    [priceData?.alternatives],
  );

  const columns = isEmpty(priceData?.alternatives)
    ? ["Project", "UN SDGs", "Carbon Credits", "% of RCT®"]
    : ["Project", "Vintage"];

  return (
    <>
      <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", gap: "24px" }}>
        <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", width: "100%" }}>
          <Typography className={classes.sectionTitle} sx={{ flexGrow: "100%" }}>
            Live Quote Request Summary
          </Typography>
        </Grid>
        <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", width: "100%" }}>
          {priceDataMsg}
        </Grid>

        <Stack
          direction="row"
          gap={4}
          sx={{ paddingBottom: "20px", width: "100%", overflowX: hasAlternatives ? "auto" : "hidden" }}
        >
          <TableContainer
            sx={{
              width: `${hasAlternatives ? "600px" : "100%"}`,
              overflow: "hidden",
              marginTop: `${hasAlternatives ? "118px" : "10px"}`,
            }}
          >
            <Maybe condition={!!selectedProjects && selectedProjects.length > 4 && selectedProjects.length < 13}>
              {hasAlternatives ? (
                <Table align={"left"} aria-label="projects-summary-table" sx={{ width: "100%" }}>
                  <TableHead>
                    <TableRow>
                      {columns.map((column, idx) => (
                        <TableCell key={idx} sx={{ paddingLeft: "0px" }}>
                          <Typography variant="body2" component="h4" fontWeight="700">
                            {column}
                          </Typography>
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {getAlternativeSummaryRows(selectedProjects)?.map((row) => (
                      <AlternativeRow key={row.id} row={row} />
                    ))}
                    <Maybe condition={isEmpty(priceData?.alternatives)}></Maybe>
                  </TableBody>
                </Table>
              ) : (
                <SummaryTable projects={selectedProjects} />
              )}
            </Maybe>
          </TableContainer>
          <Maybe condition={hasAlternatives}>
            <PricingOptions priceData={priceData} stepHandler={stepHandler} portfolioName={portfolioName} />
          </Maybe>
        </Stack>

        <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", width: "100%" }}>
          <Stack direction="column" spacing={2} sx={{ width: "100%" }}>
            <Box className={classes.tonnesSummaryLabelContainer}>
              <RiskAdjIcon width={"23px"} height={"auto"} />
              <Typography className={classes.tonnesSummaryLabel}>
                {!!customRCTData?.includeRiskAdj?.value
                  ? `Risk adjustment is included.`
                  : `Risk adjustment is not included.`}
              </Typography>
            </Box>
            <Box className={classes.tonnesSummaryLabelContainer}>
              <CloudIcon width={"23px"} height={"auto"} />
              <Typography className={classes.tonnesSummaryLabel}>
                {!!customRCTData?.retireAtPurchase?.value
                  ? `I will retire my RCTs immediately.`
                  : `I want to hold my RCTs for retirement later.`}
              </Typography>
            </Box>
          </Stack>
        </Grid>
      </Grid>
    </>
  );
}
