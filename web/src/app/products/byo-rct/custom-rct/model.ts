import { SxProps } from "@mui/material";
import {
  CustomPricingResponse,
  uuid,
  PortalProjectResponse,
  PortalProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
export interface IFormField {
  error: boolean;
  message: string;
}
export interface TableColumn {
  label?: JSX.Element | string;
  style?: SxProps;
}

export interface NumericFormField extends IFormField {
  value: number;
}

export interface BooleanFormField extends IFormField {
  value: boolean | undefined;
}

export interface RCTVintage extends PortalProjectVintageResponse {
  co2eTonnes?: number;
  rctPercent?: number;
  selected?: boolean;
}

export interface RCTProject extends PortalProjectResponse {
  projectVintages?: RCTVintage[];
  co2eTonnes?: number;
  rctPercent?: number;
  preselected?: boolean;
}

export interface RCTData {
  includeRiskAdj: BooleanFormField;
  retireAtPurchase: BooleanFormField;
  customPricingResponse?: CustomPricingResponse;
  portfolioName?: string;
}

export interface AlternativeRow {
  id: uuid;
  projectName: string;
  vintageName: string;
}

export interface VintageAmount {
  vintageId: uuid;
  amount: number;
}
