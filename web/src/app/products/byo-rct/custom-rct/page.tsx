import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import CustomRCT from "./custom-rct";
import SelectedProjectsHeader from "./selected-projects-header";
import Error from "@app/app/error";
import { ProjectRelations } from "@rubiconcarbon/shared-types";
import { getProjects } from "@app/app/projects/data-server";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";

export default async function CustomRCTPage(): Promise<JSX.Element> {
  try {
    const projectsResponse = await getProjects({
      hasAmount: true,
      byoBufferEligible: true,
      limit: DEFAULT_MAX_PAGING_LIMIT,
      isByorctEligible: true,
      includeRelations: [
        ProjectRelations.COUNTRY,
        ProjectRelations.SDGS,
        ProjectRelations.PROJECT_TYPE,
        ProjectRelations.PRICE_RANGE,
        ProjectRelations.PROJECT_VINTAGES,
      ],
    });

    if (projectsResponse === undefined) return <></>;

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <div className="no-padding">
          <SelectedProjectsHeader />
          <CustomRCT byoProjects={projectsResponse?.data} />
        </div>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
}
