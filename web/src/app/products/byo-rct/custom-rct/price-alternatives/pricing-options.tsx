"use client";

import Maybe from "@app/components/maybe/maybe";
import { StoreContext } from "@app/providers/state/store";
import { useContext, useMemo, useState } from "react";
import { Maybe as MaybeType, CustomPricingResponse } from "@rubiconcarbon/shared-types";
import PriceOption from "./price-option";
import { Indicator } from "@app/components/steps-indicator/steps-indicator";
import { Stack } from "@mui/material";
import { RCTProject } from "../model";

export type PriceEstimate = CustomPricingResponse & {
  total: number;
};

interface PricingOptionsProps {
  priceData?: PriceEstimate;
  portfolioName: string;
  stepHandler: (steps: Indicator[]) => void;
}

export default function PricingOptions(props: PricingOptionsProps): JSX.Element {
  const { priceData, portfolioName, stepHandler } = props;
  const { persistentState } = useContext(StoreContext);
  const [selectedOption, setSelectedOption] = useState<string>();

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  const onOptionChange = (name: string): void => {
    setSelectedOption(name);
  };

  return (
    <Maybe condition={!!priceData?.alternatives && priceData?.alternatives?.length > 0}>
      <Stack direction="row" width={"100%"} overflow={"auto"} gap={3}>
        {priceData?.alternatives.map((alternative, idx) => (
          <PriceOption
            key={idx}
            selectedProjects={selectedProjects!}
            priceAlternative={alternative}
            index={idx}
            selectedOption={selectedOption}
            portfolioName={portfolioName}
            onOptionChange={onOptionChange}
            stepHandler={stepHandler}
          />
        ))}
      </Stack>
    </Maybe>
  );
}
