"use client";

import { <PERSON><PERSON><PERSON>, useCallback, useEffect, useState, useContext, useMemo, CSSProperties } from "react";
import {
  TableContainer,
  Paper,
  Box,
  Grid,
  Table,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Checkbox,
  TablePagination,
  Stack,
  TableHead,
  Tooltip,
  Button,
  IconButton,
  Badge,
  SxProps,
  useMediaQuery,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ListItemText,
  OutlinedInput,
  SelectChangeEvent,
} from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import CheckIcon from "@mui/icons-material/Check";
import {
  ProjectEligibilityAccreditation,
  uuid,
  Maybe as MaybeType,
  PortalProjectResponse,
} from "@rubiconcarbon/shared-types";
import Maybe from "@app/components/maybe/maybe";
import TagChip from "@app/components/tag-chip/tag-chip";
import SDGsBox from "@app/components/sdgs-box/sdgs-box";
import IntegrityScore from "../../components/integrity-score/integrity-score";
import Link from "next/link";
import { Indicator, StepsStatus } from "@app/components/steps-indicator/steps-indicator";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { StoreContext } from "@app/providers/state/store";
import ProjectsSelector, { FilterData } from "./projects-selector";
import InteractiveWaterOrgLogo from "../../components/water-org/interactive-logo";
import SelectedProjectsList from "./selected-projects-list";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import InformationDialog from "@app/components/dialog/information-dialog";
import IntegrityScoreModal from "../../components/integrity-score/components/integrity-score-modal";
import { isEmpty } from "lodash";
import { BLANK } from "@app/constants/common";
import { RCTProject, RCTVintage } from "./model";
import {
  BYO_SELECTED_PROJECTS_STATE_KEY,
  BYOSteps,
  MAX_PROJECTS_ALLOWED,
  MIN_PROJECTS_ALLOWED,
} from "@app/constants/byo";
import RegisteredTrademark from "./registered-trademark/registered-trademark";
import { digestProjects, filterProjects } from "./utils";

import classes from "./styles.module.scss";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 150,
      minHeight: 40,
    },
  },
  sx: {
    "&& .MuiMenuItem-root": {
      backgroundColor: "white",
    },
  },
};

const getTags = (project: PortalProjectResponse): JSX.Element | "" => {
  if (!project?.country?.name) return "";
  return project.country?.name && <TagChip label={project.country?.name} />;
};

interface ProjectsSelectionProps {
  projects: MaybeType<PortalProjectResponse[]>;
  stepHandler: (steps: Indicator[]) => void;
}

export default function ProjectsSelection(props: ProjectsSelectionProps): JSX.Element {
  const { projects, stepHandler } = props;
  const [availableProjects, setAvailableProjects] = useState<RCTProject[] | null | undefined>();
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(100);
  const { persistentState, updatePersistentState } = useContext(StoreContext);
  const [allowConfigureRCT, setAllowConfigureRCT] = useState<boolean>(false);
  const [selectionModalOpen, setSelectionModalOpen] = useState<boolean>(false);

  const isMobile = useMediaQuery("(max-width: 1000px)");

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  const selectedProjectIds = useMemo(() => selectedProjects?.map((p) => p.id) as uuid[], [selectedProjects]) as uuid[];

  const isSelected = useCallback((id: uuid): boolean => selectedProjectIds.indexOf(id) !== -1, [selectedProjectIds]);

  //If there are selected projects then set the selection, otherwise use the default selection
  useEffect(() => {
    if (!!projects) {
      setAvailableProjects(digestProjects(projects, selectedProjects));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setAllowConfigureRCT(!!projects && selectedProjectIds.length > 4 && selectedProjectIds.length < 13);
  }, [selectedProjectIds, projects]);

  const projectsFilterHandler = useCallback(
    (selection: FilterData): void => {
      if (!!selection && !!projects) {
        const filteredProjects = filterProjects(selection, projects, selectedProjects);
        if (filteredProjects?.length === 0) setAvailableProjects([]);
        else {
          setAvailableProjects(filteredProjects);
        }
        setPage(0);
      }
    },
    [projects, selectedProjects],
  );

  const handleProjectSelection = useCallback(
    (event: React.MouseEvent<unknown>, id: uuid): void => {
      const selectedIndex = selectedProjectIds?.indexOf(id);
      let newSelectedProjectsIds: uuid[] = [];
      const selectedProject = availableProjects?.find((p) => p.id === id);
      //if this is a new selection and project selection was made
      if (
        selectedIndex === -1 &&
        !!selectedProject &&
        !isEmpty(selectedProject?.projectVintages?.find((pv) => pv?.selected === true))
      ) {
        newSelectedProjectsIds = newSelectedProjectsIds.concat(selectedProjectIds, id);
      } else if (selectedIndex === 0) {
        newSelectedProjectsIds = newSelectedProjectsIds.concat(selectedProjectIds.slice(1));
      } else if (selectedIndex === selectedProjectIds.length - 1) {
        newSelectedProjectsIds = newSelectedProjectsIds.concat(selectedProjectIds.slice(0, -1));
      } else if (selectedIndex > 0) {
        newSelectedProjectsIds = newSelectedProjectsIds.concat(
          selectedProjectIds.slice(0, selectedIndex),
          selectedProjectIds.slice(selectedIndex + 1),
        );
      } else {
        newSelectedProjectsIds = selectedProjectIds;
      }
      const filteredProjects = digestProjects(projects, selectedProjects)?.filter((p) =>
        newSelectedProjectsIds.includes(p.id),
      );

      // saving to persistent store
      updatePersistentState(BYO_SELECTED_PROJECTS_STATE_KEY, filteredProjects);
    },
    [selectedProjectIds, availableProjects, updatePersistentState, projects, selectedProjects],
  );

  const handleChangePage = (event: unknown, newPage: number): void => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const Row = (props: { row: RCTProject }): JSX.Element => {
    const { row } = props;
    const isWaterOrgEligible = row.eligibilityAccreditations.includes(ProjectEligibilityAccreditation.WATER_ORG);
    const [isScoreModalOpen, setIsScoreModalOpen] = useState<boolean>(false);
    const [currentlyEditing, setCurrentlyEditing] = useState<boolean>(false);
    const [selectedVintages, setSelectedVintages] = useState<RCTVintage[] | undefined>(row.projectVintages);

    const isEmptyCheckedVintages: boolean = useMemo(
      () => (!!selectedVintages?.find((v) => v.selected) ? false : true),
      [selectedVintages],
    );

    const handleVintageSave = useCallback((): void => {
      if (!isEmpty(availableProjects) && !isEmpty(selectedVintages)) {
        const updatedProjects = availableProjects?.map((p) => {
          if (p.id === row.id) {
            return {
              ...p,
              projectVintages: selectedVintages ?? [],
              preselected: false,
            };
          }
          return p;
        });

        setCurrentlyEditing(false);
        setAvailableProjects(updatedProjects);
        updatePersistentState(
          BYO_SELECTED_PROJECTS_STATE_KEY,
          updatedProjects?.filter((p) => selectedProjectIds.includes(p.id)),
        );
      }
    }, [row.id, selectedVintages]);

    const handleVintagEdit = useCallback((): void => {
      setCurrentlyEditing(true);
    }, []);

    const handleVintageSelection = useCallback(
      (event: SelectChangeEvent<string[]>) => {
        const {
          target: { value },
        } = event;

        const selectedVintagesArr = typeof value === "string" ? value.split(",") : value;
        const newSelection = selectedVintages?.map((v) => {
          return {
            ...v,
            selected: selectedVintagesArr.includes(v.name),
          };
        });

        setSelectedVintages(newSelection);
      },
      [selectedVintages],
    );

    const projectVintagesEdit = useCallback((): JSX.Element => {
      const isDisableSave = !selectedVintages || selectedVintages?.length < 1;
      return (
        <Stack direction="row" gap={2}>
          <FormControl sx={{ width: 150 }}>
            <InputLabel
              size="small"
              id="vintages-label"
              sx={{
                "&.MuiInputLabel-shrink": {
                  color: "rgba(22, 122, 143, 1)",
                },
              }}
            >
              Select
            </InputLabel>
            <Select
              labelId="vintages-label"
              id="vintages"
              name="vintages"
              multiple
              size="small"
              value={selectedVintages?.filter((sv) => sv.selected === true).map((v) => v.name)}
              renderValue={(selected): string => {
                return selected.length > 1 ? `${selected.length} selected` : selected.toString();
              }}
              onChange={handleVintageSelection}
              input={<OutlinedInput label="Select" />}
              MenuProps={MenuProps}
              sx={{
                ".& MuiMenuItem-root": {
                  backgroudColor: "red",
                },
              }}
            >
              {/* Available values */}
              {!isEmpty(selectedVintages) ? (
                selectedVintages?.map((v) => (
                  <MenuItem key={v.id} value={v.name} sx={{ paddingLeft: "0px" }}>
                    <Checkbox
                      checked={v.selected}
                      sx={{
                        "&.Mui-checked": {
                          color: "#04CDB7",
                        },
                      }}
                    />
                    <ListItemText primary={v.name} />
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled={true}>
                  <ListItemText primary="No options" />
                </MenuItem>
              )}
            </Select>
          </FormControl>
          <Tooltip title={isDisableSave ? "please select a vintage" : ""} placement="top">
            <span>
              <IconButton
                disabled={isDisableSave || isEmptyCheckedVintages}
                sx={{ color: "#094436" }}
                edge="start"
                onClick={handleVintageSave}
              >
                <CheckIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Stack>
      );
    }, [handleVintageSelection, selectedVintages, isEmptyCheckedVintages, handleVintageSave]);

    const projectVintagesDisplay = useCallback((): JSX.Element => {
      let display = BLANK;
      if (!!selectedVintages && selectedVintages.length > 0) {
        display = selectedVintages
          ?.filter((sv) => sv?.selected === true)
          .map((v) => v.name)
          .join(", ");
      }

      const isEditable = !!row?.projectVintages ? row?.projectVintages?.length > 1 : false;

      return (
        <Stack direction="row" gap={2} sx={{ width: "200px" }}>
          <Tooltip
            title={
              !!selectedVintages && selectedVintages.length > 2
                ? selectedVintages
                    ?.map((v) => v.name)
                    .sort()
                    .join(", ")
                : ""
            }
            placement="top"
          >
            <Typography
              sx={{
                paddingLeft: "10px",
                textOverflow: "ellipsis",
                maxWidth: "140px",
                whiteSpace: "nowrap",
                overflow: "hidden",
              }}
            >
              {display}
            </Typography>
          </Tooltip>
          <Maybe condition={isEditable}>
            <IconButton
              disabled={!isSelected(row?.id)}
              sx={{ color: "#094436", marginTop: "-10px" }}
              edge="start"
              onClick={handleVintagEdit}
            >
              <CreateIcon />
            </IconButton>
          </Maybe>
        </Stack>
      );
    }, [selectedVintages, row?.id, handleVintagEdit, row?.projectVintages]);

    return (
      <>
        <TableRow
          hover
          selected={isSelected(row.id)}
          aria-checked={isSelected(row.id)}
          sx={{
            height: "81px",
            padding: "2px",
            backgroundColor: "white",
            "&.Mui-selected": {
              backgroundColor: "rgba(232, 249, 253, 0.5)",
            },
            "&.MuiTableRow-hover: hover": {
              backgroundColor: "rgba(218, 232, 245, 1)",
            },
          }}
        >
          <TableCell sx={{ paddingTop: "14px", width: "15px" }}>
            <Checkbox
              onClick={(event): void => handleProjectSelection(event, row.id)}
              checked={isSelected(row.id)}
              sx={{
                "&.Mui-checked": {
                  color: "#04CDB7",
                },
              }}
            />
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "center" }}>
            <Maybe condition={!!row?.integrityGradeScore}>
              <Box onClick={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}>
                <IntegrityScore
                  score={Math.round(row?.integrityGradeScore ?? 0)}
                  separate
                  hideSubtext
                  className={classes.integrityGrade}
                />
              </Box>
            </Maybe>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left", textUnderlineOffset: 4 }}>
            <Stack direction="column">
              <Link
                href={`/projects/${row.id}` as any}
                style={{ textDecorationColor: "#000000" }}
                passHref
                legacyBehavior
              >
                <a onClick={(event: MouseEvent<HTMLAnchorElement>): void => event.stopPropagation()} target="_blank">
                  <Typography className={classes.mainInfo}>{row?.name}</Typography>
                </a>
              </Link>
              <Typography mt={0.5} variant="body2" className={classes.supportiveInfo}>
                {row?.projectType?.category}
              </Typography>
              <Typography variant="body2" className={classes.supportiveInfo}>
                {row?.registryProjectId}
              </Typography>
            </Stack>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left", width: "200px" }}>
            {currentlyEditing ? projectVintagesEdit() : projectVintagesDisplay()}
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "center" }}>
            <Maybe condition={isWaterOrgEligible}>
              <Box sx={{ marginBottom: "5px" }}>
                <InteractiveWaterOrgLogo width={100} />
              </Box>
            </Maybe>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            {row?.priceRange}
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            <SDGsBox height={32} projectSDGs={row?.projectSDGs?.sort((a, b) => (a.sdgTypeId < b.sdgTypeId ? -1 : 1))} />
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            {row?.registryName}
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            {getTags(row)}
          </TableCell>
        </TableRow>
        <IntegrityScoreModal
          open={isScoreModalOpen}
          handleClose={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}
        />
      </>
    );
  };

  const listHandler = useCallback((): void => {
    if (selectedProjectIds.length > 0) {
      setSelectionModalOpen(true);
    }
  }, [selectedProjectIds]);

  const columns = [
    {},
    {},
    { label: "Project", style: {} },
    { label: "Vintage", style: {} },
    {},
    { label: "Price Range", style: { textAlign: "left", width: "150px" } },
    { label: "UN SDGs", style: { textAlign: "left" } },
    { label: "Source", style: { textAlign: "left" } },
    { label: "Country", style: { textAlign: "left" } },
  ];

  const isValidProjectsSelection: boolean = useMemo(
    () =>
      (selectedProjectIds.length > 0 && selectedProjectIds.length < MIN_PROJECTS_ALLOWED) ||
      selectedProjectIds.length > MAX_PROJECTS_ALLOWED
        ? false
        : true,
    [selectedProjectIds],
  );

  const NextButton = (props: { style?: SxProps }): JSX.Element => {
    const { style } = props;
    return (
      <Tooltip
        title={
          !allowConfigureRCT
            ? `Please select a min of ${MIN_PROJECTS_ALLOWED} and max of ${MAX_PROJECTS_ALLOWED} projects for your portfolio.`
            : ""
        }
      >
        <span>
          <Button
            className={allowConfigureRCT ? classes.nextButton : classes.nextButtonDisabled}
            disabled={!allowConfigureRCT}
            variant="contained"
            endIcon={<ArrowForwardIosIcon />}
            onClick={(): void =>
              stepHandler([
                { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.DONE },
                {
                  name: "2",
                  label: (
                    <span>
                      {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
                    </span>
                  ),
                  status: StepsStatus.CURRENT,
                },
                { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.TODO },
              ])
            }
            sx={{ ...style }}
          >
            Next: Configure RCT <RegisteredTrademark style={{ top: "-2.5px", marginLeft: "1px" }} />
          </Button>
        </span>
      </Tooltip>
    );
  };

  return (
    <Maybe condition={projects !== null}>
      <Box className={classes.cart}>
        <Stack
          direction={isMobile ? "column" : "row-reverse"}
          className={
            selectedProjectIds.length > MAX_PROJECTS_ALLOWED || selectedProjectIds.length < MIN_PROJECTS_ALLOWED
              ? classes.selectionInvalid
              : classes.selectionValid
          }
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          <Stack direction="row-reverse" sx={{ paddingTop: "0px", height: "60px" }}>
            <IconButton
              size="large"
              onClick={listHandler}
              disabled={selectedProjectIds.length === 0}
              sx={{
                marginBotton: "30px",
                "&:hover": {
                  backgroundColor: "#DFF5F2",
                },
              }}
            >
              <Badge
                badgeContent={selectedProjectIds.length}
                color={isValidProjectsSelection ? "success" : "error"}
                overlap="circular"
                style={{ transform: "translate(30px, -20px)" }}
              ></Badge>
              <ShoppingCartIcon
                fontSize="large"
                className={selectedProjectIds.length > 0 ? classes.list : classes.disabledList}
              />
            </IconButton>

            <Maybe
              condition={
                selectedProjectIds.length > MAX_PROJECTS_ALLOWED || selectedProjectIds.length < MIN_PROJECTS_ALLOWED
              }
            >
              <Tooltip
                placement="top"
                title={`Please select a min of ${MIN_PROJECTS_ALLOWED} and max of ${MAX_PROJECTS_ALLOWED} projects for your portfolio.`}
              >
                <Typography
                  width={"320px"}
                  component="h4"
                  className={classes.selectionInvalid}
                  sx={{ marginTop: "22px" }}
                >
                  {`Please select between ${MIN_PROJECTS_ALLOWED}-${MAX_PROJECTS_ALLOWED} projects`}
                </Typography>
              </Tooltip>
            </Maybe>
          </Stack>

          <Box sx={{ width: "300px", textAlign: "left", paddingLeft: "10px" }}>
            <NextButton style={{ marginTop: "5px !important", marginBottom: "10px !important", width: "270px" }} />
          </Box>
        </Stack>
      </Box>
      <Grid container direction="row" className={classes.rctContainer}>
        <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
          <Box className={classes.rctBox}>
            <>
              <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
                <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
                  <Typography className={classes.sectionTitle}>Select your projects</Typography>
                </Grid>
                <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
                  <Typography component="h4" className={classes.projctSelectionSub}>
                    Add projects to your portfolio by making a selection. Choose at least 5 projects to configure your
                    RCT <RegisteredTrademark style={{ top: "4px", marginLeft: "0px" }} />.
                  </Typography>
                </Grid>
              </Grid>

              <Grid mt={2} item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
                <Stack direction="row" spacing={2} sx={{ width: "100%" }}>
                  <Maybe condition={!!availableProjects}>
                    <TableContainer component={Paper}>
                      <Table aria-label="projects-selection-table">
                        <TableHead>
                          <TableRow sx={{ backgroundColor: "rgba(250, 250, 250, 1)" }}>
                            <TableCell sx={{ padding: "0px" }} colSpan={columns.length + 1}>
                              <ProjectsSelector projects={projects} onFilterHandler={projectsFilterHandler} />
                            </TableCell>
                          </TableRow>
                          <TableRow sx={{ verticalAlign: "top" }}>
                            {columns.map((column, idx) => (
                              <TableCell key={idx} sx={{ paddingLeft: "16px", ...(column.style as CSSProperties) }}>
                                <Typography variant="body2" component="h4" fontWeight="700">
                                  {column.label}
                                </Typography>
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody sx={{ maxHeight: "200px" }}>
                          {availableProjects
                            ?.sort((a, b) => a.name.localeCompare(b.name))
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((project) => <Row key={project.id} row={project} />)}
                        </TableBody>
                      </Table>
                      <TablePagination
                        rowsPerPageOptions={[5, 10, 25, 100]}
                        component="div"
                        count={availableProjects?.length ?? 0}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                      />
                    </TableContainer>
                  </Maybe>
                </Stack>
              </Grid>
            </>
          </Box>
        </Grid>
        <Grid
          className={classes.nextButtonContainer}
          item
          xs={12}
          container
          direction="row"
          sx={{ position: "relative", flexGrow: "100%" }}
        >
          <NextButton style={{ marginTop: "30px !important" }} />
        </Grid>
      </Grid>
      <InformationDialog
        title="Selected Projects"
        open={selectionModalOpen}
        onClose={(): void => setSelectionModalOpen(false)}
      >
        <SelectedProjectsList
          availableProjects={selectedProjects}
          selectedProjectIds={selectedProjectIds}
          handleProjectSelection={handleProjectSelection}
        />
      </InformationDialog>
    </Maybe>
  );
}
