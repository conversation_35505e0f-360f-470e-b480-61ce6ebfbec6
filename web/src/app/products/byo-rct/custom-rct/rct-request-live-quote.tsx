"use client";

import { <PERSON><PERSON>, SxProps } from "@mui/material";
import { useState, useCallback, useContext } from "react";
import { ByorctEmailRequest, ByorctEmailResponse, UserActionDataType, uuid } from "@rubiconcarbon/shared-types";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { AxiosContext } from "@app/providers/axios-provider";
import { useRouter } from "next/navigation";
import { SUBMIT_LIVE_QUOTE_ERR, SUBMIT_LIVE_QUOTE_SUCCESS } from "@app/constants/dialog-messages";
import LiveQuoteSubmitDialog from "@app/components/dialog/submit-live-quote-dialog";

import classes from "./styles.module.scss";

export interface SubmitData {
  isSubmit: boolean;
  isSuccess: boolean;
  message?: string;
  modelPortfolioId?: uuid;
}

interface RCTSubmiLiveQuoteProps {
  estimateId: uuid;
  isEnabled?: boolean;
  style?: SxProps;
  portfolioName: string;
}

export default function RCTRequestLiveQuote(props: RCTSubmiLiveQuoteProps): JSX.Element {
  const { api } = useContext(AxiosContext);
  const router = useRouter();
  const [submitData, setSubmitData] = useState<SubmitData>({ isSubmit: false, isSuccess: false, message: "" });
  const [enableSubmit, setEnableSubmit] = useState<boolean>(true);
  const { style, estimateId, portfolioName, isEnabled = true } = props;

  const submitQuoteHandler = useCallback(async (): Promise<void> => {
    setEnableSubmit(false);
    const payload: ByorctEmailRequest = {
      cachedEstimateId: estimateId,
      modelPortfolioName: portfolioName,
    };

    api
      .post<ByorctEmailResponse>(`/byorct/emails`, payload)
      .then((response) => {
        if (response.status === 201) {
          setSubmitData({
            isSubmit: true,
            isSuccess: true,
            message: SUBMIT_LIVE_QUOTE_SUCCESS,
            modelPortfolioId: response?.data?.modelPortfolioId,
          });

          const action: UserActionDataType = {
            version: 1,
            type: "byorct_requested",
            data: {
              modelPortfolioId: response?.data?.modelPortfolioId,
            } as any, // todo: we need to update "data" of "byorct_requested" type in shared types. need to clarify with Tiago
          };
          api.post("/user-actions", action).then();
        } else {
          setEnableSubmit(true);
          setSubmitData({ isSubmit: true, isSuccess: false });
        }
      })
      .catch((error: any) => {
        setEnableSubmit(true);
        setSubmitData({
          isSubmit: true,
          isSuccess: false,
          message: error?.response?.data?.message ?? SUBMIT_LIVE_QUOTE_ERR,
        });
      });
  }, [api, estimateId, portfolioName]);

  const onCloseHandler = (): void => {
    if (submitData.isSuccess) router.push("/overview");

    setSubmitData({ ...submitData, isSubmit: false });
  };

  return (
    <>
      <Button
        onClick={submitQuoteHandler}
        disabled={!isEnabled || !enableSubmit}
        className={enableSubmit && isEnabled ? classes.nextButton : classes.nextButtonDisabled}
        variant="contained"
        sx={style ?? {}}
        endIcon={<ArrowForwardIosIcon />}
      >
        Request Live Quote
      </Button>

      <LiveQuoteSubmitDialog submitData={submitData} onClose={onCloseHandler} />
    </>
  );
}
