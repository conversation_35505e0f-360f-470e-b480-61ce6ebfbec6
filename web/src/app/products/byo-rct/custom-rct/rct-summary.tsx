"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import RCTEstimate from "./estimate/rct-estimate";
import { CustomPricingResponse, ErrorResponse, uuid } from "@rubiconcarbon/shared-types";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import { Indicator, StepsStatus } from "@app/components/steps-indicator/steps-indicator";
import RCTRequestLiveQuote from "./rct-request-live-quote";
import { useMemo, useState } from "react";
import Maybe from "@app/components/maybe/maybe";
import PortfolioName from "./portfolio-name";
import { RCTData } from "./model";
import { isEmpty } from "lodash";
import { BYOSteps } from "@app/constants/byo";
import RegisteredTrademark from "./registered-trademark/registered-trademark";
import classes from "./styles.module.scss";
import { extractJsonFeild } from "./utils";

export interface SubmitData {
  isSubmit: boolean;
  isSuccess: boolean;
  message?: string;
}

interface RCTSummaryProps {
  customRCTData: RCTData;
  pricingData: CustomPricingResponse | ErrorResponse;
  priceChangeHandler: (customPricingResponse: CustomPricingResponse | undefined) => void;
  stepHandler: (steps: Indicator[]) => void;
}

export default function RCTSummary(props: RCTSummaryProps): JSX.Element {
  const { customRCTData, pricingData, priceChangeHandler, stepHandler } = props;
  const [portfolioName, setPortfolioName] = useState<string>();

  const hasAlternatives: boolean = useMemo(
    () =>
      !!(pricingData as CustomPricingResponse)?.alternatives &&
      (pricingData as CustomPricingResponse)?.alternatives?.length > 0,
    [pricingData],
  );

  const nameHandler = (name: string, defaultName: string): void => {
    if (isEmpty(name)) {
      setPortfolioName(defaultName ?? "");
    } else {
      setPortfolioName(name);
    }
  };

  return (
    <Grid container direction="row" className={classes.rctContainer}>
      <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
        <PortfolioName nameHandler={nameHandler} />
      </Grid>
      <Maybe condition={portfolioName !== null}>
        <RCTEstimate
          customData={customRCTData}
          onPriceChange={priceChangeHandler}
          stepHandler={stepHandler}
          pricingData={pricingData}
          portfolioName={portfolioName ?? ""}
        />
      </Maybe>
      <Grid
        className={classes.nextButtonContainer}
        item
        xs={12}
        container
        direction="row"
        sx={{ position: "relative", flexGrow: "100%" }}
      >
        <Stack direction="row" spacing={2}>
          <Button
            className={classes.backButton}
            variant="contained"
            startIcon={<ArrowBackIosIcon />}
            onClick={(): void =>
              stepHandler([
                { name: "1", label: BYOSteps.SELECT_PROJECTS, status: StepsStatus.DONE },
                {
                  name: "2",
                  label: (
                    <span>
                      {BYOSteps.CONFIGURE_YOUR_RCT} <RegisteredTrademark />
                    </span>
                  ),
                  status: StepsStatus.CURRENT,
                },
                { name: "3", label: BYOSteps.REQUEST_LIVE_QUOTE, status: StepsStatus.TODO },
              ])
            }
          >
            Previous: Configure Your RCT <RegisteredTrademark style={{ marginLeft: "2px", top: "-3px" }} />
          </Button>
          <Maybe condition={!hasAlternatives && !!portfolioName}>
            <RCTRequestLiveQuote
              estimateId={
                (pricingData as CustomPricingResponse)?.id ??
                uuid(extractJsonFeild((pricingData as ErrorResponse)?.details, "id"))
              }
              portfolioName={portfolioName!}
            />
          </Maybe>
        </Stack>
      </Grid>
    </Grid>
  );
}
