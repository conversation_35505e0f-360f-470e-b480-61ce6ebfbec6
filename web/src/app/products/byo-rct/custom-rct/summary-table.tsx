"use client";

import React, { MouseEvent, useState } from "react";
import {
  Grid,
  Box,
  Typography,
  TableContainer,
  Table,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
  Stack,
} from "@mui/material";
import { Maybe as MaybeType, PortalProjectResponse } from "@rubiconcarbon/shared-types";
import Maybe from "@app/components/maybe/maybe";
import Link from "next/link";
import IntegrityScore from "../../components/integrity-score/integrity-score";
import IntegrityScoreModal from "../../components/integrity-score/components/integrity-score-modal";
import TagChip from "@app/components/tag-chip/tag-chip";
import { RCTProject, TableColumn } from "./model";
import RegisteredTrademark from "./registered-trademark/registered-trademark";
import classes from "./styles.module.scss";

const vintageCellStyle = {
  height: "10px",
  padding: "0px 0px 0px 0px",
};

const getTags = (project: PortalProjectResponse): JSX.Element | "" => {
  if (!project?.country?.name) return "";
  return project.country?.name && <TagChip label={project.country?.name} />;
};

function VintageRow(props: { row: RCTProject }): JSX.Element {
  const { row } = props;
  return (
    <>
      {row?.projectVintages
        ?.filter((pv) => pv.selected)
        .map((pv) => (
          <TableRow key={pv.id} sx={{ backgroundColor: "rgba(82, 181, 202, 0.04)" }}>
            <TableCell colSpan={3} sx={vintageCellStyle} />
            <TableCell sx={{ ...vintageCellStyle, textAlign: "center" }}>
              <Typography
                variant="body2"
                component="div"
                sx={{ fontSize: "16px", textAlign: "center", width: "100px", paddingTop: "0px" }}
              >
                {pv?.name}
              </Typography>
            </TableCell>
            <TableCell align="left" sx={{ width: "120px" }}>
              <Typography variant="body2" component="div" sx={{ fontSize: "16px", textAlign: "center" }}>
                {!!pv?.co2eTonnes ? `${pv?.co2eTonnes?.toLocaleString()}` : "0"}
              </Typography>
            </TableCell>
            <TableCell align="left" sx={{ width: "120px" }}>
              <Typography
                variant="body2"
                component="div"
                sx={{ fontSize: "16px", textAlign: "center", paddingRight: "25px" }}
              >
                {!!pv.rctPercent ? `${pv?.rctPercent.toFixed(2)}%` : "0.00%"}
              </Typography>
            </TableCell>
          </TableRow>
        ))}
    </>
  );
}

function Row(props: { row: RCTProject }): JSX.Element {
  const { row } = props;
  const [isScoreModalOpen, setIsScoreModalOpen] = useState<boolean>(false);

  return (
    <React.Fragment>
      <TableRow>
        <TableCell align="right" sx={{ textAlign: "center", maxWidth: "50px" }}>
          <Maybe condition={!!row?.integrityGradeScore}>
            <Box onClick={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}>
              <IntegrityScore
                score={Math.round(row?.integrityGradeScore ?? 0)}
                separate
                hideSubtext
                className={classes.integrityGrade}
              />
            </Box>
          </Maybe>
        </TableCell>
        <TableCell align="right" sx={{ textAlign: "left", textUnderlineOffset: 4 }}>
          <Stack direction="column">
            <Link
              href={`/projects/${row.id}` as any}
              style={{ textDecorationColor: "#000000" }}
              passHref
              legacyBehavior
            >
              <a onClick={(event: MouseEvent<HTMLAnchorElement>): void => event.stopPropagation()} target="_blank">
                <Typography fontWeight="500" color="#000000">
                  {row?.name}
                </Typography>
              </a>
            </Link>
            <Typography mt={0.5} variant="body2" className={classes.supportiveInfo}>
              {row?.projectType?.category}
            </Typography>
            <Typography variant="body2" className={classes.supportiveInfo}>
              {row?.registryProjectId}
            </Typography>
          </Stack>
        </TableCell>
        <TableCell align="right" sx={{ textAlign: "left" }}>
          {getTags(row)}
        </TableCell>
        <TableCell></TableCell>
        <TableCell align="left" sx={{ width: "120px" }}>
          <Typography variant="body2" component="div" sx={{ fontSize: "16px", textAlign: "center" }}>
            {!!row?.co2eTonnes ? `${row?.co2eTonnes?.toLocaleString()}` : "0"}
          </Typography>
        </TableCell>
        <TableCell align="left" sx={{ width: "120px" }}>
          <Typography
            variant="body2"
            component="div"
            sx={{ fontSize: "16px", textAlign: "center", paddingRight: "25px" }}
          >
            {!!row?.rctPercent ? `${row?.rctPercent.toFixed(2)}%` : "0.00%"}
          </Typography>
        </TableCell>
      </TableRow>
      <VintageRow row={row} />
      <IntegrityScoreModal
        open={isScoreModalOpen}
        handleClose={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}
      />
    </React.Fragment>
  );
}

interface SummaryTableProps {
  projects: MaybeType<RCTProject[]>;
}

export default function SummaryTable(props: SummaryTableProps): JSX.Element {
  const { projects } = props;

  const columns: TableColumn[] = [
    { style: { width: "20px" } },
    { label: "Project/Vintage", style: {} },
    { label: "Country", style: { textAlign: "left" } },
    { label: "Vintage", style: { textAlign: "left", paddingLeft: "25px" } },
    { label: "Carbon Credits", style: { textAlign: "center" } },
    {
      label: (
        <span>
          % of RCT
          <RegisteredTrademark style={{ paddingLeft: "3px" }} />
        </span>
      ),
      style: { textAlign: "left", paddingLeft: "35px" },
    },
  ];

  return (
    <>
      <Grid item xs={12} mt={6} container direction="row" sx={{ position: "relative", flexGrow: "100%" }}>
        <Stack direction="column" spacing={2} sx={{ width: "100%" }}>
          <Maybe condition={!!projects && projects.length > 0}>
            <TableContainer sx={{ width: "100%" }}>
              <Table align={"left"} aria-label="projects-selection-table">
                <TableHead>
                  <TableRow sx={{ verticalAlign: "top" }}>
                    {columns.map((column, idx) => (
                      <TableCell key={idx} sx={{ paddingLeft: "16px", ...column.style }}>
                        <Typography variant="body2" component="h4" fontWeight="700">
                          {column.label}
                        </Typography>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>{projects?.map((row) => <Row key={row.id} row={row} />)}</TableBody>
              </Table>
            </TableContainer>
          </Maybe>
        </Stack>
      </Grid>
    </>
  );
}
