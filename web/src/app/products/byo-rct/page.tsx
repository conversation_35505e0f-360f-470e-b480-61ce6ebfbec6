import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import ImageJumbotron from "@app/components/image-jumbotron/image-jumbotron";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import { Jumbotron, Paragraph as ParagraphType, StepGist } from "@app/types/static-content";
import Link from "next/link";
import Paragraph from "@app/components/paragraph/paragraph";
import StepBullet from "@app/components/step-bullet/step-bullet";
import byoRctImage from "@assets/images/byo-rct.webp";
import RegisteredTrademark from "./custom-rct/registered-trademark/registered-trademark";
import classes from "./styles.module.scss";

type PageTree = {
  header: Jumbotron;
  stepsHeader: ParagraphType;
  steps: [StepGist, StepGist, StepGist];
};

const GetStartedButton = (): JSX.Element => (
  <Link className={classes.GetStarted} href={"/products/byo-rct/custom-rct" as any}>
    Get started
  </Link>
);

const StaticDataTree: PageTree = {
  header: {
    images: [byoRctImage],
    text: (
      <>
        Build Your Own RCT <RegisteredTrademark />
      </>
    ),
    fullWidth: true,
    bottomRightAction: <GetStartedButton />,
  },
  stepsHeader: {
    content: (
      <span>
        Build a custom RCT <RegisteredTrademark /> in 3 easy steps
      </span>
    ),
  },
  steps: [
    {
      step: "01",
      text: "Select your projects, risk mitigation and retirement method",
      subTexts: [
        {
          content: (
            <span>
              Explore projects and handpick what you want to include in your RCT <RegisteredTrademark />.
            </span>
          ),
        },
        { content: "Select at least 5 projects with a minimum of 1,000 credits." },
      ],
    },
    {
      step: "02",
      text: "Receive an instant price estimate",
      subTexts: [
        {
          content: (
            <span>
              You will get a non-binding price estimate for your{" "}
              <span style={{ whiteSpace: "nowrap" }}>
                RCT <RegisteredTrademark />
              </span>{" "}
              immediately.
            </span>
          ),
        },
      ],
    },
    {
      step: "03",
      text: "Live quote",
      subTexts: [{ content: "Your account manager will follow up in the next business day with a live quote." }],
    },
  ],
};

const BuildYourOwnRCTPage = (): JSX.Element => {
  const { header, stepsHeader, steps } = StaticDataTree;

  return (
    <RequirePermissions
      permissions={["verified"]}
      user={getUser()}
      forbidden={<ForbiddenErrorPage />}
      unauthenticated={<UnauthenticatedErrorPage />}
    >
      <div className={`${classes.Container} no-padding`}>
        <div>
          <ImageJumbotron data={header} style={{ height: 300 }} />
          <Paragraph classes={{ root: classes.Paragraph }} data={stepsHeader} />
          <div className={classes.StepBulletsContainer}>
            {steps.map((step) => (
              <StepBullet key={step.step} data={step} />
            ))}
          </div>
        </div>
        <footer className={classes.Footer}>
          <div className={classes.Text}>
            Ready to build your RCT <RegisteredTrademark style={{ paddingRight: "5px" }} />?
          </div>
          <GetStartedButton />
        </footer>
      </div>
    </RequirePermissions>
  );
};

export default BuildYourOwnRCTPage;
