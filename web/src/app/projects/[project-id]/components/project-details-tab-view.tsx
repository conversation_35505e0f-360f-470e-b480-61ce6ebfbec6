import Grid from "@mui/material/Grid";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";
import Typography from "@mui/material/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useTheme } from "@mui/material/styles";
import React from "react";
import { PortalProjectResponse, ProjectSdgResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@app/theme/colors";
import { Remark } from "react-remark";
import { sdgIconMap } from "@app/components/sdg-icons";
import TabPanel from "@app/components/tab-panel/tab-panel";
import Image from "next/image";

const ProjectSDGRow = (props: { projectSDG: ProjectSdgResponse }): JSX.Element => {
  const { projectSDG } = props;
  return (
    <Grid container sx={{ mb: "2rem" }}>
      {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        <Image alt="" src={sdgIconMap.get(projectSDG.sdgTypeId)!} style={{ maxWidth: "9.9rem", height: "9.9rem" }} />
      }
      <Grid container direction="column" flex={1} sx={{ ml: "2rem" }}>
        <Typography variant="bodyCopySBold" component="h4">
          {projectSDG.sdgType.title}
        </Typography>
        <Typography variant="bodyCopyS">
          <Remark>{projectSDG.sdgDescriptionBlurb ?? ""}</Remark>
        </Typography>
      </Grid>
    </Grid>
  );
};

const firstTab = (project?: PortalProjectResponse): number => {
  if (project?.permanenceBlurb) return 1;
  if (project?.otherCoBenefitsBlurb) return 2;
  return 0;
};

type ProjectDetailsTabViewProps = { project: PortalProjectResponse | undefined };

const ProjectDetailsTabView = ({ project }: ProjectDetailsTabViewProps): JSX.Element => {
  const [tabValue, setTabValue] = React.useState(firstTab(project));
  const theme = useTheme();
  const breakpointSM = useMediaQuery(theme.breakpoints.down("sm"));
  const ProjectDetailsMapFeature = false; // @kofi to cleanup (this is just always false for now)
  if (!project) return <></>;

  const handleChange = (event: React.SyntheticEvent, newValue: number): void => {
    setTabValue(newValue);
  };

  const getTabStyle = (index: number): { [key: string]: unknown } => {
    const selected = index === tabValue;
    return {
      color: selected ? COLORS.rubiconGreen : COLORS.rubiconGreenLight,
      backgroundColor: selected ? COLORS.white : COLORS.rubiconGreen,
      p: "2.4rem",
      fontSize: "1.4rem",
      [theme.breakpoints.down("sm")]: {
        p: "1.6rem",
        fontSize: "revert",
      },
      "&:hover": { backgroundColor: COLORS.white, color: COLORS.rubiconGreen },
    };
  };

  return (
    <Grid container>
      <Grid
        item
        width={{
          xs: "100%",
          sm: "22rem",
        }}
        sx={{
          background: COLORS.rubiconGreen,
        }}
      >
        <Tabs
          aria-label="Product details tabs"
          orientation={breakpointSM ? "horizontal" : "vertical"}
          variant={breakpointSM ? "scrollable" : "standard"}
          value={tabValue}
          scrollButtons={false}
          onChange={handleChange}
          TabIndicatorProps={{
            sx: {
              background: COLORS.white,
            },
          }}
        >
          <Tab
            label="Additionality"
            sx={getTabStyle(0)}
            style={{ display: project.additionalityBlurb ? "block" : "none" }}
          />
          <Tab label="Permanence" sx={getTabStyle(1)} style={{ display: project.permanenceBlurb ? "block" : "none" }} />
          <Tab
            label="Other impact"
            sx={getTabStyle(2)}
            style={{ display: project.otherCoBenefitsBlurb ? "block" : "none" }}
          />
          {ProjectDetailsMapFeature && <Tab label="Map" sx={getTabStyle(3)} />}
        </Tabs>
      </Grid>
      <Grid
        container
        item
        xs
        sx={{
          background: COLORS.white,
          overflowY: "auto",
          [theme.breakpoints.down("sm")]: {
            overflowY: "initial",
          },
        }}
      >
        <Grid container item p="2.4rem">
          {project.additionalityBlurb && (
            <TabPanel index={0} value={tabValue}>
              <Grid item sx={{ mb: "2rem" }}>
                <Typography variant="title" sx={{ color: COLORS.rubiconGreen, mb: "2rem" }} component="h3">
                  Additionality
                </Typography>
                <Typography variant="bodyCopy" component="h4" sx={{ fontStyle: "italic" }}>
                  A project is additional if the project activities would not have occurred in the absence of a carbon
                  offset market.
                </Typography>
              </Grid>
              {project.additionalityBlurb && (
                <Typography variant="bodyCopy">
                  <Remark>{project.additionalityBlurb}</Remark>
                </Typography>
              )}
            </TabPanel>
          )}
          {project.permanenceBlurb && (
            <TabPanel index={1} value={tabValue}>
              <Grid item sx={{ mb: "2rem" }}>
                <Typography variant="title" sx={{ color: COLORS.rubiconGreen, mb: "2rem" }} component="h3">
                  Permanence
                </Typography>
                <Typography variant="bodyCopy" component="h4" sx={{ fontStyle: "italic" }}>
                  Permanence is the likelihood that the carbon emissions reduction or sequestration will persist for an
                  atmospherically relevant timeline.
                </Typography>
              </Grid>
              {project.permanenceBlurb && (
                <Typography variant="bodyCopy">
                  <Remark>{project.permanenceBlurb}</Remark>
                </Typography>
              )}
            </TabPanel>
          )}
          {project.otherCoBenefitsBlurb && (
            <TabPanel index={2} value={tabValue}>
              <Grid item sx={{ mb: "2rem" }}>
                <Typography variant="title" sx={{ color: COLORS.rubiconGreen, mb: "2rem" }} component="h3">
                  Environmental and social impact
                </Typography>
                <Typography variant="bodyCopy" component="h4" sx={{ fontStyle: "italic" }}>
                  Projects enable other environmental, social, and economic benefits, beyond the original climate impact
                  of carbon emission reduction (e.g., poverty reduction, biodiversity protection).
                </Typography>
              </Grid>
              {project.projectSDGs &&
                project.projectSDGs.map(
                  (sdg: ProjectSdgResponse) =>
                    sdg.sdgDescriptionBlurb && <ProjectSDGRow projectSDG={sdg} key={sdg.sdgTypeId} />,
                )}
              {project.otherCoBenefitsBlurb && (
                <Grid item>
                  <Typography variant="bodyCopy">
                    <Remark>{project.otherCoBenefitsBlurb}</Remark>
                  </Typography>
                </Grid>
              )}
            </TabPanel>
          )}
          <TabPanel index={3} value={tabValue}>
            <Grid item sx={{ mb: "2rem" }}>
              <Typography variant="title" sx={{ color: COLORS.rubiconGreen }} component="h3">
                Map
              </Typography>
            </Grid>
          </TabPanel>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ProjectDetailsTabView;
