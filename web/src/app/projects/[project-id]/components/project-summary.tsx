import { PropsWith<PERSON>hildren, useEffect, useMemo, useState } from "react";
import { PortalProjectResponse, SdgTypeResponse, uuid } from "@rubiconcarbon/shared-types";
import { utcDateFormat } from "@app/utilities/date";
import { Grid, Stack, Typography, Divider, Switch, Box, CircularProgress, Tooltip, Button } from "@mui/material";
import HeaderAndContentSection from "@app/components/header-and-content-section/header-and-content-section";
import SDGList from "@app/components/sdg-list/sdg-list";
import classes from "../styles/project-summary.module.scss";
import {
  calculator,
  isNothing,
  LinearBar,
  Maybe,
  MaybeNothing,
  numberFormat,
  percentageFormat,
  pickFromRecord,
  px,
  Undefinable,
} from "@rubiconcarbon/frontend-shared";
import IntegrityScore from "@app/app/products/components/integrity-score/integrity-score";
import {
  ArrowDownwardRounded,
  ArrowUpwardRounded,
  AutoAwesomeOutlined,
  DownloadRounded,
  InfoRounded,
} from "@mui/icons-material";
import useFileDownloader from "@app/hooks/use-file-downloader";
import useIsMobile from "@app/providers/mobile";
import IntegrityScoreModal from "@app/app/products/components/integrity-score/components/integrity-score-modal";
import GenericDialog from "@app/components/generic-dialog/generic-dialog";
import { AnimatePresence, motion } from "framer-motion";
import { FeatureCollection } from "geojson";
import ProjectLocation from "./project-location";

/**
 * Key-Value pair of score property names and the relative metadata they contain
 * The value is a tuple of [Label without risk adjustment, Boolean signifying if it is risk adjusted]
 * For now, all risk adjusted properties just simply have "RiskAdjusted" concatenated on the default property label
 */
type ScoreListMetaData = Record<string, [{ label: string; tooltip: string }, boolean]>;

type Score = Pick<
  PortalProjectResponse,
  | "climateImpact"
  | "climateImpactRiskAdjusted"
  | "certificationScore"
  | "futureDeliveryRisk"
  | "additionalityScore"
  | "durabilityScore"
  | "integrityGradeScore"
  | "integrityGradeScoreRiskAdjusted"
>;

type ScoreListProps = {
  riskAdjusted: boolean;
  integral: boolean;
  onRiskAdjustedToggle: () => void;
} & Omit<Score, "integrityGradeScore" | "integrityGradeScoreRiskAdjusted">;

type Snapshot = {
  registry?: string;
  projectId?: string;
  startDate?: Date;
  endDate?: Date;
  developer?: string;
};

type Analyst = {
  analystName?: string;
  analystRole?: string;
  lastReviewDate?: Date;
};

type SideSectionProps = {
  direction?: "row" | "column";
  justifyContent?: "flext-start" | "space-around";
  alignItems?: "flext-start" | "center";
  gap?: number;
  padding?: number | string;
};

type ProjectSummaryContentProps = {
  id: uuid;
  description: string[];
  snapshot?: Snapshot;
  score?: Score;
  analyst?: Analyst;
  sdgs: SdgTypeResponse[];
  canDownloadReport: boolean;
  locationData?: FeatureCollection;
  locationCoordinate?: [number, number];
};

type ProjectSummaryProps = {
  project: MaybeNothing<PortalProjectResponse>;
  locationData?: FeatureCollection;
  locationCoordinate?: [number, number];
};

type ScoreKey =
  | "climateImpact"
  | "climateImpactRiskAdjusted"
  | "certificationScore"
  | "futureDeliveryRisk"
  | "additionalityScore"
  | "durabilityScore";

const ScoreKeyToData: Record<string, { label: string; tooltip: string }> = {
  climateImpact: {
    label: "Climate Impact",
    tooltip: "How effectively the project delivers the amount of emissions reduction or removal it claims.",
  },
  certificationScore: {
    label: "Certification",
    tooltip: "How well the project is designed, implemented, and managed.",
  },
  futureDeliveryRisk: {
    label: "Future Delivery",
    tooltip:
      "The likelihood that the carbon emissions avoided or removed will be kept from the atmosphere for the duration of the project.",
  },
  additionalityScore: {
    label: "Additionality",
    tooltip: `How likely the project is to create change compared to “business as usual”.`,
  },
  durabilityScore: {
    label: "Principled Durability",
    tooltip: "The length of time for which a project is projected to positively impact the climate.",
  },
};

const hasSignificantDelta = (a: Undefinable<number>, b: Undefinable<number>): boolean =>
  calculator(a, { absolutize: true }).subtract(b).isGreaterThanOrEqualTo(1);

const ScoreList = (props: ScoreListProps): JSX.Element => {
  const { riskAdjusted, integral, onRiskAdjustedToggle, ...scores } = props;
  const metadata = Object.keys(scores).reduce((accum, key) => {
    const [baseKey, riskAdjustedKey] = key?.split("RiskAdjusted");

    if (accum[baseKey]) {
      if (!isNothing(riskAdjustedKey)) accum[baseKey][1] = true;
    } else
      accum = {
        ...accum,
        [baseKey]: [ScoreKeyToData[baseKey], !isNothing(riskAdjustedKey)],
      };

    return accum;
  }, {} as ScoreListMetaData);

  return (
    <Stack width="100%" gap={2}>
      {Object.entries(metadata).map(([key, [{ label, tooltip }, canRiskAdjust]]) => {
        const adjusted = riskAdjusted && canRiskAdjust;
        const hasSignificantAdjustment = hasSignificantDelta(
          Number(scores?.[`${key}${canRiskAdjust ? "RiskAdjusted" : ""}` as ScoreKey]),
          Number(scores?.[key as ScoreKey]),
        );
        const percentage =
          scores?.[`${key}${adjusted && hasSignificantAdjustment ? "RiskAdjusted" : ""}` as ScoreKey] ?? 0;

        return (
          <Stack key={key} gap={1}>
            <Stack gap={1} justifyContent="center">
              <Stack gap={1}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2">{label}</Typography>
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography variant="body2" color="GrayText">
                      {numberFormat(percentage, { decimalPlaces: 0 })}/100
                    </Typography>
                    <Tooltip title={tooltip}>
                      <InfoRounded fontSize="small" htmlColor="gray" />
                    </Tooltip>
                  </Stack>
                </Stack>
                <LinearBar
                  animate
                  value={Number(percentage)}
                  scale={(value: number) =>
                    calculator(
                      calculator(value, { absolutize: true })
                        .subtract(Number(scores?.[key as ScoreKey]))
                        .calculate(),
                    ).isBetween(0.5, 1)
                      ? 1.01 * value
                      : value
                  }
                  {...px({
                    ticks: adjusted &&
                      hasSignificantAdjustment && {
                        value: [Number(scores[key as ScoreKey])],
                        size: 15,
                        thickness: 3,
                        color: "#545454",
                        position: "on",
                        rounded: true,
                      },
                  })}
                />
              </Stack>
              <Maybe condition={canRiskAdjust && (integral || hasSignificantAdjustment)}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" padding="5px 0px">
                  <Box>
                    <svg width={0} height={0}>
                      <linearGradient id="linearColors" x1={1} y1={0} x2={1} y2={1}>
                        <stop offset="0%" stopColor="#167A8F" />
                        <stop offset="100%" stopColor="#99CCC5" />
                      </linearGradient>
                    </svg>
                    <AutoAwesomeOutlined fontSize="small" sx={{ fill: "url(#linearColors)" }} />
                  </Box>
                  <Typography variant="body2" fontWeight="500">
                    Show risk-adjusted {label}
                  </Typography>
                  <Switch
                    checked={riskAdjusted}
                    size="small"
                    classes={{
                      root: classes.Switch,
                      checked: classes.Checked,
                      thumb: classes.Thumb,
                      track: classes.Track,
                    }}
                    onChange={onRiskAdjustedToggle}
                  />
                </Stack>
              </Maybe>
            </Stack>
          </Stack>
        );
      })}
    </Stack>
  );
};

const SideSection = ({
  direction = "column",
  justifyContent,
  alignItems,
  gap = 0,
  padding = 0,
  children,
}: PropsWithChildren<SideSectionProps>): JSX.Element => (
  <Stack
    direction={direction}
    justifyContent={justifyContent}
    alignItems={alignItems}
    gap={gap}
    width="100%"
    borderRadius={1}
    padding={padding}
    boxSizing="border-box"
    sx={{ backgroundColor: "white" }}
  >
    {children}
  </Stack>
);

const ProjectSummaryContent = ({
  id,
  description,
  snapshot = {},
  sdgs = [],
  score = {},
  analyst = {},
  canDownloadReport,
  locationData,
  locationCoordinate,
}: ProjectSummaryContentProps): JSX.Element => {
  const isMobile = useIsMobile();
  const isMediumScreen = useIsMobile(1300);
  const isExtraMediumScreen = useIsMobile(1000);

  const { trigger, result } = useFileDownloader({ query: `reporting/projects/report?project_id=${id}` });

  const snapshotData = useMemo(() => {
    return Object.entries(snapshot)
      .filter(([, value]) => !!value)
      .map(([key, value]) => {
        switch (key) {
          case "registry":
            return {
              label: "Registry",
              value,
            };
          case "projectId":
            return {
              label: "Project ID",
              value,
            };
          case "startDate":
            return {
              label: "Project start date",
              value: utcDateFormat(value.toString()),
            };
          case "endDate":
            return {
              label: "Project end date",
              value: utcDateFormat(value.toString()),
            };
          case "developer":
            return {
              label: "Project developer",
              value,
            };
          default:
            return {};
        }
      });
  }, [snapshot]);

  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [openDownloadProgress, setOpenDownloadProgress] = useState<boolean>(false);
  const [riskAdjusted, setRiskAdjusted] = useState<boolean>(false);

  const hasScore = useMemo(() => Object.values(score).some((value) => !isNothing(value)), [score]);
  const canRiskAdjust = useMemo(
    () =>
      (hasScore && !isNothing(score?.climateImpactRiskAdjusted)) || !isNothing(score.integrityGradeScoreRiskAdjusted),
    [hasScore, score?.climateImpactRiskAdjusted, score.integrityGradeScoreRiskAdjusted],
  );
  const hasAnalyst = useMemo(() => Object.values(analyst).some((value) => !isNothing(value)), [analyst]);
  const integityScore = useMemo(
    () =>
      hasScore
        ? canRiskAdjust && riskAdjusted
          ? Math.round(score.integrityGradeScoreRiskAdjusted ?? 0)
          : Math.round(score.integrityGradeScore ?? 0)
        : null,
    [canRiskAdjust, hasScore, riskAdjusted, score?.integrityGradeScore, score?.integrityGradeScoreRiskAdjusted],
  );
  const integrityGradeScoreRiskAdjustedDelta = useMemo(() => {
    const value = calculator(score.integrityGradeScoreRiskAdjusted).subtract(score.integrityGradeScore).calculate();
    return {
      value,
      isPositive: value?.isPositive(),
    };
  }, [score.integrityGradeScore, score.integrityGradeScoreRiskAdjusted]);

  const handleDialogToggle = (): void => setOpenDialog(!openDialog);

  const handleRiskAdjustmentToggle = (): void => setRiskAdjusted(!riskAdjusted);

  useEffect(() => {
    if (result.downloading && !openDownloadProgress) setTimeout(() => setOpenDownloadProgress(true), 500);
    else if (!result.downloading && openDownloadProgress) setOpenDownloadProgress(false);
  }, [openDownloadProgress, result.downloading]);

  return (
    <Grid className={classes.ProjectSummaryContent} container justifyContent="space-between" gap={1} rowGap={2}>
      <Grid item xs={12} md={hasScore ? (isMediumScreen ? (isExtraMediumScreen ? 12 : 7) : 8) : 12}>
        <Stack gap={4} alignItems={isMobile || isExtraMediumScreen ? "center" : "flex-start"}>
          <Maybe condition={!!description.length}>
            <Stack gap={2}>
              {description.map((paragraph, index) => (
                <Typography key={index} variant="bodyCopy">
                  {paragraph}
                </Typography>
              ))}
            </Stack>
          </Maybe>
          <Stack className={classes.Card} gap={2}>
            <Grid container gap={2}>
              {snapshotData.map(({ label = "", value = "" }) => (
                <Grid item xs={12} sm={5} md={3} lg={2.5} xl={2} container direction="column" key={label}>
                  <Typography variant="bodyCopyB" className={classes.CardItemHeaderText}>
                    {label}
                  </Typography>
                  <Typography variant="bodyCopy">{value.toString()}</Typography>
                </Grid>
              ))}
            </Grid>
            <Divider />
            <Stack direction="row" gap={4} alignItems="center">
              <Button
                disabled={!canDownloadReport}
                variant="contained"
                startIcon={<DownloadRounded sx={{ fontSize: 25 }} />}
                sx={{ width: 225 }}
                onClick={trigger}
              >
                Project Report
              </Button>
              <Maybe condition={!!sdgs.length}>
                <Stack gap={1} width="100%">
                  <Typography variant="bodyCopyB" fontSize={20} color="#003049">
                    UN Sustainable Development Goals
                  </Typography>
                  <SDGList sdgs={sdgs} size={40} />
                </Stack>
              </Maybe>
            </Stack>
          </Stack>
          <Maybe condition={!!locationData || !!locationCoordinate?.length}>
            <ProjectLocation locationData={locationData} locationCoordinate={locationCoordinate} />
          </Maybe>
        </Stack>
      </Grid>
      <Maybe condition={!isNothing(integityScore)}>
        <Grid
          item
          xs={12}
          md={isMediumScreen ? (isExtraMediumScreen ? 12 : 4) : 3}
          container
          direction="column"
          alignItems="center"
          gap={3}
          sx={{ backgroundColor: "#E0EDE84D", padding: 2, boxSizing: "content-box" }}
        >
          <Stack alignItems="center">
            <IntegrityScore
              animate
              className={classes.IntegrityScore}
              score={integityScore ?? 0}
              size={200}
              fontSize={55}
              adjustedTick={riskAdjusted ? score.integrityGradeScore : 0}
            />
            <Typography variant="displayS" fontSize={18} color="#595959">
              Rubicon Carbon Integrity Score: {percentageFormat(integityScore ?? 0, { decimalPlaces: 0, scale: 0.01 })}
            </Typography>
          </Stack>
          <AnimatePresence initial={false}>
            {riskAdjusted && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Stack
                  className={classes.WithRiskAdjustmentIndicator}
                  direction="row"
                  justifyContent="center"
                  alignItems="center"
                  gap={1}
                  sx={{
                    backgroundColor: integrityGradeScoreRiskAdjustedDelta.isPositive ? "#2E7D324D" : "#CD505057",
                  }}
                >
                  {integrityGradeScoreRiskAdjustedDelta.isPositive ? (
                    <ArrowUpwardRounded fontSize="small" />
                  ) : (
                    <ArrowDownwardRounded fontSize="small" />
                  )}
                  <Typography fontWeight={500}>
                    {percentageFormat(integrityGradeScoreRiskAdjustedDelta.value.toString(), {
                      scale: 0.01,
                      decimalPlaces: 0,
                    })}
                  </Typography>
                  <Typography fontWeight={500}>With Risk Adjustment</Typography>
                </Stack>
              </motion.div>
            )}
          </AnimatePresence>
          <Typography className={classes.Disclaimer} textAlign="center" variant="body1" onClick={handleDialogToggle}>
            What is the Rubicon Carbon Integrity Score?
          </Typography>
          <Typography
            variant="body2"
            textTransform="uppercase"
            textAlign="left"
            width="100%"
            color="#003049"
            marginTop={1}
          >
            score breakdown
          </Typography>
          <ScoreList
            riskAdjusted={riskAdjusted}
            integral={hasSignificantDelta(
              Number(score.integrityGradeScoreRiskAdjusted),
              Number(score.integrityGradeScore),
            )}
            {...pickFromRecord(score, [
              "climateImpact",
              "climateImpactRiskAdjusted",
              "certificationScore",
              "futureDeliveryRisk",
              "additionalityScore",
              "durabilityScore",
            ])}
            onRiskAdjustedToggle={handleRiskAdjustmentToggle}
          />
          <Maybe condition={hasAnalyst}>
            <SideSection gap={2} padding={2}>
              <Typography color="#003049">ANALYST</Typography>
              <Stack>
                <Maybe condition={!!analyst?.analystName}>
                  <Typography variant="h6" fontWeight={500}>
                    {analyst?.analystName}
                  </Typography>
                </Maybe>
                <Maybe condition={!!analyst?.analystRole}>
                  <Typography>{analyst?.analystRole}</Typography>
                </Maybe>
              </Stack>
              <Maybe condition={!!analyst?.lastReviewDate}>
                <Typography component={Stack} direction="row" gap={1}>
                  <Typography variant="body1" fontWeight={500}>
                    Last Reviewed:
                  </Typography>
                  {utcDateFormat(analyst?.lastReviewDate?.toString())}
                </Typography>
              </Maybe>
            </SideSection>
          </Maybe>
        </Grid>
      </Maybe>
      <IntegrityScoreModal open={openDialog} handleClose={handleDialogToggle} />
      <GenericDialog open={openDownloadProgress} dismissable={false} onClose={() => setOpenDownloadProgress(false)}>
        <Stack alignItems="center" gap={1}>
          <CircularProgress variant="indeterminate" sx={{ color: "#52B5CA" }} />
          <Typography variant="h6">Downloading Analyst Insight Report...</Typography>
        </Stack>
      </GenericDialog>
    </Grid>
  );
};

const ProjectSummary = ({ project, locationData, locationCoordinate }: ProjectSummaryProps): JSX.Element => {
  const description = project?.projectDescription?.split("\n").filter((paragraph) => !!paragraph);

  return (
    <HeaderAndContentSection
      header="Project Summary"
      content={
        <ProjectSummaryContent
          id={project!.id}
          description={description ?? []}
          snapshot={{
            registry: project?.registryName,
            projectId: project?.registryProjectId,
            startDate: project?.startDate,
            endDate: project?.endDate,
            developer: project?.projectDeveloperName,
          }}
          sdgs={(project?.projectSDGs || [])?.map(({ sdgType }) => sdgType)}
          score={{
            climateImpact: project?.climateImpact,
            climateImpactRiskAdjusted: project?.climateImpactRiskAdjusted,
            certificationScore: project?.certificationScore,
            futureDeliveryRisk: project?.futureDeliveryRisk,
            additionalityScore: project?.additionalityScore,
            durabilityScore: project?.durabilityScore,
            integrityGradeScore: project?.integrityGradeScore,
            integrityGradeScoreRiskAdjusted: project?.integrityGradeScoreRiskAdjusted,
          }}
          analyst={{
            analystName: project?.analystName,
            analystRole: project?.analystRole,
            lastReviewDate: project?.lastReviewDate,
          }}
          canDownloadReport={!!project?.pdfReady}
          locationData={locationData}
          locationCoordinate={locationCoordinate}
        />
      }
    />
  );
};

export default ProjectSummary;
