import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import { Suspense } from "react";
import { getProject } from "./data-server";
import ProjectDetails from "./components/project-details";
import Error from "@app/app/error";
import { ProjectRelations } from "@rubiconcarbon/shared-types";

type ProjectPageProps = {
  params: {
    "project-id": string;
  };
};

const ProjectPage = async ({ params }: ProjectPageProps): Promise<JSX.Element> => {
  try {
    const projectId = params["project-id"];
    const project = await getProject(projectId, {
      includeRelations: [ProjectRelations.COUNTRY, ProjectRelations.SDGS, ProjectRelations.PROJECT_TYPE],
    });

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <ProjectDetails productId={undefined} project={project} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
};

export default ProjectPage;
