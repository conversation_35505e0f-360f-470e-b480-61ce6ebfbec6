"use client";

import {
  Maybe as MaybeType,
  PortalProjectResponse,
  ProjectEligibilityAccreditation,
} from "@rubiconcarbon/shared-types";
import {
  Box,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
} from "@mui/material";
import {
  CSSProperties,
  MouseEvent,
  SyntheticEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { PROJECTS_TABLE_COLUMNS } from "./constants";
import Link from "next/link";
import Maybe from "@app/components/maybe/maybe";
import SDGsBox from "@app/components/sdgs-box/sdgs-box";
import TagChip from "@app/components/tag-chip/tag-chip";
import StarIcon from "@mui/icons-material/Star";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import RemoveFavoriteProjectModal from "./favorite-project-modal";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { useLogger } from "@app/providers/logging-provider";
import IntegrityScoreModal from "@app/app/products/components/integrity-score/components/integrity-score-modal";
import IntegrityScore from "@app/app/products/components/integrity-score/integrity-score";
import InteractiveWaterOrgLogo from "@app/app/products/components/water-org/interactive-logo";
import { AxiosContext } from "@app/providers/axios-provider";
import { FavoritesContext } from "@app/providers/favorites-provider";
import ProductIcon from "@app/app/favorites/product-icon";
import ProductsLegend from "./products-legend";
import ProjectsSelector, { FilterData } from "@app/app/products/byo-rct/custom-rct/projects-selector";
import { filterProjects } from "@app/app/products/byo-rct/custom-rct/utils";
import { StoreContext } from "@app/providers/state/store";
import { RCTProject } from "@app/app/products/byo-rct/custom-rct/model";

import classes from "./styles.module.scss";

const getTags = (project: PortalProjectResponse): JSX.Element | "" => {
  if (!project?.country?.name) return "";
  return project.country?.name && <TagChip label={project.country?.name} />;
};

interface ProjectTableProps {
  projectsResponse?: MaybeType<PortalProjectResponse[]>;
  refreshFavorites?: () => void;
}

export default function ProjectsTable(props: ProjectTableProps): JSX.Element {
  const { projectsResponse, refreshFavorites } = props;

  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(100);
  const [projects, setProjects] = useState<MaybeType<PortalProjectResponse[]>>();
  const { logger } = useLogger();
  const [favoriteProjectIds, setFavoriteProjectIds] = useState<string[]>();
  const { setFavoritesCount } = useContext(FavoritesContext);
  const { persistentState } = useContext(StoreContext);

  const { trigger: getFavoriteProjectsIds } = useTriggerRequest<string[]>({
    url: "users/projects/ids",
    queryParams: {
      limit: DEFAULT_MAX_PAGING_LIMIT,
    },
    swrOptions: {
      onSuccess: (data: string[]): void => {
        setFavoriteProjectIds(data);
        setFavoritesCount(data?.length ?? 0);
      },
      onError: (error: any): void => {
        logger.error(`Error fetching user favorite projects ids. ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    setProjects(projectsResponse);
  }, [projectsResponse]);

  // fetching from persistent store
  const selectedProjects = useMemo(
    () => persistentState["custom-rct"].forms.selectedProjects ?? [],
    [persistentState],
  ) as MaybeType<RCTProject[]>;

  useEffect(() => {
    getFavoriteProjectsIds();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectsResponse]);

  const onRowChange = useCallback(() => {
    if (!!refreshFavorites) {
      refreshFavorites();
    }
    getFavoriteProjectsIds();
  }, [getFavoriteProjectsIds, refreshFavorites]);

  const projectsFilterHandler = useCallback(
    (selection: FilterData): void => {
      if (!!selection && !!projects) {
        const filteredProjects = filterProjects(selection, projectsResponse, selectedProjects);
        if (filteredProjects?.length === 0) setProjects([]);
        else {
          setProjects(filteredProjects);
        }
        setPage(0);
      }
    },
    [projects, projectsResponse, selectedProjects],
  );

  const ProjectRow = (props: { row: PortalProjectResponse; onRowChange: () => void }): JSX.Element => {
    const { row, onRowChange } = props;
    const isWaterOrgEligible = row.eligibilityAccreditations.includes(ProjectEligibilityAccreditation.WATER_ORG);
    const [isScoreModalOpen, setIsScoreModalOpen] = useState<boolean>(false);
    const [isConfirmFavoritesModalOpen, setIsConfirmFavoritesModalOpen] = useState<boolean>(false);
    const [isFavorite] = useState<boolean>(!!favoriteProjectIds?.includes(row?.id));
    const { api } = useContext(AxiosContext);

    const addFavoriteProject = useCallback(async (): Promise<void> => {
      try {
        await api.patch<PortalProjectResponse>(`/users/projects/${row?.id}`);
        onRowChange();
      } catch (error) {
        logger.error(`Error adding favorite project ${row?.id}`, {});
      }
    }, [api, row?.id, onRowChange]);

    const favoriteClickHandler = useCallback(
      (event: SyntheticEvent) => {
        event.preventDefault();
        if (!isFavorite) {
          //Adding project to favorites
          addFavoriteProject();
        } else {
          //Remove project from favorites
          setIsConfirmFavoritesModalOpen(true);
        }
      },
      [addFavoriteProject, isFavorite],
    );

    const onCloseFavoritesModal = useCallback(() => {
      setIsConfirmFavoritesModalOpen(false);
    }, []);

    const onConfirmFavoriteAction = useCallback(() => {
      setIsConfirmFavoritesModalOpen(false);
      onRowChange();
    }, [onRowChange]);
    return (
      <>
        <TableRow
          sx={{
            height: "81px",
            padding: "2px",
            backgroundColor: "white",
          }}
        >
          <TableCell sx={{ paddingTop: "14px", width: "15px" }}>
            <Maybe condition={!!row?.integrityGradeScore}>
              <Box onClick={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}>
                <IntegrityScore
                  score={Math.round(row?.integrityGradeScore ?? 0)}
                  separate
                  hideSubtext
                  className={classes.integrityGrade}
                />
              </Box>
            </Maybe>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left", textUnderlineOffset: 4 }}>
            <Stack direction="row" gap={2}>
              <Stack direction="column">
                <Stack direction="row">
                  <Link
                    href={`/projects/${row.id}` as any}
                    style={{ textDecorationColor: "#000000" }}
                    passHref
                    legacyBehavior
                  >
                    <a
                      onClick={(event: MouseEvent<HTMLAnchorElement>): void => event.stopPropagation()}
                      target="_blank"
                    >
                      <Typography className={classes.mainInfo}>{row?.name}</Typography>
                    </a>
                  </Link>
                  <Box className={classes.iconContainer} onClick={favoriteClickHandler}>
                    <Maybe condition={isFavorite}>
                      <StarIcon sx={{ color: "gold" }} />
                    </Maybe>
                    <Maybe condition={!isFavorite}>
                      <StarBorderIcon sx={{ color: "lightgray" }} />
                    </Maybe>
                  </Box>
                </Stack>
                <Typography mt={-0.2} variant="body2" className={classes.supportiveInfo}>
                  {row?.projectType?.category}
                </Typography>
                <Typography variant="body2" className={classes.supportiveInfo}>
                  {row?.registryProjectId}
                </Typography>
              </Stack>
              <Maybe condition={isWaterOrgEligible}>
                <Box sx={{ marginTop: -0.5 }}>
                  <InteractiveWaterOrgLogo width={90} />
                </Box>
              </Maybe>
            </Stack>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            <Typography variant="body2">{row?.priceRange}</Typography>
          </TableCell>
          <TableCell align="center" sx={{ textAlign: "center" }}>
            <Stack direction="row" gap={1} sx={{ textAlign: "center" }}>
              {row?.productIds?.map((id) => <ProductIcon key={id} productId={id} color="rgba(22, 122, 143, 1)" />)}
            </Stack>
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            <SDGsBox height={32} projectSDGs={row?.projectSDGs?.sort((a, b) => (a.sdgTypeId < b.sdgTypeId ? -1 : 1))} />
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            {row?.registryName}
          </TableCell>
          <TableCell align="right" sx={{ textAlign: "left" }}>
            {getTags(row)}
          </TableCell>
        </TableRow>
        <IntegrityScoreModal
          open={isScoreModalOpen}
          handleClose={(): void => setIsScoreModalOpen((isScoreModalOpen) => !isScoreModalOpen)}
        />
        <RemoveFavoriteProjectModal
          project={row}
          onClose={onCloseFavoritesModal}
          onConfirm={onConfirmFavoriteAction}
          isOpen={isConfirmFavoritesModalOpen}
        />
      </>
    );
  };

  const handleChangePage = (event: unknown, newPage: number): void => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Stack gap={2}>
      <ProductsLegend projects={projectsResponse} />
      <TableContainer component={Paper}>
        <Table aria-label="favorite-projects-table">
          <TableHead>
            <TableRow sx={{ backgroundColor: "rgba(250, 250, 250, 1)" }}>
              <TableCell sx={{ padding: "0px" }} colSpan={PROJECTS_TABLE_COLUMNS.length + 1}>
                <ProjectsSelector projects={projects} onFilterHandler={projectsFilterHandler} />
              </TableCell>
            </TableRow>
            <TableRow sx={{ verticalAlign: "top" }}>
              {PROJECTS_TABLE_COLUMNS.map((column, idx) => (
                <TableCell key={idx} sx={{ paddingLeft: "16px", ...(column.style as CSSProperties) }}>
                  <Typography variant="body2" component="h4" fontWeight="700">
                    {column.label}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <Maybe condition={!!favoriteProjectIds}>
            <TableBody sx={{ maxHeight: "200px" }}>
              {projects
                ?.sort((a, b) => a?.projectType?.name.localeCompare(b?.projectType?.name))
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((project) => <ProjectRow key={project.id} row={project} onRowChange={onRowChange} />)}
            </TableBody>
          </Maybe>
          <Maybe condition={!!projectsResponse && projectsResponse?.length === 0}>
            <TableRow sx={{ textAlign: "center" }}>
              <TableCell colSpan={8} sx={{ textAlign: "center" }}>
                <Typography variant="body2" component="h4" fontWeight="500">
                  There are no projects to display
                </Typography>
              </TableCell>
            </TableRow>
          </Maybe>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 100]}
          component="div"
          count={projects?.length ?? 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>
    </Stack>
  );
}
