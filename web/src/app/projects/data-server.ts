import "server-only";

import { PortalProjectQueryResponse, PortalProjectResponse, ProjectRelations, uuid } from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { FetchQueryParams } from "@app/types/query-params";
import { ssrFetch } from "@app/app/data-server";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { generateQueryParams } from "@app/utilities/fetch";

type Relations = "amounts" | "country" | "projectType" | "projectSDGs" | "projectVintages" | "price_range" | "products";

export type ProjectFetchQueryParams = {
  byoBufferEligible?: boolean;
  isByorctEligible?: boolean;
  hasAmount?: boolean;
  hasTrades?: boolean;
  includeBYOMinimum?: boolean;
  ids?: uuid[];
  projectTypeIds?: number[];
} & FetchQueryParams<Relations>;

export const getProjects = async (
  queryParams: ProjectFetchQueryParams = {},
): Promise<MaybeNothing<PortalProjectQueryResponse>> =>
  ssrFetch(
    `projects?${generateQueryParams({
      offset: 0,
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    })}`,
  );

export const getProject = async (
  id: uuid | string,
  queryParams: Pick<ProjectFetchQueryParams, "includeRelations"> = {},
): Promise<MaybeNothing<PortalProjectResponse>> => ssrFetch(`projects/${id}?${generateQueryParams(queryParams)}`);

export const getUserFavoriteVintages = async (
  queryParams: ProjectFetchQueryParams = {},
): Promise<MaybeNothing<PortalProjectQueryResponse>> =>
  ssrFetch(
    `users/projects?${generateQueryParams({
      offset: 0,
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      includeRelations: [
        ProjectRelations.PRICE_RANGE,
        ProjectRelations.SDGS,
        ProjectRelations.COUNTRY,
        ProjectRelations.PROJECT_TYPE,
      ],
      ...queryParams,
    })}`,
  );
