import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import { Suspense } from "react";
import Error from "@app/app/error";
import { getProjects } from "./data-server";
import ExploreProjects from "./components/explore-projects";
import { ProjectRelations } from "@rubiconcarbon/shared-types";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";

const ProjectPage = async (): Promise<JSX.Element> => {
  try {
    const projectsResponse = await getProjects({
      hasAmount: true,
      byoBufferEligible: true,
      limit: DEFAULT_MAX_PAGING_LIMIT,
      isByorctEligible: true,
      includeRelations: [
        ProjectRelations.COUNTRY,
        ProjectRelations.SDGS,
        ProjectRelations.PROJECT_TYPE,
        ProjectRelations.PRICE_RANGE,
        ProjectRelations.PRODUCTS,
      ],
    });

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <ExploreProjects projects={projectsResponse?.data} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
};

export default ProjectPage;
