import { DataTable, customGridDefs } from "@app/components/data-table";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { Stack, Typography } from "@mui/material";
import { GridColDef, GridRenderCellParams, GridValueFormatterParams, GridValueGetterParams } from "@mui/x-data-grid";
import { Maybe, Undefinable, numberFormat } from "@rubiconcarbon/frontend-shared";
import { BLANK } from "@app/constants/common";
import Link from "next/link";
import { VintageSummary } from "./quotes-details";
import TagChip from "@app/components/tag-chip/tag-chip";

import classes from "../styles/summary-table.module.scss";
import { ModelPortfolioComponentResponse } from "@rubiconcarbon/shared-types";
import { useMemo } from "react";

type FuturePipelineTableProps = {
  vintages: ModelPortfolioComponentResponse[];
};

const { medium, large } = customGridDefs;

export const FuturePipelineColumns: GridColDef[] = [
  {
    field: "project",
    headerName: "Project & Vintage",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary, string>): string => {
      const { name = "", project } = params?.row || {};
      return `${name}${name ? ", " : ""}${project?.registryProjectId}`;
    },
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => {
      const { id, name = "", project } = row || {};

      const canRender = !!id && !!name && !!project?.registryProjectId;

      return (
        <>
          <Maybe condition={canRender}>
            <Stack width="100%">
              <Maybe condition={project?.isScienceTeamApproved ?? false}>
                <Link className={classes.Link} href={`/projects/${id}`} target="_blank">
                  <Typography>{project?.name}</Typography>
                </Link>
              </Maybe>
              <Maybe condition={!project?.isScienceTeamApproved}>
                <Typography>{project?.name}</Typography>
              </Maybe>
              <Typography className={classes.SubLabel} variant="body2">
                {project?.registryProjectId} - {name}
              </Typography>
            </Stack>
          </Maybe>
          <Maybe condition={!canRender}>{BLANK}</Maybe>
        </>
      );
    },
    ...large,
  },
  {
    field: "type",
    headerName: "Type",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.projectType?.type,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    ...large,
  },
  {
    field: "country",
    headerName: "Country",
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.country?.name,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => {
      return (
        <>
          <Maybe condition={!!row?.project?.country?.name}>
            <TagChip label={row.project?.country?.name ?? ""} />
          </Maybe>
          <Maybe condition={!row?.project?.country?.name}>{BLANK}</Maybe>
        </>
      );
    },
    ...medium,
  },
  {
    field: "quantity",
    headerName: "Quantity",
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<number> => params?.row?.quantity,
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      numberFormat(params?.value, { separator: "thousand", fallback: BLANK }),
    ...medium,
  },
];

const FuturePipelineTable = ({ vintages = [] }: FuturePipelineTableProps): JSX.Element => {
  const data = useMemo(
    () =>
      vintages.map(
        (x) =>
          ({
            id: `${x.projectId}:${x.vintageInterval}`,
            ...x.vintage,
            name: x.vintageInterval,
            quantity: x.amountAllocated,
            quantityF: x.amountAllocated.toString(),
          }) as VintageSummary,
      ),
    [vintages],
  );

  return (
    <Stack gap={2} sx={{ position: "relative", flexGrow: "100%" }}>
      <Typography variant="bodyCopy" fontWeight={500}>
        Future Pipeline
      </Typography>
      <DataTable
        dataGridProps={{
          "aria-label": "Future Pipeline data grid",
          columns: FuturePipelineColumns,
          rows: data,
          initialState: {
            pagination: { paginationModel: { pageSize: 25 } },
          },
          components: {
            NoRowsOverlay,
          },
          slotProps: {
            toolbar: {
              showColumns: false,
              showFilters: false,
            },
          },
          rowHeight: 70,
          pageSizeOptions: [10, 25, 50, 100],
        }}
      />
    </Stack>
  );
};

export default FuturePipelineTable;
