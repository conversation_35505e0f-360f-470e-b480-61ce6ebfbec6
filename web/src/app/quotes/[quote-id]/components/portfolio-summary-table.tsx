import { DataTable, customGridDefs } from "@app/components/data-table";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { Box, Stack, Typography } from "@mui/material";
import { GridColDef, GridRenderCellParams, GridValueFormatterParams, GridValueGetterParams } from "@mui/x-data-grid";
import {
  pickFromArrayOfRecords,
  searchByKeys,
  px,
  numberFormat,
  Maybe,
  Undefinable,
} from "@rubiconcarbon/frontend-shared";
import { ModelPortfolioComponentResponse, uuid } from "@rubiconcarbon/shared-types";
import { now } from "lodash";
import { useState, useMemo, ChangeEvent, MouseEvent, useContext } from "react";
import { useDebounce } from "react-use";
import { BLANK } from "@app/constants/common";
import Link from "next/link";
import { SummaryModel } from "../../utilities/model";
import { VintageSummary } from "./quotes-details";
import SDGList from "@app/components/sdg-list/sdg-list";
import TagChip from "@app/components/tag-chip/tag-chip";
import IntegrityScore from "@app/app/products/components/integrity-score/integrity-score";
import IntegrityScoreModal from "@app/app/products/components/integrity-score/components/integrity-score-modal";
import { StoreContext } from "@app/providers/state/store";

import classes from "../styles/summary-table.module.scss";

type SummaryRiskScoreProps = {
  row: VintageSummary;
};

type VintageTableProps = {
  vintages: ModelPortfolioComponentResponse[];
};

const THREE_HUNDRED = 300;
const { small, medium, large } = customGridDefs;

const SummaryRiskScore = ({ row }: SummaryRiskScoreProps): JSX.Element => {
  const score = Math.round(row?.project?.integrityGradeScore ?? 0);

  const [open, setOpen] = useState<boolean>(false);

  return (
    <>
      <Maybe condition={!!score}>
        <Box onClick={() => setOpen(true)}>
          <IntegrityScore className={classes.Score} score={score ?? 0} separate hideSubtext />
        </Box>
        <IntegrityScoreModal open={open} handleClose={() => setOpen(false)} />
      </Maybe>
      <Maybe condition={!score}>{BLANK}</Maybe>
    </>
  );
};

export const PortfolioSummaryColumns: GridColDef[] = [
  {
    field: "risk",
    headerName: "",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.overallRiskScore,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => (
      <Box sx={{ paddingLeft: "20px" }}>
        <SummaryRiskScore row={row} />
      </Box>
    ),
    ...small,
  },
  {
    field: "project",
    headerName: "Project & Vintage",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary, string>): string => {
      const { project } = params?.row || {};
      return `${project?.name}${project?.name ? ", " : ""}${project?.registryProjectId}`;
    },
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => {
      const { id, name = "", project } = row || {};

      const canRender = !!id && !!name && !!project?.registryProjectId && !!project?.name;

      return (
        <>
          <Maybe condition={canRender}>
            <Stack width="100%">
              <Maybe condition={project?.isScienceTeamApproved ?? false}>
                <Link className={classes.Link} href={`/projects/${project?.id}`} target="_blank">
                  <Typography>{project?.name}</Typography>
                </Link>
              </Maybe>
              <Maybe condition={!project?.isScienceTeamApproved}>
                <Typography>{project?.name}</Typography>
              </Maybe>
              <Typography className={classes.SubLabel} variant="body2">
                {project?.registryProjectId} - {name}
              </Typography>
            </Stack>
          </Maybe>
          <Maybe condition={!canRender}>{BLANK}</Maybe>
        </>
      );
    },
    ...large,
  },
  {
    field: "type",
    headerName: "Type",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.projectType?.type,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    ...medium,
  },
  {
    field: "source",
    headerName: "Source",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.registryName,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    ...medium,
  },
  {
    field: "country",
    headerName: "Country",
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string> =>
      params?.row?.project?.country?.name,
    valueFormatter: (params: GridValueFormatterParams<string>): string => params?.value || BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => {
      return (
        <>
          <Maybe condition={!!row?.project?.country?.name}>
            <TagChip label={row.project?.country?.name ?? ""} />
          </Maybe>
          <Maybe condition={!row?.project?.country?.name}>{BLANK}</Maybe>
        </>
      );
    },
    ...medium,
  },
  {
    field: "sdgs",
    headerName: "UN SDGs",
    cellClassName: classes.QuoteCell,
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<string[]> =>
      params?.row?.project?.projectSDGs?.map(({ sdgType }) => sdgType)?.map(({ title }) => title),
    valueFormatter: (params: GridValueFormatterParams<string[]>): string =>
      !!params?.value?.length
        ? params?.value?.reduce(
            (accum, sdg, index, array) => `${accum}${index > 0 && index < array.length ? `, ${sdg}` : `${sdg}`}`,
          )
        : BLANK,
    renderCell: ({ row }: GridRenderCellParams<VintageSummary>): JSX.Element => {
      const sdgs = row?.project?.projectSDGs?.map(({ sdgType }) => sdgType);
      return (
        <>
          <Maybe condition={!!sdgs && !!sdgs?.length}>
            <SDGList sdgs={sdgs} component="stack" size={35} />
          </Maybe>
          <Maybe condition={!sdgs || !sdgs?.length}>{BLANK}</Maybe>
        </>
      );
    },
    sortable: false,
    ...medium,
  },
  {
    field: "quantity",
    headerName: "Quantity",
    valueGetter: (params: GridValueGetterParams<VintageSummary>): Undefinable<number> => params?.row?.quantity,
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      numberFormat(params?.value, { separator: "thousand", fallback: BLANK }),
    ...medium,
  },
];

const ProjectSummaryTable = ({ vintages = [] }: VintageTableProps): JSX.Element => {
  const { ephemeralState } = useContext(StoreContext);

  const [searchTerm, setSearchTerm] = useState<string>();
  const [filteredIds, setFilteredIds] = useState<uuid[]>();

  const searchData: SummaryModel[] = useMemo(() => {
    return vintages.map((x) => ({
      id: x.vintageId!,
      project: x.vintage!.project!.name,
      source: x.vintage!.project!.registryName,
      type: x?.project?.projectType?.name ?? "",
      risk: x.vintage?.project?.overallRiskScore,
      country: x.vintage!.project!.country!.name,
      sdgs: x.vintage?.project?.projectSDGs?.map((x) => x.sdgTypeId).toString(),
      quantity: x.amountAllocated,
      quantityF: x.amountAllocated.toString(),
    }));
  }, [vintages]);

  const data = useMemo(
    () =>
      vintages.map(
        (x) =>
          ({
            ...x.vintage,
            quantity: x.amountAllocated,
            quantityF: x.amountAllocated.toString(),
          }) as VintageSummary,
      ),
    [vintages],
  );

  const tableRows = useMemo(() => {
    if (!!filteredIds) return data?.filter(({ id }) => filteredIds.includes(id));
    return data;
  }, [filteredIds, data]);

  useDebounce(
    () => {
      if (searchTerm !== undefined) {
        const ids = pickFromArrayOfRecords(
          searchByKeys(searchTerm, searchData, [
            "project",
            "source",
            "type",
            "risk",
            "country",
            "sdgs",
            "quantity",
            "quantityF",
          ]),
          ["id"],
        ).reduce((ids: uuid[], { id }) => [...ids, id] as uuid[], []);

        if (vintages?.length === ids?.length) setFilteredIds(undefined);
        else setFilteredIds(ids);
      }
    },
    THREE_HUNDRED,
    [searchTerm, vintages],
  );

  const handleSearch = (event: ChangeEvent<HTMLInputElement>): void => {
    event?.preventDefault();
    setSearchTerm(event?.currentTarget?.value);
  };

  const handleSearchClear = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    setSearchTerm("");
  };

  return (
    <Stack gap={2} sx={{ position: "relative", flexGrow: "100%" }}>
      <Typography variant="bodyCopy" fontWeight={500}>
        Portfolio Summary
      </Typography>
      <DataTable
        dataGridProps={{
          "aria-label": "Portfolio Summary data grid",
          columns: PortfolioSummaryColumns,
          rows: tableRows,
          initialState: {
            pagination: { paginationModel: { pageSize: 25 } },
          },
          components: {
            NoRowsOverlay,
          },
          slotProps: {
            toolbar: {
              searchOption: {
                value: searchTerm,
                onSearch: handleSearch,
                onClear: handleSearchClear,
              },
              ...px(
                {
                  csvOptions: tableRows?.length > 0 && {
                    allColumns: false,
                    fileName: `${ephemeralState?.organization?.name}_${now()}_Quote_Portfolio-Summary`,
                  },
                },
                [false],
              ),
            },
          },
          rowHeight: 70,
          pageSizeOptions: [10, 25, 50, 100],
        }}
      />
    </Stack>
  );
};

export default ProjectSummaryTable;
