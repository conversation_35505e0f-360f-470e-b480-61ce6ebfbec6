import { Stack, Typography, useMediaQuery } from "@mui/material";
import { currencyFormat, numberFormat } from "@rubiconcarbon/frontend-shared";
import { PublicModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { BLANK } from "@app/constants/common";

import classes from "../styles/price-summary.module.scss";
import { RiskAdjIcon } from "@app/components/custom-icons/risk-adj";

type PriceSummaryProps = {
  quote: PublicModelPortfolioResponse;
};

const PriceSummary = ({ quote }: PriceSummaryProps): JSX.Element => {
  const isMobile = useMediaQuery("(max-width: 450px)");
  const shouldStack = useMediaQuery("(max-width: 1360px)");

  const { priceEstimate, modelPortfolioComponents = [], includeRiskAdjustment } = quote;
  const quantity = !!modelPortfolioComponents?.length
    ? modelPortfolioComponents
        ?.filter(({ isBufferComponent }) => !isBufferComponent)
        ?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0)
    : undefined;
  const perTonne = !!quantity && !!priceEstimate ? +priceEstimate / quantity : undefined;

  return (
    <Stack className={classes.Container} width={shouldStack ? "100%" : "25%"} gap={2}>
      <Typography variant="bodyCopy" fontWeight={500}>
        Price Summary
      </Typography>
      <Stack gap={shouldStack ? 1 : 6} pt={2}>
        <Stack direction="row" justifyContent="space-between">
          <Typography className={classes.Label}>Per Tonne</Typography>
          <Typography>{currencyFormat(perTonne as any, { fallback: BLANK })}</Typography>
        </Stack>
        <Stack direction="row" justifyContent="space-between">
          <Typography className={classes.Label}>Quantity</Typography>
          <Typography>{numberFormat(quantity as any, { fallback: BLANK })}</Typography>
        </Stack>
        <Stack direction="row" justifyContent="space-between">
          <Typography className={classes.Label}>Total</Typography>
          <Typography>
            {currencyFormat(priceEstimate ? priceEstimate : (undefined as any), { fallback: BLANK })}
          </Typography>
        </Stack>
        <Stack direction="row" gap={2} pt={1}>
          <RiskAdjIcon width={"22px"} />
          <Typography className={classes.BooleanTitle}>
            Risk adjustment is <strong>{!includeRiskAdjustment ? "not " : ""}included</strong>.
          </Typography>
        </Stack>
        <Typography className={classes.Label} variant="body2" pt={!isMobile ? 2 : 0}>
          The provided price estimate is for indicative purposes only and is not legally binding.
        </Typography>
      </Stack>
    </Stack>
  );
};

export default PriceSummary;
