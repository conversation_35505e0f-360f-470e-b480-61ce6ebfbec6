import { Stack, Typography, useMediaQuery } from "@mui/material";
import { PortalProjectVintageResponse, PublicModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import ProjectDistribution, { ProjectDistributionData } from "./project-distribution";
import PriceSummary from "./price-summary";
import ProjectSummaryTable from "./portfolio-summary-table";
import { BLANK } from "@app/constants/common";
import { utcDateFormat } from "@app/utilities/date";
import { Maybe, Undefinable } from "@rubiconcarbon/frontend-shared";
import { useMemo } from "react";
import Link from "next/link";
import { ArrowBack } from "@mui/icons-material";
import FuturePipelineTable from "./future-pipeline-table";
import { groupBy, sum } from "lodash";

import classes from "../styles/quote-details.module.scss";

export type VintageSummary = {
  quantity: Undefinable<number>;
  quantityF?: string;
} & PortalProjectVintageResponse;

type QuoteDetailsProps = {
  quote: PublicModelPortfolioResponse;
};

const QuoteDetails = ({ quote }: QuoteDetailsProps): JSX.Element => {
  const isMobile = useMediaQuery("(max-width: 500px)");
  const shouldStack = useMediaQuery("(max-width: 1360px)");

  const distribution: ProjectDistributionData = useMemo(() => {
    const result = {} as ProjectDistributionData;
    const g = groupBy(quote.modelPortfolioComponents, (x) => x.vintage?.project?.country?.name);

    Object.entries(g).forEach(
      ([k, v]) => (result[k] = { count: v.length, allocation: sum(v.map((x) => x.amountAllocated)) }),
    );

    return result;
  }, [quote]);

  const portfolioVintages =
    quote.modelPortfolioComponents?.filter(({ projectPipeline, vintageId }) => !projectPipeline && vintageId) ?? [];

  const futureVintages =
    quote.modelPortfolioComponents?.filter(({ projectPipeline, vintageId }) => projectPipeline || !vintageId) ?? [];

  return (
    <Stack className={classes.Container} gap={2}>
      <Stack
        direction={isMobile ? "column" : "row"}
        justifyContent={isMobile ? "center" : "space-between"}
        alignItems="center"
      >
        <Typography className={classes.SubHeader}>
          <Link
            href={"/quotes" as any}
            style={{ alignItems: "center", display: "inline-flex", color: "inherit", textDecoration: "none" }}
          >
            <ArrowBack sx={{ fontSize: "inherit", marginRight: "0.75rem" }} aria-hidden="true" />
            Back to portfolio watchlist
          </Link>
        </Typography>
        <Typography className={classes.SubHeader}>
          <strong>Creation Date: </strong> {utcDateFormat(quote?.createdAt as any, { defaultValue: BLANK })}
        </Typography>
      </Stack>
      <Stack direction={shouldStack ? "column" : "row"} gap={2}>
        <ProjectDistribution data={distribution} />
        <PriceSummary quote={quote} />
      </Stack>
      <ProjectSummaryTable {...{ vintages: portfolioVintages }} />
      <Maybe condition={futureVintages !== undefined && futureVintages.length > 0}>
        <FuturePipelineTable {...{ vintages: futureVintages }} />
      </Maybe>
    </Stack>
  );
};

export default QuoteDetails;
