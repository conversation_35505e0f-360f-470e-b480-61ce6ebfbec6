import { ssrFetch } from "@app/app/data-server";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { ModelPortfolioRelations, PublicModelPortfolioResponse, uuid } from "@rubiconcarbon/shared-types";

export const getQuote = async (id: uuid): Promise<MaybeNothing<PublicModelPortfolioResponse>> => {
  const s = ssrFetch(
    `model-portfolios/${id}?includeRelations=${ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS}&includeRelations=${ModelPortfolioRelations.PROJECT}&includeRelations=${ModelPortfolioRelations.PROJECT_VINTAGE}`,
  );

  return s as any;
};
