"use client";

import { Box } from "@mui/material";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { PublicModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { use } from "react";
import PageContentHeading from "@app/app/page-components/heading";
import QuoteDetails from "./components/quotes-details";
import { byoNameFormatter } from "@app/utilities/byo-name";
import HeadingContainer from "@app/app/components/heading-container/heading-container";
import ContentContainer from "@app/app/components/content-container/content-container";

type QuoteProps = {
  quote: Promise<MaybeNothing<PublicModelPortfolioResponse>>;
};

const Quote = ({ quote: quotesPromise }: QuoteProps): JSX.Element => {
  const quote = use(quotesPromise) as PublicModelPortfolioResponse;

  return (
    <>
      <HeadingContainer>
        <PageContentHeading
          headingId="quote-details-page-heading"
          headingText={
            <span>
              Portfolio{" "}
              <Box
                component="span"
                fontWeight={450}
                textTransform={quote?.name?.startsWith("BYO") ? "uppercase" : "initial"}
              >
                {byoNameFormatter(quote?.name)}
              </Box>
            </span>
          }
        />
      </HeadingContainer>
      <ContentContainer>
        <QuoteDetails quote={quote} />
      </ContentContainer>
    </>
  );
};

export default Quote;
