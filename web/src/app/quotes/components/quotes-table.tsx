import { DataTable, customGridDefs } from "@app/components/data-table";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { Grid, Typography } from "@mui/material";
import {
  GridColDef,
  GridRenderCellParams,
  GridSortDirection,
  GridSortItem,
  GridSortModel,
  GridValueFormatterParams,
  GridValueGetterParams,
  NoRowsOverlayPropsOverrides,
} from "@mui/x-data-grid";
import {
  pickFromArrayOfRecords,
  searchByKeys,
  px,
  numberFormat,
  currencyFormat,
  Undefinable,
} from "@rubiconcarbon/frontend-shared";
import { PublicModelPortfolioQueryResponse, PublicModelPortfolioResponse, uuid } from "@rubiconcarbon/shared-types";
import { now } from "lodash";
import { useState, useMemo, ChangeEvent, MouseEvent, useContext } from "react";
import { useDebounce, useUpdateEffect } from "react-use";
import { toQuotesModel } from "../utilities/model";
import { BLANK } from "@app/constants/common";
import { utcDateFormat } from "@app/utilities/date";
import Link from "next/link";
import { PaginationModel } from "@app/types/table";
import { byoNameFormatter } from "@app/utilities/byo-name";
import { StoreContext } from "@app/providers/state/store";

export type QuotesServerSortKey = keyof typeof QuotesServerSortMap;

type QuotesTableProps = {
  loading: boolean;
  pagedQuotes: PublicModelPortfolioQueryResponse;
  onSort: (field: QuotesServerSortKey, sort: GridSortDirection) => void;
  onPage: (page: number, pageSize: number) => void;
};

export const QuotesServerSortMap = {
  // todo: add table column to backend field map if it is not the same
};

const DefaultSortModel: GridSortItem = { field: "createdAt", sort: "desc" };
const THREE_HUNDRED = 300;
const GRID_FEATURE_MODE = "client"; // switching to client for now as server api is insufficient for filtering and export
const { small, medium, large } = customGridDefs;

export const QuotesColumns: GridColDef[] = [
  {
    field: "uiKey",
    headerName: "ID",
    valueFormatter: (params: GridValueFormatterParams<string>): string => {
      const value = params?.value;
      return byoNameFormatter(value);
    },
    ...small,
  },
  {
    field: "name",
    headerName: "Portfolio Name",
    valueFormatter: (params: GridValueFormatterParams<string>): string => {
      return params?.value;
    },
    ...large,
  },
  {
    field: "createdAt",
    headerName: "Date",
    type: "dateTime",
    valueFormatter: (params: GridValueFormatterParams<string>): string =>
      utcDateFormat(params?.value, { defaultValue: BLANK }),
    ...medium,
  },
  {
    // not server compatible as it stands
    field: "quantity",
    headerName: "Quantity",
    valueGetter: (params: GridValueGetterParams<PublicModelPortfolioResponse>): Undefinable<number> =>
      !!params?.row?.modelPortfolioComponents?.length
        ? params?.row?.modelPortfolioComponents
            ?.filter(({ isBufferComponent }) => !isBufferComponent)
            .reduce((sum, { amountAllocated }) => sum + amountAllocated, 0)
        : undefined,
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      numberFormat(params?.value, { separator: "thousand", fallback: BLANK }),
    ...medium,
  },
  {
    // not server compatible as it stands
    field: "pricePerUnit",
    headerName: "Price Per Unit",
    valueGetter: (params: GridValueGetterParams<PublicModelPortfolioResponse>): Undefinable<number> => {
      const quantity = !!params?.row?.modelPortfolioComponents?.length
        ? params?.row?.modelPortfolioComponents
            ?.filter(({ isBufferComponent }) => !isBufferComponent)
            ?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0)
        : undefined;
      return !!quantity && params?.row?.priceEstimate ? +params?.row?.priceEstimate / quantity : undefined;
    },
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      currencyFormat(params?.value, { separator: "thousand", fallback: BLANK }),
    ...medium,
  },
  {
    // not server compatible as it stands
    field: "priceEstimate",
    headerName: "Total Amount",
    valueGetter: (params: GridValueGetterParams<PublicModelPortfolioResponse>): Undefinable<number> =>
      !!params?.row?.priceEstimate ? +params?.row?.priceEstimate : undefined,
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      currencyFormat(params?.value, { fallback: BLANK }),
    ...medium,
  },
  {
    field: "Details",
    headerName: "",
    sortable: false,
    filterable: false,
    hideable: false,
    disableExport: true,
    renderCell: ({ row }: GridRenderCellParams<PublicModelPortfolioResponse>): JSX.Element => (
      <Link href={`/quotes/${row?.id}` as any}>
        <Typography>View details</Typography>
      </Link>
    ),
    ...medium,
    align: "center",
  },
];

const QuotesTable = ({ loading, pagedQuotes }: QuotesTableProps): JSX.Element => {
  const { ephemeralState } = useContext(StoreContext);

  const [sortModel, setSortModel] = useState<GridSortModel>([DefaultSortModel]);
  const [searchTerm, setSearchTerm] = useState<string>();
  const [filteredIds, setFilteredIds] = useState<uuid[]>();
  const [paginationModel, setPaginationModel] = useState<PaginationModel>({ page: 0, pageSize: 100 });
  const [debounceMS, setDebounceMS] = useState<number>(THREE_HUNDRED);

  // commented out for now as server api is insufficient for filtering and export
  // const rowCount = useMemo(() => pagedQuotes?.page?.totalCount || 0, [pagedQuotes?.page?.totalCount]);
  const pagedRows = useMemo(() => pagedQuotes?.data, [pagedQuotes?.data]);

  const tableRows = useMemo(() => {
    if (!!filteredIds) return pagedRows?.filter(({ id }) => filteredIds.includes(id));
    return pagedRows;
  }, [filteredIds, pagedRows]);

  useDebounce(
    () => {
      if (searchTerm !== undefined) {
        const ids = pickFromArrayOfRecords(
          searchByKeys(searchTerm, pagedRows?.map(toQuotesModel), [
            "name",
            "uiKey",
            "createdAt",
            "quantity",
            "quantityF",
            "pricePerUnit",
            "pricePerUnitF",
            "priceEstimate",
            "priceEstimateF",
          ]),
          ["id"],
        ).reduce((ids: uuid[], { id }) => [...ids, id] as uuid[], []);

        if (pagedRows?.length === ids?.length) setFilteredIds(undefined);
        else setFilteredIds(ids);
      }
    },
    debounceMS,
    [searchTerm, pagedRows, paginationModel],
  );

  useUpdateEffect(() => {
    if (loading) setDebounceMS(0);
    else setTimeout(() => setDebounceMS(THREE_HUNDRED), THREE_HUNDRED);
  }, [loading]);

  // commented out for now as server api is insufficient for filtering and export
  // useUpdateEffect(() => {
  //   const [{ field, sort } = { field: null, sort: null }] = sortModel;
  //   onSort(field as QuotesServerSortKey, sort);
  // }, [sortModel]);

  // commented out for now as server api is insufficient for filtering and export
  // useUpdateEffect(() => {
  //   const { page, pageSize } = paginationModel;
  //   onPage(page, pageSize);
  // }, [paginationModel]);

  const handleSearch = (event: ChangeEvent<HTMLInputElement>): void => {
    event?.preventDefault();
    setSearchTerm(event?.currentTarget?.value);
  };

  const handleSearchClear = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    setSearchTerm("");
  };

  return (
    <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", ">*": { pt: "10px" } }}>
      <DataTable
        dataGridProps={{
          "aria-label": "Portfolio Watchlist data grid",
          columns: QuotesColumns,
          rows: tableRows,
          components: {
            NoRowsOverlay,
          },
          slotProps: {
            noRowsOverlay: {
              message: "When you have a quote with Rubicon Carbon, it will appear here.",
            } as NoRowsOverlayPropsOverrides,
            toolbar: {
              searchOption: {
                value: searchTerm,
                onSearch: handleSearch,
                onClear: handleSearchClear,
              },
              ...px(
                {
                  csvOptions: tableRows?.length > 0 && {
                    allColumns: false,
                    fileName: `${ephemeralState?.organization?.name}_${now()}_Portolio_Watchlist`,
                  },
                },
                [false],
              ),
            },
          },
          rowHeight: 60,
          // rowCount, // commented out for now as server api is insufficient for filtering and export
          loading,
          sortingMode: GRID_FEATURE_MODE,
          paginationMode: GRID_FEATURE_MODE,
          pageSizeOptions: [10, 25, 50, 100],
          sortModel,
          paginationModel,
          onSortModelChange: setSortModel,
          onPaginationModelChange: setPaginationModel,
        }}
      />
    </Grid>
  );
};

export default QuotesTable;
