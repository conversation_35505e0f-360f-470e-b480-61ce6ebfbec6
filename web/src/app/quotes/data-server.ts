import "server-only";

import { PublicModelPortfolioQueryResponse } from "@rubiconcarbon/shared-types";
import { getToken } from "@app/providers/auth-provider/server";
import { environment } from "@app/environment";
import { HttpStatusMapper } from "@app/constants/http";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";

export const getQuotes = async (): Promise<MaybeNothing<PublicModelPortfolioQueryResponse>> => {
  const token = getToken();

  if (!token) return;

  const res = await fetch(
    `${environment.server.api.ssr}/model-portfolios?includeTotalCount=true&offset=0&limit=500&orderBy=createdAt&orderByDirection=desc_nulls_last`,
    {
      cache: "no-store",
      headers: { Authorization: `Bearer ${token}` },
    },
  );

  if (!res.ok) throw new Error(HttpStatusMapper[res.status]);

  return await res.json();
};
