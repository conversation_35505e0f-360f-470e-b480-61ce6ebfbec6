"use client";

import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { PublicModelPortfolioQueryResponse, PublicModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { use } from "react";
import PageContentHeading from "../page-components/heading";
import { PortalUpdaterContextProvider } from "../portal-updater-context";
import useTableServerActions from "@app/hooks/use-table-server-actions";
import QuotesTable, { QuotesServerSortKey, QuotesServerSortMap } from "./components/quotes-table";
import HeadingContainer from "../components/heading-container/heading-container";
import ContentContainer from "../components/content-container/content-container";

type QuotesProps = {
  pagedQuotes: Promise<MaybeNothing<PublicModelPortfolioQueryResponse>>;
};

const Quotes = ({ pagedQuotes: pagedQuotesPromise }: QuotesProps): JSX.Element => {
  const {
    data: pagedQuotes,
    loading,
    refresh,
    onSortChange,
    onPageChange,
  } = useTableServerActions<PublicModelPortfolioResponse, QuotesServerSortKey>({
    url: "/model-portfolios",
    seed: { data: use(pagedQuotesPromise) as any },
    sortMap: QuotesServerSortMap,
  });

  return (
    <>
      <HeadingContainer>
        <PortalUpdaterContextProvider updater={refresh}>
          <PageContentHeading
            headingId="portfolio-watchlist-page-heading"
            headingText="Portfolio Watchlist"
            buttons={{ byo: true }}
          />
        </PortalUpdaterContextProvider>
      </HeadingContainer>
      <ContentContainer>
        <QuotesTable {...{ loading, pagedQuotes, onSort: onSortChange, onPage: onPageChange }} />
      </ContentContainer>
    </>
  );
};

export default Quotes;
