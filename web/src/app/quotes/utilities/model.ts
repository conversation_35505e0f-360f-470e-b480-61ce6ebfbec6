import Decimal from "decimal.js";
import { PublicModelPortfolioResponse, uuid } from "@rubiconcarbon/shared-types";
import { QuotesColumns } from "../components/quotes-table";
import { Undefinable } from "@rubiconcarbon/frontend-shared";

type QuoteModel = {
  id: uuid;
  uiKey: string;
  name: string;
  createdAt: string;
  quantity: Undefinable<number>;
  quantityF: string;
  pricePerUnit: Undefinable<number>;
  pricePerUnitF: string;
  priceEstimate: Undefinable<Decimal>;
  priceEstimateF: string;
};

export type SummaryModel = {
  id: uuid;
  project: string;
  type: string;
  source?: string;
  risk?: string;
  country: string;
  sdgs?: string;
  quantity: Undefinable<number>;
  quantityF: string;
};

export const toQuotesModel = (row: PublicModelPortfolioResponse): QuoteModel => {
  const quantity = (QuotesColumns.find(({ field }) => field === "quantity") as any)?.valueGetter({ row });
  const pricePerUnit = (QuotesColumns.find(({ field }) => field === "pricePerUnit") as any)?.valueGetter({ row });
  const priceEstimate = (QuotesColumns.find(({ field }) => field === "priceEstimate") as any)?.valueGetter({ row });

  return {
    id: row?.id,
    uiKey: (QuotesColumns.find(({ field }) => field === "uiKey") as any)?.valueFormatter({
      value: row?.uiKey,
    }),
    name: (QuotesColumns.find(({ field }) => field === "name") as any)?.valueFormatter({ value: row?.name }),
    createdAt: (QuotesColumns.find(({ field }) => field === "createdAt") as any)?.valueFormatter({
      value: row?.createdAt,
    }),
    quantity,
    quantityF: (QuotesColumns.find(({ field }) => field === "quantity") as any)?.valueFormatter({ value: quantity }),
    pricePerUnit,
    pricePerUnitF: (QuotesColumns.find(({ field }) => field === "pricePerUnit") as any)?.valueFormatter({
      value: pricePerUnit,
    }),
    priceEstimate,
    priceEstimateF: (QuotesColumns.find(({ field }) => field === "priceEstimate") as any)?.valueFormatter({
      value: priceEstimate,
    }),
  };
};
