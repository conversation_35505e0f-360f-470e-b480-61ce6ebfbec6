import COLORS from "@app/theme/colors";
import { LinkRounded } from "@mui/icons-material";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utton,
  <PERSON>ge,
  <PERSON><PERSON>,
  <PERSON>row,
  Paper,
  ClickAwayListener,
  MenuList,
  MenuItem,
  Typography,
  Box,
} from "@mui/material";
import { px } from "@rubiconcarbon/frontend-shared";
import { RetirementLink } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { MouseEvent, useRef, useState } from "react";

import classes from "../styles/retirement-links.module.scss";

type RegistryLinksProps = {
  links: RetirementLink[];
};

const RegistryLinks = ({ links }: RegistryLinksProps): JSX.Element => {
  const noLinks = !links.length;

  const anchorRef = useRef<HTMLButtonElement>(null);
  const [openPopper, setOpenPopper] = useState<boolean>(false);

  const transformURL = (url: string): string => {
    const protocol = /^(https?:\/\/)?/;
    const domain = /^([A-z0-9.-]+)(\/)/;
    const endSlashAndBeyond = /(\/[A-z0-9.?=-]*)$/;

    const withoutProtocolPath = url.replace(protocol, "");

    const domainPath = withoutProtocolPath.match(domain)?.[0] || "";
    const endSlashAndBeyondPath = withoutProtocolPath.match(endSlashAndBeyond)?.[0];

    if (!!endSlashAndBeyondPath && withoutProtocolPath !== `${domainPath.replace("/", "")}${endSlashAndBeyondPath}`)
      return `${domainPath}...${endSlashAndBeyondPath}`;
    else return withoutProtocolPath;
  };

  const toOpennableUrl = (url: string): string => {
    const protocol = /^(http|https):\/\//i;

    if (protocol.test(url)) return url;
    else return `http://${url}`;
  };

  const handlePopperToggle = (): void => setOpenPopper((prevOpen) => !prevOpen);

  const handlePopperClose = (event: Event | React.SyntheticEvent): void => {
    if (openPopper) {
      event?.preventDefault();
      if (!anchorRef?.current?.contains(event.target as HTMLElement)) setOpenPopper(false);
    }
  };

  const handleLinkClick = (event: MouseEvent<HTMLAnchorElement>): void => {
    event.stopPropagation();
    setOpenPopper(false);
  };

  return (
    <>
      <Tooltip title={!openPopper ? (noLinks ? "No links added" : "View links") : ""} arrow>
        <Box className={classes.LinkDropdownButtonContainer}>
          <IconButton
            ref={anchorRef}
            disabled={noLinks}
            className={classes.LinkDropdownButton}
            sx={{ ...px({ backgroundColor: openPopper && "rgb(9 68 54 / 24%)" }) }}
            aria-controls={openPopper ? "links-menu" : undefined}
            aria-expanded={openPopper ? "true" : undefined}
            aria-haspopup="true"
            onClick={handlePopperToggle}
          >
            <Badge color="success" badgeContent={links.length} classes={{ badge: classes.Badge }}>
              <LinkRounded htmlColor={noLinks ? "gray" : COLORS.rubiconGreen} />
            </Badge>
          </IconButton>
        </Box>
      </Tooltip>
      <Popper id="registry-links" open={openPopper} anchorEl={anchorRef.current} transition keepMounted>
        {({ TransitionProps }) => (
          <Grow {...TransitionProps}>
            <Paper>
              <ClickAwayListener onClickAway={handlePopperClose}>
                <MenuList autoFocusItem={openPopper} id="links-menu" aria-labelledby="links-button">
                  {links.map((link) => (
                    <MenuItem key={link?.url} className={classes.MenuItem} onClick={handlePopperClose}>
                      <Link
                        className={classes.Link}
                        href={toOpennableUrl(link.url) as any}
                        target="_blank"
                        onClick={handleLinkClick}
                      >
                        <Typography className={classes.Text} variant="body1" textAlign="center">
                          {transformURL(link.url)}
                        </Typography>
                      </Link>
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
};

export default RegistryLinks;
