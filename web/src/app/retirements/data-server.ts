import "server-only";

import {
  Asset<PERSON><PERSON>Query,
  AssetFlowQueryResponse,
  AssetFlowStatus,
  AssetType,
  TransactionType,
} from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { ssrFetch } from "../data-server";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { generateQueryParams } from "@app/utilities/fetch";

export const getRetirementsByVintages = async (
  queryParams: AssetFlowQuery,
): Promise<MaybeNothing<AssetFlowQueryResponse>> =>
  ssrFetch(
    `retirements/asset-flows?${generateQueryParams({
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      ...queryParams,
      assetType: AssetType.REGISTRY_VINTAGE,
      transactionTypes: [TransactionType.RETIREMENT],
      status: AssetFlowStatus.SETTLED,
    })}`,
  );

export { getOrganizations } from "../data-server";
