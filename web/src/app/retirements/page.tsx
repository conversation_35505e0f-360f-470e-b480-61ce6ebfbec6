import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { getRetirementsByVintages } from "./data-server";
import { Suspense } from "react";
import { AssetFlowRelation, PortalUserOrganizationResponse } from "@rubiconcarbon/shared-types";
import Error from "../error";
import RetiredTransactions from "./retired-transactions";
import { getDefaultOrganization } from "../data-server";

export default async function TransactionPage(): Promise<JSX.Element> {
  try {
    const defaultOrganization = (await getDefaultOrganization()) as PortalUserOrganizationResponse;

    if (!defaultOrganization) return <></>;

    const serverRetiredTransactions = await getRetirementsByVintages({
      includeRelations: [
        AssetFlowRelation.DETAILED_ASSET,
        AssetFlowRelation.RELATED_OBJECT,
        AssetFlowRelation.SOURCE,
        AssetFlowRelation.LINKS,
      ],
    });

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <RetiredTransactions {...{ id: defaultOrganization?.id, serverRetiredTransactions }} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
}
