"use client";

import { Grid } from "@mui/material";
import PageContentHeading from "../page-components/heading";
import { PortalUpdaterContextProvider } from "../portal-updater-context";
import {
  Maybe,
  MaybeNothing,
  numberFormat,
  pickFromArrayOfRecords,
  px,
  searchByKeys,
  Undefinable,
  useKeepWhileValidate,
} from "@rubiconcarbon/frontend-shared";
import {
  AssetFlowQuery,
  AssetFlowQueryResponse,
  AssetFlowRelation,
  AssetFlowResponse,
  AssetFlowStatus,
  AssetType,
  BookType,
  TransactionType,
  TrimmedProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import useTriggerRequest from "@app/hooks/useTriggerRequest";
import { ChangeEvent, MouseEvent, useContext, useMemo, useState } from "react";
import {
  GridColDef,
  GridRenderCellParams,
  GridSortDirection,
  GridSortItem,
  GridSortModel,
  GridValueFormatterParams,
  GridValueGetterParams,
  NoRowsOverlayPropsOverrides,
} from "@mui/x-data-grid";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { DataTable, customGridDefs } from "@app/components/data-table";
import { nowAsString, utcDateFormat } from "@app/utilities/date";
import { BLANK, DEFAULT_MAX_PAGING_LIMIT, THREE_HUNDRED, ZERO } from "@app/constants/common";
import { useDebounce, useUpdateEffect } from "react-use";
import PortfolioName from "@app/components/styled/portfolio-name";
import RegistryLinks from "./components/retirement-links";
import { useLogger } from "@app/providers/logging-provider";
import { PaginationModel } from "@app/types/table";
import { TABLE_GRID_FEATURE_MODE } from "@app/constants/table";
import { ProjectWithVintage } from "@app/components/styled/project-with-vintage";
import HeadingContainer from "../components/heading-container/heading-container";
import ContentContainer from "../components/content-container/content-container";
import { StoreContext } from "@app/providers/state/store";
import { ProductMapping } from "@app/constants/products";

import classes from "./styles/retired-transactions.module.scss";

type ServerSortKey = keyof typeof ServerSortMap;

type ContentProps = {
  loading: boolean;
  retiredTransactions: MaybeNothing<AssetFlowQueryResponse>;
  onSort: (field: ServerSortKey, sort: GridSortDirection) => void;
  onPage: (page: number, pageSize: number) => void;
};

type RetirementsProps = {
  id: uuid;
  serverRetiredTransactions: MaybeNothing<AssetFlowQueryResponse>;
};

type RetiredRowModel = {
  id: uuid;
  portfolio: string;
  project: string;
  country: string;
  type: string;
  source: string;
  quantity: number;
  dateFinished: string;
  links: string;
};

const { medium, mediumToLarge, large } = customGridDefs;

const Columns: GridColDef[] = [
  {
    field: "portfolio",
    headerName: "Product",
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string => params?.row?.source?.name || BLANK,
    renderCell: ({ row }: GridRenderCellParams<AssetFlowResponse>): JSX.Element => {
      const { source } = row || {};
      const id = source?.id;
      const name = source?.name;

      return (
        <>
          <Maybe condition={source?.type !== BookType.PORTFOLIO_CUSTOMER}>
            <PortfolioName
              id={id!}
              name={name!}
              includeColorChip
              color={ProductMapping?.[id!]?.color}
              sx={{ textWrap: "wrap" }}
            />
          </Maybe>
          <Maybe condition={source?.type === BookType.PORTFOLIO_CUSTOMER}>{BLANK}</Maybe>
        </>
      );
    },
    ...mediumToLarge,
  },
  {
    field: "project",
    headerName: "Project",
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse, string>): string => {
      const vintage = params?.row?.detailedAsset as TrimmedProjectVintageResponse;
      const vintageName = vintage?.name;
      const registryProjectId = vintage?.project?.registryProjectId;

      const { name } = vintage?.project || {};

      return `${name}${name ? ", " : ""}${registryProjectId}${registryProjectId ? ", " : ""}${vintageName}` || BLANK;
    },
    renderCell: ({ row }: GridRenderCellParams<AssetFlowResponse>): JSX.Element => {
      const vintage = row?.detailedAsset as TrimmedProjectVintageResponse;
      return <ProjectWithVintage vintage={vintage} />;
    },
    ...large,
  },
  {
    field: "country",
    headerName: "Country",
    cellClassName: classes.Cell,
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string =>
      (params?.row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.country?.name || BLANK,
    ...medium,
  },
  {
    field: "type",
    headerName: "Type",
    cellClassName: classes.Cell,
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string =>
      (params?.row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.projectType?.type || BLANK,
    ...medium,
  },
  {
    field: "source",
    headerName: "Source",
    cellClassName: classes.Cell,
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string =>
      (params?.row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.registryName || BLANK,
    ...medium,
  },
  {
    field: "quantity",
    headerName: "Quantity",
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): number => params?.row?.amount,
    valueFormatter: (params: GridValueFormatterParams<number>): string =>
      numberFormat(params?.value, { separator: "thousand" }) || BLANK,
    ...medium,
  },
  {
    field: "dateFinished",
    headerName: "Retired Date",
    sortComparator: (a: string, b: string): number => {
      const date1 = new Date(a).getTime();
      const date2 = new Date(b).getTime();
      return date1 - date2;
    },
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string =>
      utcDateFormat(params.row?.updatedAt?.toString(), { defaultValue: BLANK }),
    ...medium,
  },
  {
    field: "links",
    headerName: "External Confirmation",
    sortable: false,
    valueGetter: (params: GridValueGetterParams<AssetFlowResponse>): string => {
      const links = params?.row?.links || [];
      const concatedLinks = links.reduce(
        (accum, link, index) => accum.concat(index > 0 ? ", " : "").concat(link?.url),
        "",
      );
      return concatedLinks || BLANK;
    },
    renderCell: ({ row }: GridRenderCellParams<AssetFlowResponse>): JSX.Element => (
      <RegistryLinks links={row?.links || []} />
    ),
    ...medium,
  },
];

const DefaultSortModel: GridSortItem = { field: "dateFinished", sort: "desc" };

const ServerSortMap = {
  dateFinished: "updatedAt",
};

const toModel = (row: AssetFlowResponse): RetiredRowModel => ({
  id: row?.id,
  portfolio: (Columns?.find(({ field }) => field === "portfolio") as any)?.valueGetter({ row } as any) || "",
  project: (Columns?.find(({ field }) => field === "project") as any)?.valueGetter({ row } as any) || "",
  country: (Columns?.find(({ field }) => field === "country") as any)?.valueGetter({ row } as any) || "",
  type: (Columns?.find(({ field }) => field === "type") as any)?.valueGetter({ row } as any) || "",
  source: (Columns?.find(({ field }) => field === "source") as any)?.valueGetter({ row } as any) || "",
  quantity: (Columns?.find(({ field }) => field === "quantity") as any)?.valueGetter({ row } as any) || "",
  dateFinished: (Columns?.find(({ field }) => field === "dateFinished") as any)?.valueGetter({ row } as any) || "",
  links: (Columns?.find(({ field }) => field === "links") as any)?.valueGetter({ row } as any) || "",
});

const Content = ({ loading, retiredTransactions }: ContentProps): JSX.Element => {
  const { ephemeralState } = useContext(StoreContext);

  const [sortModel, setSortModel] = useState<GridSortModel>([DefaultSortModel]);
  const [searchTerm, setSearchTerm] = useState<string>();
  const [filteredIds, setFilteredIds] = useState<uuid[]>();
  const [paginationModel, setPaginationModel] = useState<PaginationModel>({ page: 0, pageSize: 100 });
  const [debounceMS, setDebounceMS] = useState<number>(THREE_HUNDRED);

  // commented out for now as server api is insufficient for filtering and export
  // const rowCount = useMemo(() => retiredTransactions?.page?.totalCount || 0, [retiredTransactions?.page?.totalCount]);
  const pagedRows = useMemo(() => retiredTransactions?.data || [], [retiredTransactions?.data]);

  const tableRows = useMemo(() => {
    if (!!filteredIds) return pagedRows?.filter(({ id }) => filteredIds.includes(id));
    return pagedRows;
  }, [filteredIds, pagedRows]);

  useDebounce(
    () => {
      if (searchTerm !== undefined) {
        const ids = pickFromArrayOfRecords(
          searchByKeys(searchTerm, pagedRows?.map(toModel), [
            "portfolio",
            "project",
            "country",
            "type",
            "source",
            "quantity",
            "dateFinished",
            "links",
          ]),
          ["id"],
        ).reduce((ids: uuid[], { id }) => [...ids, id] as uuid[], []);

        if (pagedRows?.length === ids?.length) setFilteredIds(undefined);
        else setFilteredIds(ids);
      }
    },
    debounceMS,
    [searchTerm, pagedRows, paginationModel],
  );

  useUpdateEffect(() => {
    if (loading) setDebounceMS(0);
    else setTimeout(() => setDebounceMS(THREE_HUNDRED), THREE_HUNDRED);
  }, [loading]);

  // commented out for now as server api is insufficient for filtering and export
  // useUpdateEffect(() => {
  //   const [{ field, sort } = { field: null, sort: null }] = sortModel;
  //   onSort(field as ServerSortKey, sort);
  // }, [sortModel]);

  // commented out for now as server api is insufficient for filtering and export
  // useUpdateEffect(() => {
  //   const { page, pageSize } = paginationModel;
  //   onPage(page, pageSize);
  // }, [paginationModel]);

  const handleSearch = (event: ChangeEvent<HTMLInputElement>): void => {
    event?.preventDefault();
    setSearchTerm(event?.currentTarget?.value);
  };

  const handleSearchClear = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    setSearchTerm("");
  };

  return (
    <Grid item xs={12} container direction="row" sx={{ position: "relative", flexGrow: "100%", ">*": { pt: "10px" } }}>
      <DataTable
        dataGridProps={{
          "aria-label": "User retired data grid",
          columns: Columns,
          rows: tableRows,
          components: {
            NoRowsOverlay,
          },
          slotProps: {
            noRowsOverlay: {
              message: "When you complete a retirement with Rubicon Carbon, it will appear here.",
            } as NoRowsOverlayPropsOverrides,
            toolbar: {
              searchOption: {
                value: searchTerm,
                onSearch: handleSearch,
                onClear: handleSearchClear,
              },
              ...px(
                {
                  csvOptions: tableRows?.length > 0 && {
                    allColumns: false,
                    fileName: `${ephemeralState?.organization?.name}_${nowAsString()}_Retired_Transactions`,
                  },
                },
                [false],
              ),
            },
          },
          rowHeight: 75,
          // rowCount, // commented out for now as server api is insufficient for filtering and export
          loading,
          sortingMode: TABLE_GRID_FEATURE_MODE.client, // using client for now as server api is insufficient for filtering and export
          paginationMode: TABLE_GRID_FEATURE_MODE.client, // using client for now as server api is insufficient for filtering and export
          pageSizeOptions: [10, 25, 50, 100],
          sortModel,
          paginationModel,
          onSortModelChange: setSortModel,
          onPaginationModelChange: setPaginationModel,
        }}
      />
    </Grid>
  );
};

const RetiredTransactions = ({ serverRetiredTransactions }: RetirementsProps): JSX.Element => {
  const { logger } = useLogger();
  const [offset, setOffset] = useState<number>(ZERO);
  const [limit, setLimit] = useState<number>(DEFAULT_MAX_PAGING_LIMIT);
  const [orderBy, setOrderBy] = useState<MaybeNothing<string>>("dateFinished");
  const [orderByDirection, setOrderByDirection] =
    useState<MaybeNothing<"asc_nulls_last" | "desc_nulls_last">>("desc_nulls_last");

  const {
    data: clientRetiredTransactions,
    trigger: updater,
    isMutating: loading,
  } = useTriggerRequest<AssetFlowQueryResponse, null, null, AssetFlowQuery>({
    url: `/retirements/asset-flows`,
    queryParams: {
      offset,
      limit,
      includeTotalCount: true,
      status: AssetFlowStatus.SETTLED,
      assetType: AssetType.REGISTRY_VINTAGE,
      transactionTypes: [TransactionType.RETIREMENT],
      includeRelations: [
        AssetFlowRelation.DETAILED_ASSET,
        AssetFlowRelation.RELATED_OBJECT,
        AssetFlowRelation.SOURCE,
        AssetFlowRelation.LINKS,
      ],
      ...px({ orderBys: !!orderBy ? [`${orderBy}:${orderByDirection}`] : null }, [null, undefined]),
    },
    swrOptions: {
      onSuccess: (data: Undefinable<AssetFlowQueryResponse>): void => {
        updateValue(data);
        stopValidating();
      },
      onError: (error: any) => {
        stopValidating();
        logger.error(error?.message, {});
      },
    },
  });

  const {
    value: refreshedClientRetiredTransactions,
    updateValue,
    startValidating,
    stopValidating,
  } = useKeepWhileValidate(clientRetiredTransactions, loading);

  const retiredTransactions = refreshedClientRetiredTransactions ?? serverRetiredTransactions;

  // commented out for now as server api is insufficient for filtering and export
  // useEffect(() => setLimit(retiredTransactions?.page.totalCount), [retiredTransactions?.page.totalCount]);

  const handleSortChange = (field: ServerSortKey, sort: GridSortDirection): void => {
    if (!!sort) {
      setOrderBy(ServerSortMap[field]);
      setOrderByDirection(`${sort}_nulls_last`);
    } else {
      setOrderBy(null);
      setOrderByDirection(null);
    }

    setTimeout(async () => {
      startValidating();
      await updater();
    });
  };

  const handlePageChange = (page: number, pageSize: number): void => {
    const {
      page: { size },
    } = retiredTransactions || { page: {} };
    const potentialOffset = page ? page * (size ?? 0) - 1 : 0;

    if (potentialOffset !== offset || pageSize !== limit) {
      if (potentialOffset !== offset) setOffset(potentialOffset);
      if (pageSize !== limit) setLimit(pageSize);

      setTimeout(async () => {
        startValidating();
        await updater();
      });
    }
  };

  return (
    <>
      <HeadingContainer>
        <PortalUpdaterContextProvider updater={updater}>
          <PageContentHeading
            headingId="retirements-page-heading"
            headingText="Retirements"
            buttons={{ retire: true }}
          />
        </PortalUpdaterContextProvider>
      </HeadingContainer>
      <ContentContainer>
        <Content {...{ loading, retiredTransactions, onSort: handleSortChange, onPage: handlePageChange }} />
      </ContentContainer>
    </>
  );
};

export default RetiredTransactions;
