import { customGridDefs, DataTable } from "@app/components/data-table";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { SingleAssetProductName } from "@app/components/styled/product-name";
import { GridColDef, GridRenderCellParams, GridValueGetterParams } from "@mui/x-data-grid";
import { RctAssetResponse, VintageAssetResponse } from "@rubiconcarbon/shared-types";

type PurchaseAssetSummaryProps = {
  assets: (RctAssetResponse | VintageAssetResponse)[];
};

const { medium, large } = customGridDefs;

const Columns: GridColDef[] = [
  {
    field: "product",
    headerName: "Product",
    valueGetter: ({ row }: GridValueGetterParams<RctAssetResponse | VintageAssetResponse>): string => {
      return Object.hasOwn(row, "rct")
        ? (row as RctAssetResponse)?.rct?.name
        : `${(row as VintageAssetResponse)?.projectVintage?.name} - ${(row as VintageAssetResponse)?.projectVintage?.project?.name} - ${(row as VintageAssetResponse)?.projectVintage?.project?.registryProjectId}`;
    },
    renderCell: ({ row }: GridRenderCellParams<RctAssetResponse | VintageAssetResponse>): JSX.Element => (
      <SingleAssetProductName asset={row} />
    ),
    ...large,
  },
  {
    field: "productType",
    headerName: "Product Type",
    valueGetter: ({ row }: GridValueGetterParams<RctAssetResponse | VintageAssetResponse>): string =>
      Object.hasOwn(row, "rct") ? "Portfolio" : "Project",
    ...medium,
  },
  {
    field: "amount",
    headerName: "Quantity",
    type: "number",
    ...medium,
  },
];

const PurchaseAssetSummary = ({ assets }: PurchaseAssetSummaryProps): JSX.Element => {
  return (
    <DataTable
      dataGridProps={{
        getRowId: (row: RctAssetResponse | VintageAssetResponse) =>
          Object.hasOwn(row, "rct")
            ? (row as RctAssetResponse)?.rct?.id
            : (row as VintageAssetResponse)?.projectVintage?.id,
        "aria-label": "purchase asset summary data grid",
        columns: Columns,
        rows: assets,
        columnBuffer: 5,
        initialState: {
          sorting: {
            sortModel: [{ field: "amount", sort: "desc" }],
          },
        },
        components: {
          NoRowsOverlay,
        },
        slotProps: {
          toolbar: {},
        },
      }}
    />
  );
};

export default PurchaseAssetSummary;
