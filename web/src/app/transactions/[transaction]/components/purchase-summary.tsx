import InfoDetailsContainer from "@app/components/info-details";
import InfoDetailsGroup from "@app/components/info-details/components/group";
import InfoDetailsItem from "@app/components/info-details/components/item";
import StatusChip from "@app/components/styled/status-chip";
import { TransactionSettlementTypeUILabel } from "@app/constants/transactions";
import useIsMobile from "@app/providers/mobile";
import { utcDateFormat } from "@app/utilities/date";
import { capitalize } from "@mui/material";
import { calculator, currencyFormat, numberFormat } from "@rubiconcarbon/frontend-shared";
import { PurchaseResponse, TransactionType } from "@rubiconcarbon/shared-types";

type PurchaseSummaryProps = {
  transaction: PurchaseResponse;
};

const PurchaseSummary = ({ transaction }: PurchaseSummaryProps): JSX.Element => {
  const isSmallAndLess = useIsMobile(899);

  return (
    <InfoDetailsContainer columnGap={!isSmallAndLess ? 4 : 0} justifyContent="space-between">
      <InfoDetailsGroup direction={{ xs: "column", md: "row" }} xs={6} md={5.7} sx={{ padding: "5px 10px" }}>
        <InfoDetailsItem label="Type" value={capitalize(TransactionType.PURCHASE)} />
        <InfoDetailsItem label="Date" value={utcDateFormat(transaction?.dateStarted?.toString())} />
        <InfoDetailsItem label="Status" value={transaction?.status && <StatusChip status={transaction?.status} />} />
        <InfoDetailsItem
          label="Risk Adjusted"
          value={
            <StatusChip
              label={transaction?.needsRiskAdjustment ? "Yes" : "No"}
              status={!!transaction?.needsRiskAdjustment}
            />
          }
        />
      </InfoDetailsGroup>
      <InfoDetailsGroup direction={{ xs: "column", md: "row" }} xs={6} md={5.7} sx={{ padding: "5px 10px" }}>
        <InfoDetailsItem
          label="Total Amount"
          value={currencyFormat(
            transaction?.assets?.reduce(
              (sum, asset) =>
                calculator(sum).add(asset?.rawPrice).add(asset?.otherFee).add(asset?.serviceFee).calculate().toNumber(),
              0,
            ),
          )}
        />
        <InfoDetailsItem label="Total Quantity" value={numberFormat(transaction?.amount)} />
        <InfoDetailsItem
          label="Settlement Type"
          value={TransactionSettlementTypeUILabel[transaction?.flowType || ""]}
        />
        <InfoDetailsItem label="" value={" "} sx={{ width: "12.5%", height: "25%" }} />
      </InfoDetailsGroup>
    </InfoDetailsContainer>
  );
};

export default PurchaseSummary;
