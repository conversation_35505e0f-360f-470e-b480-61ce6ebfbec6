import { customGridDefs, DataTable } from "@app/components/data-table";
import NoRowsOverlay from "@app/components/data-table/no-rows-overlay";
import { SingleAssetProductName } from "@app/components/styled/product-name";
import { BLANK } from "@app/constants/common";
import { GridColDef, GridRenderCellParams, GridValueGetterParams } from "@mui/x-data-grid";
import { numberFormat } from "@rubiconcarbon/frontend-shared";
import { RctAssetResponse, VintageAssetResponse } from "@rubiconcarbon/shared-types";
import { useMemo } from "react";

type RetirementAssetSummaryProps = {
  assets: (RctAssetResponse | VintageAssetResponse)[];
};

const { medium, large } = customGridDefs;

const Columns: GridColDef[] = [
  {
    field: "project",
    headerName: "Project",
    valueGetter: ({ row }: GridValueGetterParams<VintageAssetResponse>): string =>
      `${row?.projectVintage?.name} - ${row?.projectVintage?.project?.name} - ${row?.projectVintage?.project?.registryProjectId}`,
    renderCell: ({ row }: GridRenderCellParams<VintageAssetResponse>): JSX.Element => (
      <SingleAssetProductName asset={row} />
    ),
    ...large,
  },
  {
    field: "registryName",
    headerName: "Registry Name",
    valueGetter: (params: GridValueGetterParams<VintageAssetResponse>) =>
      params?.row?.projectVintage?.project?.registryName ?? "-",
    ...medium,
  },
  {
    field: "amount",
    headerName: "Quantity",
    type: "number",
    valueGetter: (params: GridValueGetterParams<VintageAssetResponse>) =>
      numberFormat(`${params?.row.amount}`, { fallback: BLANK }),
    ...medium,
  },
];

const RetirementAssetSummary = ({ assets }: RetirementAssetSummaryProps): JSX.Element => {
  const rows = useMemo(
    () =>
      assets?.flatMap((asset) =>
        Object.hasOwn(asset, "rct")
          ? ((asset as RctAssetResponse).associatedVintages ?? [])
          : [asset as VintageAssetResponse],
      ) as VintageAssetResponse[],
    [assets],
  );

  return (
    <DataTable
      dataGridProps={{
        getRowId: (row: RctAssetResponse | VintageAssetResponse) =>
          Object.hasOwn(row, "rct")
            ? (row as RctAssetResponse)?.rct?.id
            : (row as VintageAssetResponse)?.projectVintage?.id,
        "aria-label": "retirement asset summary data grid",
        columns: Columns,
        rows,
        columnBuffer: 5,
        initialState: {
          sorting: {
            sortModel: [{ field: "amount", sort: "desc" }],
          },
        },
        components: {
          NoRowsOverlay,
        },
        slotProps: {
          toolbar: {},
        },
      }}
    />
  );
};

export default RetirementAssetSummary;
