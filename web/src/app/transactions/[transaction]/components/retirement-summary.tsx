import { Attachment } from "@app/components/attachment-list/attachment-list";
import InfoDetailsContainer from "@app/components/info-details";
import InfoDetailsGroup from "@app/components/info-details/components/group";
import InfoDetailsItem from "@app/components/info-details/components/item";
import Maybe from "@app/components/maybe/maybe";
import StatusChip from "@app/components/styled/status-chip";
import { BLANK } from "@app/constants/common";
import { TransactionTypeName } from "@app/constants/transactions";
import useIsMobile from "@app/providers/mobile";
import { utcDateFormat } from "@app/utilities/date";
import { MaybeNothing, numberFormat } from "@rubiconcarbon/frontend-shared";
import { PortalDocumentQueryResponse, RetirementResponse, RetirementType } from "@rubiconcarbon/shared-types";
import { useMemo } from "react";

type RetirementSummaryProps = {
  transaction: RetirementResponse;
  documents?: MaybeNothing<PortalDocumentQueryResponse>;
};

const RetirementSummary = ({ transaction, documents }: RetirementSummaryProps): JSX.Element => {
  const isSmallAndLess = useIsMobile(899);

  const isTransfer = useMemo(() => transaction?.type === RetirementType.TRANSFER_OUTFLOW, [transaction?.type]);
  const hasTransferToAccount = useMemo(() => !!transaction?.registryAccount, [transaction?.registryAccount]);

  const typeName = ((): string => {
    switch (transaction.type) {
      case RetirementType.RETIREMENT:
        return TransactionTypeName.RETIREMENT;
      case RetirementType.TRANSFER_OUTFLOW:
        return TransactionTypeName.TRANSFER_OUTFLOW;
      default:
        return "";
    }
  })();

  return (
    <InfoDetailsContainer columnGap={!isSmallAndLess ? 1 : 0} justifyContent="space-between">
      <InfoDetailsGroup
        direction={{ xs: "column", md: "row" }}
        xs={6}
        md={5.5}
        columnGap={0}
        sx={{ padding: "5px 10px" }}
      >
        <InfoDetailsItem label="Type" value={typeName} />
        <InfoDetailsItem label="Status" value={transaction?.status && <StatusChip status={transaction?.status} />} />
        <InfoDetailsItem label="Total Quantity" value={numberFormat(transaction?.amount)} />
      </InfoDetailsGroup>
      <InfoDetailsGroup
        direction={{ xs: "column", md: "row" }}
        xs={6}
        md={5.5}
        columnGap={isTransfer && !hasTransferToAccount ? 30 : 0}
        sx={{
          padding: "5px 10px",
          justifyContent: isTransfer && !hasTransferToAccount ? "flex-start" : "space-between",
        }}
      >
        <Maybe condition={!isTransfer}>
          <InfoDetailsItem label="Visibility" value={transaction?.isPublic ? "Public" : "Private"} />
        </Maybe>
        <Maybe condition={isTransfer && hasTransferToAccount}>
          <InfoDetailsItem label="Transfer Credits to Account" value={transaction?.registryAccount} />
        </Maybe>
        <InfoDetailsItem label="Date Started" value={utcDateFormat(transaction?.dateStarted?.toString())} />
        <InfoDetailsItem label="Date Finished" value={utcDateFormat(transaction?.dateFinished?.toString())} />
      </InfoDetailsGroup>
      <Maybe condition={!isTransfer}>
        <InfoDetailsGroup
          direction={{ xs: "column", md: "row" }}
          xs={!isSmallAndLess ? 6 : 12}
          md={5.5}
          columnGap={0}
          sx={{ padding: "5px 10px" }}
        >
          <InfoDetailsItem label="Beneficiary" value={transaction?.beneficiary} fallback="No Beneficiary" />
        </InfoDetailsGroup>
        <InfoDetailsGroup
          direction={{ xs: "column", md: "row" }}
          xs={!isSmallAndLess ? 6 : 12}
          md={5.5}
          columnGap={0}
          sx={{
            padding: "5px 10px",
            "* li": {
              padding: 0,
              height: 24,
              "* a": {
                paddingTop: 0,
                paddingBottom: 0,
              },
            },
          }}
        >
          <InfoDetailsItem
            label="Attachment"
            value={!!documents?.data?.length ? <Attachment document={{ ...documents?.data?.at?.(0) } as any} /> : BLANK}
          />
        </InfoDetailsGroup>
        <InfoDetailsGroup direction={{ xs: "column", md: "row" }} xs={12} sx={{ padding: "5px 10px" }}>
          <InfoDetailsItem label="Memo" value={transaction?.memo} fallback="No Memo" />
        </InfoDetailsGroup>
      </Maybe>
    </InfoDetailsContainer>
  );
};

export default RetirementSummary;
