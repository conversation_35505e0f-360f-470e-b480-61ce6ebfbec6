import { RequirePermissions, getUser } from "@app/providers/auth-provider/server";
import ForbiddenErrorPage from "@app/components/error-pages/forbidden";
import UnauthenticatedErrorPage from "@app/components/error-pages/unauthenticated";
import { PurchaseRelations, RetirementRelations } from "@rubiconcarbon/shared-types";
import type { uuid, TransactionType } from "@rubiconcarbon/shared-types";
import { Suspense } from "react";
import { getTransaction } from "../data-server";
import Error from "@app/app/error";
import { getDocuments } from "@app/app/documents/data-server";
import TransactionDetails from "./transaction-details";

type PageProps = {
  params: Record<string, string>;
  searchParams: Record<string, string>;
};

const Relations = { ...PurchaseRelations, ...RetirementRelations };

export default async function TransactionPage({
  params: { transaction: id },
  searchParams: { type },
}: PageProps): Promise<JSX.Element> {
  try {
    const transaction = await getTransaction(
      type as Extract<TransactionType, TransactionType.PURCHASE | TransactionType.RETIREMENT>,
      id as uuid,
      {
        includeRelations: [
          Relations.ASSETS,
          Relations.CUSTOMER_PORTFOLIO,
          ...(type === "retirement" ? [Relations.RCT_VINTAGES, Relations.LINKS] : []),
        ],
      },
    );
    const documents = await getDocuments({ relatedUiKey: transaction?.uiKey });

    return (
      <RequirePermissions
        permissions={["verified"]}
        user={getUser()}
        forbidden={<ForbiddenErrorPage />}
        unauthenticated={<UnauthenticatedErrorPage />}
      >
        <Suspense>
          <TransactionDetails {...{ type: type as TransactionType, transaction, documents }} />
        </Suspense>
      </RequirePermissions>
    );
  } catch (error: any) {
    return <Error error={{ message: error?.message }} />;
  }
}
