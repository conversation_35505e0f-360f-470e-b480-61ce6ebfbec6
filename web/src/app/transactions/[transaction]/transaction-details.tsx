"use client";

import PageContentHeading from "@app/app/page-components/heading";
import COLORS from "@app/theme/colors";
import { ArrowBack, ArrowDropDownRounded, InsertDriveFileRounded } from "@mui/icons-material";
import { Box, Button, Grid, Menu, MenuItem, Stack, Typography } from "@mui/material";
import { Maybe, MaybeNothing, usePopperState } from "@rubiconcarbon/frontend-shared";
import {
  PortalDocumentQueryResponse,
  PurchaseResponse,
  RetirementResponse,
  TransactionType,
} from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { RefObject } from "react";
import PurchaseSummary from "./components/purchase-summary";
import PurchaseAssetSummary from "./components/purchase-asset-summary";
import RetirementSummary from "./components/retirement-summary";
import RetirementAssetSummary from "./components/retirement-asset-summary";
import { Attachment } from "@app/components/attachment-list/attachment-list";
import ContentContainer from "@app/app/components/content-container/content-container";
import HeadingContainer from "@app/app/components/heading-container/heading-container";

type TransactionDetailsProps = {
  type: TransactionType;
  transaction: MaybeNothing<PurchaseResponse | RetirementResponse>;
  documents: MaybeNothing<PortalDocumentQueryResponse>;
};

const TransactionDetails = ({ type, transaction, documents }: TransactionDetailsProps): JSX.Element => {
  const {
    ref: popperRef,
    popperId,
    popout,
    close: closeMenu,
    toggle: toggleMenu,
  } = usePopperState<HTMLButtonElement>({ id: "documents-menu" });

  return (
    <>
      <HeadingContainer>
        <PageContentHeading
          headingId="transactions-page-heading"
          headingText={
            <span>
              Transaction{" "}
              <Box component="span" fontWeight={450} textTransform="uppercase">
                {transaction?.uiKey}
              </Box>
            </span>
          }
        />
      </HeadingContainer>
      <ContentContainer>
        <Grid item xs={12}>
          <Stack gap={3}>
            <Typography
              variant="bodyCopy"
              color={COLORS.darkGrey}
              component="div"
              position="relative"
              display={"flex"}
              alignItems={"center"}
              lineHeight={"unset"}
            >
              <Link
                href={"/transactions" as any}
                style={{ display: "inline-flex", color: "inherit", textDecoration: "none" }}
              >
                <ArrowBack sx={{ fontSize: "inherit", marginRight: "0.75rem" }} aria-hidden="true" />
                Back to transactions
              </Link>
            </Typography>

            {/* Summary */}
            <Typography variant="bodyCopyL" fontWeight={600} component="h3" id="transaction-summary-heading">
              Summary
            </Typography>
            <Maybe condition={type === TransactionType.PURCHASE}>
              <PurchaseSummary transaction={transaction! as PurchaseResponse} />
            </Maybe>
            <Maybe condition={type === TransactionType.RETIREMENT}>
              <RetirementSummary transaction={transaction! as RetirementResponse} documents={documents} />
            </Maybe>

            {/* Table Details */}
            <Maybe condition={type === TransactionType.PURCHASE}>
              <Grid item container justifyContent="space-between">
                <Typography
                  variant="bodyCopyL"
                  fontWeight={600}
                  component="h3"
                  id="transaction-purchase-assets-summary-heading"
                >
                  Product Summary
                </Typography>
                <Box>
                  <Button
                    ref={popperRef as RefObject<HTMLButtonElement>}
                    startIcon={<InsertDriveFileRounded />}
                    endIcon={<ArrowDropDownRounded sx={{ rotate: popout ? "180deg" : "0" }} />}
                    disableRipple
                    disabled={!documents?.data?.length}
                    sx={{ borderRadius: 1 }}
                    onClick={toggleMenu}
                  >
                    Documents
                  </Button>
                  <Menu
                    id={popperId}
                    anchorEl={popperRef.current}
                    open={popout}
                    onClose={closeMenu}
                    onClick={closeMenu}
                    transformOrigin={{ horizontal: "right", vertical: "top" }}
                    anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                    slotProps={{
                      paper: {
                        sx: {
                          li: {
                            padding: "0 5px",
                            "* a > p": {
                              textDecoration: "none !important",
                              textTransform: "none !important",
                              width: "100%",
                            },
                          },
                        },
                      },
                    }}
                  >
                    {documents?.data?.map((document) => (
                      <MenuItem key={document?.id}>
                        {<Attachment document={{ as: "link", renderIcon: false, ...document }} />}
                      </MenuItem>
                    ))}
                  </Menu>
                </Box>
              </Grid>
              <PurchaseAssetSummary assets={transaction!.assets!} />
            </Maybe>
            <Maybe condition={type === TransactionType.RETIREMENT}>
              <Grid item container justifyContent="space-between">
                <Typography
                  variant="bodyCopyL"
                  fontWeight={600}
                  component="h3"
                  id="transaction-retirement-assets-summary-heading"
                >
                  Associated Vintages
                </Typography>
              </Grid>
              <RetirementAssetSummary assets={transaction!.assets!} />
            </Maybe>
          </Stack>
        </Grid>
      </ContentContainer>
    </>
  );
};

export default TransactionDetails;
