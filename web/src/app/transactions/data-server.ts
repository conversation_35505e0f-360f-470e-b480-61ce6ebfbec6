import "server-only";

import {
  PurchaseRelationsQuery,
  PurchaseResponse,
  RetirementRelationsQuery,
  RetirementResponse,
  TransactionQuery,
  TransactionQueryResponse,
  TransactionType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";
import { ssrFetch } from "../data-server";
import { generateQueryParams } from "@app/utilities/fetch";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";

export const getTransactions = async (
  queryParams: TransactionQuery = {},
): Promise<MaybeNothing<TransactionQueryResponse>> =>
  ssrFetch(
    `transactions?${generateQueryParams({
      limit: DEFAULT_MAX_PAGING_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    })}`,
  );

export const getPortfolioTransactions = async (
  queryParams: TransactionQuery = {},
): Promise<MaybeNothing<TransactionQueryResponse>> => getTransactions(queryParams);

export const getTransaction = async (
  type: Extract<TransactionType, TransactionType.PURCHASE | TransactionType.RETIREMENT>,
  id: uuid,
  queryParams: PurchaseRelationsQuery | RetirementRelationsQuery = {},
): Promise<MaybeNothing<PurchaseResponse | RetirementResponse>> =>
  ssrFetch(
    `${type}s/${id}?${generateQueryParams({
      ...queryParams,
    })}`,
  );

export { getOrganizations } from "../data-server";
