import { GridAlignment } from "@mui/x-data-grid";

export const customGridDefs = {
  menu: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 0,
    minWidth: 75,
    width: 75,
  },
  xsmall: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 1,
    minWidth: 60,
  },
  small: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 1,
    minWidth: 100,
  },
  smallToMedium: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 2,
    minWidth: 125,
  },
  medium: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 2,
    minWidth: 150,
  },
  mediumToLarge: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 2.5,
    minWidth: 225,
  },
  large: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 3,
    minWidth: 300,
  },
  extraLarge: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 3,
    minWidth: 320,
  },
  flex: {
    headerAlign: "left" as GridAlignment,
    align: "left" as GridAlignment,
    flex: 3,
  },
};
