import {
  PortalBookQueryResponse,
  BookType,
  PortalUserOrganizationQueryResponse,
  PortalUserOrganizationResponse,
} from "@rubiconcarbon/shared-types";
import { useMemo, useState } from "react";
import CustomerAssistanceDialog from "./customer-assistance-dialog";
import { ImageMinitronProductsData, reconcileProduct } from "@app/constants/image-minitron-products-data";
import ImageMinitron from "../image-minitron/image-minitron";
import GenericDialog from "../generic-dialog/generic-dialog";
import { CloseRounded } from "@mui/icons-material";
import useTriggerRequest from "@app/hooks/useTriggerRequest";
import { Minitron } from "@app/types/static-content";
import { useUpdateEffect } from "react-use";
import { currencyFormat, Maybe, px } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@app/providers/logging-provider";
import { DEFAULT_MAX_PAGING_LIMIT } from "@app/constants/common";
import { BookFetchQueryParams } from "@app/app/data-server";
import { ProductIds, ProductMapping } from "@app/constants/products";
import EmptySlot from "../empty-slot/empty-slot";
import ProductPriceDisplay from "../product-price-display/product-price-display";
import ProductTitle from "../product-title/product-title";
import { Stack } from "@mui/material";

import classes from "./styles/product-purchase-modal.module.scss";
import { DoubleHexagonIcon, IndustrialPollutantIcon, StockChartIcon, TreesIcon } from "../custom-icons";

interface ProductPurchaseModalProps {
  open: boolean;
  handleClose: (event: object, reason: string) => void;
}

export default function ProductPurchaseModal({ open, handleClose }: ProductPurchaseModalProps): JSX.Element {
  const { logger } = useLogger();
  const [canOpen, setCanOpen] = useState<boolean>(false);
  const [organization, setOrganization] = useState<PortalUserOrganizationResponse>();
  const [products, setProducts] = useState<Minitron[]>([]);

  const hasOrg = useMemo(() => !!organization, [organization]);
  const hasProducts = useMemo(() => !!products?.length, [products]);

  const {
    trigger: getOrganizations,
    isMutating: loadingOrganization,
    error: organizationError,
  } = useTriggerRequest<PortalUserOrganizationQueryResponse>({
    url: "/organizations",
    swrOptions: {
      onSuccess: async (data) => {
        setOrganization(data?.data?.[0]);
        await getBooks();
        setCanOpen(true);
      },
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  const {
    trigger: getBooks,
    isMutating: loadingBooks,
    error: booksError,
  } = useTriggerRequest<PortalBookQueryResponse, null, BookFetchQueryParams>({
    url: "/books",
    queryParams: {
      limit: DEFAULT_MAX_PAGING_LIMIT,
      types: [BookType.PORTFOLIO_PUBLIC], // todo: (books) should we add custom portfolios here?
    },
    swrOptions: {
      onSuccess: (response) => {
        const books = response?.data || [];

        const products = [
          ...(books
            ?.map((book) => {
              const product = reconcileProduct(book, ImageMinitronProductsData);
              return {
                ...product,
                title: (
                  <ProductTitle value={product?.title} trademark={product?.text !== ProductIds.RUBICON_RATED_TONNE} />
                ),
                bottomSlot: (
                  <Stack justifyContent="space-between" alignItems="center" direction="row">
                    <Maybe condition={ProductIds.NATURE === product?.text}>
                      <TreesIcon />
                    </Maybe>
                    <Maybe condition={ProductIds.SUPERPOLLUTANT_ELIMINATION === product?.text}>
                      <IndustrialPollutantIcon />
                    </Maybe>
                    <Maybe condition={ProductIds.CARBON_REMOVALS === product?.text}>
                      <DoubleHexagonIcon />
                    </Maybe>
                    <Maybe condition={ProductIds.RUBICON_RATED_TONNE === product?.text}>
                      <StockChartIcon />
                    </Maybe>
                    <ProductPriceDisplay value={currencyFormat(product?.bottomSlot?.toString() ?? "")} />
                  </Stack>
                ),
              };
            })
            .filter((product) => !!product) as Minitron[]),
          // Add BYO
          {
            ...ImageMinitronProductsData[4],
            title: <ProductTitle value={ImageMinitronProductsData[4]?.title} />,
            bottomSlot: (
              <EmptySlot
                sx={{
                  display: "flex",
                  height: "36px",
                }}
              />
            ),
          },
        ].sort((a, b) => ProductMapping?.[a.text]?.order - ProductMapping?.[b.text]?.order);

        setProducts(
          products
            .filter(({ link }) => {
              const linkId = link?.split("/")?.slice(-1)?.[0];
              return !!linkId;
            })
            .map((product) => ({
              ...product,
              link: `${product.link}/${product.link?.endsWith("byo-rct") ? "" : "purchase"}`,
            })),
        );
      },
      onError: (error: any) => {
        logger.error(error?.message, {});
      },
    },
  });

  useUpdateEffect(() => {
    if (open) getOrganizations();
    else setCanOpen(false);
  }, [open]);

  return (
    <Maybe condition={!loadingOrganization && !organizationError && canOpen}>
      <Maybe condition={!hasOrg}>
        <CustomerAssistanceDialog text="purchasing" open={canOpen} handleClose={handleClose}></CustomerAssistanceDialog>
      </Maybe>
      <Maybe condition={!!hasOrg}>
        <GenericDialog
          open={!loadingBooks && !booksError && hasProducts && canOpen}
          title="Select Portfolio To Continue"
          dismissIcon={<CloseRounded />}
          negativeAction
          onNegativeClick={handleClose as any}
          onClose={handleClose}
          classes={{
            root: classes.Root,
            title: classes.Title,
            content: classes.Content,
          }}
        >
          {products.map((product) => (
            <ImageMinitron
              key={product.text}
              classes={{ link: classes.LinkTagContainer, tag: classes.LinkTag }}
              data={product}
              style={{
                ...px({
                  borderColor: product?.text === ProductIds.RUBICON_RATED_TONNE && "#9747FF",
                  "&:hover": product?.text === ProductIds.RUBICON_RATED_TONNE && {
                    background: `linear-gradient(${
                      !product?.disabled
                        ? "180deg, rgba(151, 71, 255, 0.5) 0%, rgba(176, 116, 255, 0.26) 100%"
                        : "rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)"
                    }), url(${product?.image?.src ?? ""})`,
                    backgroundSize: "280%",
                    backgroundPosition: "center center",
                    borderColor: "#9747FF",
                  },
                }),
              }}
            />
          ))}
        </GenericDialog>
      </Maybe>
    </Maybe>
  );
}
