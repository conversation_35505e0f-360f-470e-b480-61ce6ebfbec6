import { useState, useMemo, useContext } from "react";
import RetirementRequestDialog from "./retirement/request";
import Retirement2FA from "./retirement/2fa";
import RetirementConfirmation from "./retirement/confirmation";
import Dialog from "@mui/material/Dialog";
import {
  RetirementRequest,
  RetirementResponse,
  GroupedAllocationWithNestedResponse,
  AssetType,
  AssetTypeFilterQuery,
} from "@rubiconcarbon/shared-types";
import CustomerAssistanceDialog from "./customer-assistance-dialog";
import useTriggerRequest from "@app/hooks/useTriggerRequest";
import { useUpdateEffect } from "react-use";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@app/providers/logging-provider";
import { StoreContext } from "@app/providers/state/store";

enum RetirementStep {
  REQUEST,
  VERIFY2FA,
  CONFIRMATION,
}

interface RetirementRequestModalProps {
  open: boolean;
  handleClose: (event: object, reason: string) => void;
}

export default function RetirementRequestModal({ open, handleClose }: RetirementRequestModalProps): JSX.Element {
  const { logger } = useLogger();
  const { ephemeralState } = useContext(StoreContext);
  const { organization } = ephemeralState;

  const [step, setStep] = useState(RetirementStep.REQUEST);
  const [canOpen, setCanOpen] = useState<boolean>(false);
  const [request, setRequest] = useState<RetirementRequest>();
  const [retirement, setRetirement] = useState<RetirementResponse>();

  const {
    data: holdings,
    trigger: getOrganizationHoldings,
    isMutating: loadingOrganizationHoldings,
    error: organizationHoldingsError,
  } = useTriggerRequest<GroupedAllocationWithNestedResponse, null, AssetTypeFilterQuery>({
    url: `/organizations/${organization?.id}/holdings`,
    queryParams: {
      assetTypes: [AssetType.RCT],
    },
    swrOptions: {
      onSuccess: () => setCanOpen(true),
      onError: (error: any) => logger.error(error?.message, {}),
    },
  });

  const rcts = useMemo(
    () =>
      (holdings?.allocations || [])
        ?.filter(({ amountAvailable }) => amountAvailable > 0)
        ?.sort((a, b) => a!.detailedAsset!.name!.localeCompare(b!.detailedAsset!.name!)),
    [holdings?.allocations],
  );

  const hasRetirableProducts = useMemo(() => !!rcts?.length, [rcts?.length]);
  const canTransact = useMemo(() => open && hasRetirableProducts, [hasRetirableProducts, open]);

  useUpdateEffect(() => {
    if (open) getOrganizationHoldings();
    else setCanOpen(false);
  }, [open]);

  useUpdateEffect(() => {
    if (canTransact) setStep(RetirementStep.REQUEST);
  }, [canTransact]);

  const advanceTo2FA = (payload: RetirementRequest): void => {
    setRequest(payload);
    setStep(RetirementStep.VERIFY2FA);
  };

  const advanceToConfirmation = (tx: RetirementResponse): void => {
    setRetirement(tx);
    setStep(RetirementStep.CONFIRMATION);
  };

  const StepElement = (): JSX.Element => {
    switch (step) {
      case RetirementStep.REQUEST:
        return <RetirementRequestDialog rcts={rcts} handleClose={handleClose} advance={advanceTo2FA} />;
      case RetirementStep.VERIFY2FA:
        return request ? (
          <Retirement2FA handleClose={handleClose} advance={advanceToConfirmation} retirement={request} />
        ) : (
          <></>
        );
      case RetirementStep.CONFIRMATION:
        return retirement ? <RetirementConfirmation handleClose={handleClose} retirement={retirement} /> : <></>;
    }
  };

  return (
    <Maybe condition={!organizationHoldingsError && !loadingOrganizationHoldings && canOpen}>
      <Maybe condition={!canTransact}>
        <CustomerAssistanceDialog text="retiring" open={canOpen} handleClose={handleClose} />
      </Maybe>
      <Maybe condition={canTransact}>
        <Dialog
          open={canOpen}
          onClose={handleClose}
          maxWidth={step === RetirementStep.CONFIRMATION ? "md" : "sm"}
          fullWidth
        >
          <StepElement />
        </Dialog>
      </Maybe>
    </Maybe>
  );
}
