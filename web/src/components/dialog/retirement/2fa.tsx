import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Controller, useForm } from "react-hook-form";
import { IsNumberString, Length } from "class-validator";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { useState } from "react";
import { useAuth } from "@app/providers/auth-provider";
import TextField from "@mui/material/TextField";
import { useTheme } from "@mui/material/styles";
import Button from "@mui/material/Button";
import DialogActions from "@mui/material/DialogActions";
import CircularProgress from "@mui/material/CircularProgress";
import {
  RetirementRequest,
  RetirementResponse,
  TransactionType,
  UserActionDataType,
} from "@rubiconcarbon/shared-types";
import { useAxios } from "@app/providers/axios-provider";
import COLORS from "@app/theme/colors";

class VerificationCodeModel {
  @Length(6, 6, { message: "Verification code must be a 6-digit number" })
  @IsNumberString({}, { message: "Verification Code must be a 6-digit number" })
  verificationCode: string;
}

const resolver = classValidatorResolver(VerificationCodeModel);

export default function Retirement2FA({
  handleClose,
  advance,
  retirement,
}: {
  handleClose: (event: object, reason: string) => void;
  advance: (tx: RetirementResponse) => void;
  retirement: RetirementRequest;
}): JSX.Element {
  const { user } = useAuth();

  const {
    handleSubmit,
    formState: { errors, isValid },
    control,
  } = useForm<VerificationCodeModel>({ resolver, mode: "onBlur" });

  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);
  const [newTokenRequestInFlight, setNewTokenRequestInFlight] = useState<boolean>(false);
  const [newTokenRequestSuccess, setNewTokenRequestSuccess] = useState<boolean>(false);
  const [newTokenRequestFail, setNewTokenRequestFail] = useState<boolean>(false);
  const [tokenCheckFail, setTokenCheckFail] = useState<boolean>(false);

  const { api } = useAxios();

  const theme = useTheme();

  const onSubmit = async (formData: VerificationCodeModel): Promise<void> => {
    setSubmissionInFlight(true);
    setTokenCheckFail(false);

    try {
      const result = (
        await api.post<RetirementRequest, { data: RetirementResponse }>("/retirements", {
          ...retirement,
          type: TransactionType.RETIREMENT,
          token: formData.verificationCode,
        })
      ).data;

      const action: UserActionDataType = {
        version: 1,
        type: "rct_retirement_request",
        data: { retirementId: result.id },
      };
      api.post("/user-actions", action);

      advance(result);
    } catch (err: unknown) {
      setTokenCheckFail(true);
      setTimeout(() => setSubmissionInFlight(false), 2_500);
      setTimeout(() => setTokenCheckFail(false), 5_000);
    }
  };

  const cancel = (event: object): void => {
    handleClose(event, "closed");
  };

  const requestNewToken = async (): Promise<void> => {
    setNewTokenRequestInFlight(true);

    try {
      await api.post("/retirements", { ...retirement, type: TransactionType.RETIREMENT });
      setNewTokenRequestInFlight(false);
      setNewTokenRequestSuccess(true);
      setTimeout(() => setNewTokenRequestSuccess(false), 10_000);
    } catch (err: unknown) {
      setNewTokenRequestInFlight(false);
      setNewTokenRequestFail(true);
    }
  };

  return (
    <>
      <DialogTitle textAlign="center" component="h2">
        Email verification
      </DialogTitle>
      <DialogContent>
        <IconButton
          aria-label="Close"
          onClick={(event: object): void => handleClose(event, "clicked")}
          sx={{
            position: "absolute",
            top: "1.2rem",
            right: "1.2rem",
          }}
        >
          <CloseIcon />
        </IconButton>

        <Typography variant="bodyCopyS" component="p">
          In order to proceed with this request, we have sent an email summary with a verification code to{" "}
          <Typography variant="bodyCopySBold">{user?.email}</Typography>. (Make sure to check your spam folder if you do
          not see it in your inbox.)
        </Typography>
        <Typography variant="bodyCopyS" component="p" pt="0.5rem">
          Please enter the code below to continue.
        </Typography>
        <Box marginTop="2.2rem">
          <form id="retirement-request-2fa-form" onSubmit={handleSubmit(onSubmit)}>
            <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
              <Grid container gap={3} flexDirection="column">
                <Controller
                  control={control}
                  name="verificationCode"
                  render={({ field }): JSX.Element => (
                    <TextField
                      {...field}
                      label="Verification code"
                      fullWidth
                      inputProps={{ maxLength: 6 }}
                      error={!!errors.verificationCode}
                      helperText={errors.verificationCode?.message}
                      value={field.value ?? ""}
                    />
                  )}
                />
                <Box aria-live="assertive">
                  <Box
                    display="flex"
                    alignItems="center"
                    height="1.5rem" // matches spinner height
                  >
                    {newTokenRequestInFlight && (
                      <>
                        <CircularProgress size="1.5rem" sx={{ ml: "0.5rem", mr: "0.25rem" }} />
                        <span id="new-token-request-spinner" style={{ marginLeft: "5px" }}>
                          Requesting new code, please wait...
                        </span>
                      </>
                    )}
                    {newTokenRequestSuccess && (
                      <Typography variant={"caption"}>New verification code sent; please check your email</Typography>
                    )}
                    {newTokenRequestFail && (
                      <Typography variant={"caption"} color={COLORS.red}>
                        Error sending new code. Please try again later.
                      </Typography>
                    )}
                    {tokenCheckFail && (
                      <Typography variant={"caption"} color={COLORS.red}>
                        Error verifying code. Please try again.
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Grid>
            </fieldset>
          </form>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          disabled={newTokenRequestInFlight || submissionInFlight}
          onClick={requestNewToken}
          sx={
            newTokenRequestInFlight
              ? {
                  cursor: "not-allowed",
                  color: theme.palette.text.disabled,
                  textDecorationColor: `${theme.palette.text.disabled} !important`,
                }
              : {}
          }
        >
          Request new Code
        </Button>
        <span style={{ flexGrow: 1 }}></span>
        <Button variant="text" onClick={cancel} disabled={submissionInFlight}>
          Cancel
        </Button>
        <Button
          variant="contained"
          type="submit"
          form="retirement-request-2fa-form"
          sx={{ px: 3.5 }}
          disabled={submissionInFlight || newTokenRequestInFlight || !isValid}
        >
          Verify code
        </Button>
      </DialogActions>
    </>
  );
}
