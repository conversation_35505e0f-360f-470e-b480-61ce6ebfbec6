import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import { RetirementResponse } from "@rubiconcarbon/shared-types";
import StatusChip from "@app/components/styled/status-chip";
import PortfolioStatusStepper from "./portfolio-status-stepper";
import Link from "next/link";
import { utcDateFormat } from "@app/utilities/date";
import { DATE_WITH_TIME_FORMAT } from "@app/constants/common";
import { MultiAssetProductName } from "@app/components/styled/product-name";
import { TransactionStatus } from "@app/constants/transactions";

function integerFormat(x: number | undefined): string {
  return x !== undefined ? x.toLocaleString() : "";
}

export default function RetirementConfirmation({
  handleClose,
  retirement,
}: {
  handleClose: (event: object, reason: string) => void;
  retirement: RetirementResponse;
}): JSX.Element {
  return (
    <>
      <DialogTitle>Retirement successfully submitted</DialogTitle>
      <DialogContent>
        <IconButton
          aria-label="Close"
          onClick={(event: object): void => handleClose(event, "closed")}
          sx={{
            position: "absolute",
            top: "1.2rem",
            right: "1.2rem",
          }}
        >
          <CloseIcon />
        </IconButton>

        <Grid container spacing={1} columns={12}>
          <Grid item container xs={12} sm={6} md={6} spacing={2}>
            <Grid item xs={12}>
              <Typography variant="bodyCopyS" component="p">
                Below are the details of your request.
              </Typography>
            </Grid>
            <Grid item container xs={12}>
              <Grid item xs={5}>
                <Typography variant="bodyCopyS" component="p">
                  Transaction key:
                </Typography>
              </Grid>
              <Grid item xs={7}>
                <Typography variant="bodyCopyS" component="p">
                  {retirement.uiKey}
                </Typography>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              {retirement.status && <StatusChip status={retirement.status} />}
            </Grid>
            <Grid item container xs={12}>
              <Grid item xs={5}>
                <Typography variant="bodyCopyS" component="p">
                  Placed on:
                </Typography>
              </Grid>
              <Grid item xs={7}>
                <Typography variant="bodyCopyS" component="p">
                  {utcDateFormat(retirement.dateStarted as unknown as string, { as: DATE_WITH_TIME_FORMAT })}
                </Typography>
              </Grid>
            </Grid>
            <Grid item container xs={12}>
              <Grid item xs={5}>
                <Typography variant="bodyCopyS" component="p">
                  Submitted by:
                </Typography>
              </Grid>
              <Grid item xs={7}>
                <Typography variant="bodyCopyS" component="p">
                  {retirement.requestedBy?.name}
                </Typography>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="title" component="h3">
                Impact details
              </Typography>
            </Grid>
            <Grid item container xs={12} spacing={1}>
              <Grid item xs={12}>
                <Typography variant="elegant" component="p">
                  Product
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <MultiAssetProductName assets={retirement?.assets} style={{ fontSize: 14 }} />
              </Grid>
            </Grid>
            <Grid item container xs={12} spacing={1}>
              <Grid item container xs={6} spacing={1}>
                <Grid item xs={12}>
                  <Typography variant="elegant" component="p">
                    Quantity retired
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="bodyCopyS" component="p">
                    {integerFormat(retirement.amount)} RCTs
                  </Typography>
                </Grid>
              </Grid>
              <Grid item container xs={6} spacing={1}>
                <Grid item xs={12}>
                  <Typography variant="elegant" component="p">
                    Beneficiary
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="bodyCopyS" component="p">
                    {retirement.beneficiary}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid item container xs={12} spacing={1}>
              <Grid item container xs={12} spacing={1} alignContent="flex-start">
                <Grid item xs={12}>
                  <Typography variant="elegant" component="p">
                    Visibility
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="bodyCopyS" component="p">
                    {retirement?.isPublic ? "Public" : "Private"}
                  </Typography>
                </Grid>
              </Grid>
              <Grid item container xs={12} spacing={1}>
                <Grid item xs={12}>
                  <Typography variant="elegant" component="p">
                    Memo
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="bodyCopyS" component="p" sx={{ whiteSpace: "pre-line" }}>
                    {retirement.memo}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          <Grid item container xs={12} sm={6} md={6} spacing={2} alignContent="flex-start">
            <Grid item xs={12}>
              <Typography variant="title" component="h3">
                Next Steps
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="bodyCopyS" component="p">
                You can view the status of your retirement by clicking the{" "}
                <Link href="/transactions">Transactions</Link> tab.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <PortfolioStatusStepper status={retirement.status as unknown as TransactionStatus} />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="bodyCopyS" component="p" fontWeight={700}>
                Retirement processing time is up to 5 business days.
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Link href={`/transactions/${retirement.id}?type=retirement`} passHref style={{ marginRight: "10px" }}>
          <Button variant="text">View details</Button>
        </Link>
        <Button variant="contained" onClick={(event: object): void => handleClose(event, "closed")}>
          Close
        </Button>
      </DialogActions>
    </>
  );
}
