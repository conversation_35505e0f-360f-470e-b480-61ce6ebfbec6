import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Box from "@mui/material/Box";
import COLORS from "@app/theme/colors";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import {
  AllocationResponse,
  AssetType,
  RetirementRequest as RetirementRequestType,
  RetirementType,
  uuid,
} from "@rubiconcarbon/shared-types";
import PortfolioName from "@app/components/styled/portfolio-name";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import { useState } from "react";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormHelperText from "@mui/material/FormHelperText";
import Select from "@mui/material/Select";
import { useForm, Controller } from "react-hook-form";
import { IsNotEmpty, Min, ValidateIf } from "class-validator";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { NumericFormat } from "react-number-format";
import TextField from "@mui/material/TextField";
import { Transform, plainToClass } from "class-transformer";
import { useAxios } from "@app/providers/axios-provider";
import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import MemoFormControl from "@app/components/memo-form/memo-form";
import { MaxOfField, numberFormat, toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import { Stack, Tooltip } from "@mui/material";
import { InfoRounded } from "@mui/icons-material";
import { ProductMapping } from "@app/constants/products";

type RequestDialog = {
  rcts: AllocationResponse[];
  handleClose: (event: object, reason: string) => void;
  advance: (payload: RetirementRequestType) => void;
};

export enum RetirementVisibility {
  PUBLIC = "Public",
  PRIVATE = "Private",
}

class RetirementRequestModel {
  _maxAmount?: number;

  @IsNotEmpty({ message: "Visibility is required" })
  visibility: RetirementVisibility;

  @IsNotEmpty({ message: "Product is required" })
  product: uuid;

  @MaxOfField<RetirementRequestModel>("_maxAmount", {
    message: "The amount to retire cannot exceed the available credits",
  })
  @Min(1, { message: "The amount to retire must be at least 1 credit" })
  @Transform(({ value }: { value: string | undefined }) => {
    return value !== undefined ? Number(`${value}`.replaceAll(",", "")) : undefined;
  })
  amount: number;

  @IsNotEmpty({ message: "Beneficiary is required" })
  beneficiary: string;

  @IsNotEmpty({ message: "Memo is required for public retirements" })
  @ValidateIf((o) => o.visibility == RetirementVisibility.PUBLIC)
  memo: string;
}

const resolver = classValidatorResolver(RetirementRequestModel);

export default function RetirementRequest({ rcts, handleClose, advance }: RequestDialog): JSX.Element {
  const [maxAmount, setMaxAmount] = useState<number>();
  const [visibilitySelection, setVisibilitySelection] = useState<RetirementVisibility>();

  const { api } = useAxios();

  const {
    handleSubmit,
    formState: { errors, isValid },
    control,
    setValue,
    getValues,
    trigger,
  } = useForm<RetirementRequestModel>({ resolver, mode: "onChange" });

  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);
  const [submissionFailed, setSubmissionFailed] = useState<boolean>(false);

  if (!rcts?.length) return <></>;

  const onSubmit = async (formData: RetirementRequestModel): Promise<void> => {
    setSubmissionInFlight(true);

    const data = plainToClass(RetirementRequestModel, formData);

    /**
     * this current flow allows for only portfolio single-asset retirements
     * flow needs to be updated to account for vintages and multi-asset retirements
     */
    const payload: RetirementRequestType = {
      assetType: AssetType.RCT,
      assets: [
        {
          assetId: data?.product,
          amount: toNumber(data?.amount),
          rawPrice: toDecimal(0.0),
        },
      ],
      type: RetirementType.RETIREMENT,
      beneficiary: data.beneficiary,
      memo: data.memo,
      isPublic: data.visibility === RetirementVisibility.PUBLIC,
    };

    try {
      await api.post("/retirements", payload);
      advance(payload);
    } catch (err: unknown) {
      setSubmissionFailed(true);
    }
  };

  const onClearError = (): void => {
    setSubmissionInFlight(false);
    setSubmissionFailed(false);
  };

  return (
    <>
      <DialogTitle textAlign="center" component="h2">
        New Retirement Request
      </DialogTitle>
      {!submissionFailed && (
        <>
          <DialogContent>
            <IconButton
              aria-label="Close"
              onClick={(event: object): void => handleClose(event, "closed")}
              sx={{
                position: "absolute",
                top: "1.2rem",
                right: "1.2rem",
              }}
            >
              <CloseIcon />
            </IconButton>

            <Box
              borderRadius={1}
              borderColor={COLORS.rubiconGreenExtraLight}
              bgcolor={COLORS.rubiconGreenExtraLight}
              mb="1.5rem"
              p={1.5}
            >
              <Typography
                id="available-rcts-list-header"
                component="h3"
                variant="eyebrowS"
                fontSize="1.1rem"
                mb="0.8rem"
              >
                Current Holdings
              </Typography>
              <List dense disablePadding aria-labelledby="available-rcts-list-header">
                {rcts?.map(({ amountAllocated = 0, asset }) => (
                  <ListItem disableGutters key={asset.id}>
                    <PortfolioName
                      id={asset?.id}
                      name={asset?.name}
                      includeColorChip
                      sx={{ marginRight: 0 }}
                      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                      color={ProductMapping?.[asset.id]?.color}
                    />
                    <Typography variant="bodyCopyS" component="span" lineHeight="normal">
                      {`: ${numberFormat(amountAllocated)}`}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            </Box>

            <form id="retirement-request-form" onSubmit={handleSubmit(onSubmit)}>
              <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
                <Grid container gap={3} flexDirection="column">
                  <Controller
                    name="visibility"
                    control={control}
                    render={({ field }): JSX.Element => (
                      <FormControl error={!!errors?.visibility}>
                        <InputLabel id="visibility-label">Visibility</InputLabel>
                        <Select
                          {...field}
                          labelId="visibility-label"
                          id="visibility-dropdown"
                          label="Visibility"
                          aria-describedby={`${
                            errors?.visibility ? "visibility-error-message " : ""
                          }visibility-helper-text`}
                          value={field.value ?? ""}
                          onChange={({ target }: any): void => {
                            setVisibilitySelection(target.value);
                            setValue("visibility", target.value);
                            trigger("memo");
                          }}
                        >
                          <MenuItem value={RetirementVisibility.PUBLIC}>{RetirementVisibility.PUBLIC}</MenuItem>
                          <MenuItem value={RetirementVisibility.PRIVATE}>{RetirementVisibility.PRIVATE}</MenuItem>
                        </Select>
                        {errors.visibility?.message && (
                          <FormHelperText error id="visibility-error-message">
                            {errors.visibility?.message.toString()}
                          </FormHelperText>
                        )}
                        {getValues("visibility") == RetirementVisibility.PUBLIC && (
                          <FormHelperText error={false} id="visibility-helper-text">
                            Public retirement will be viewable on the registry publicly
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  />

                  <Controller
                    name="product"
                    control={control}
                    render={({ field }): JSX.Element => (
                      <FormControl error={!!errors?.product}>
                        <InputLabel id="product-label">Product</InputLabel>
                        <Select
                          {...field}
                          labelId="product-label"
                          id="product-dropdown"
                          label="product"
                          aria-describedby={`${errors?.product ? "product-error-message " : ""}product-helper-text`}
                          value={field.value ?? ""}
                          onChange={({ target }: any): void => {
                            const currentProduct = rcts?.find((rct) => rct.asset.id == target.value);
                            const { amountAllocated = 0, amountPendingRetirement = 0 } = currentProduct || {};

                            const value = amountAllocated - amountPendingRetirement;
                            setValue("_maxAmount", value);
                            setMaxAmount(value);
                            setValue("product", target.value);
                            trigger("amount");
                          }}
                        >
                          {rcts?.map((rct) => (
                            <MenuItem key={rct.asset.id} value={rct.asset.id}>
                              <PortfolioName
                                id={rct?.asset?.id}
                                name={rct.asset?.name ?? ""}
                                includeColorChip
                                linkable={false}
                                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                                color={ProductMapping?.[rct.asset.id]?.color}
                              />
                            </MenuItem>
                          ))}
                        </Select>

                        {maxAmount && (
                          <FormHelperText
                            component={Stack}
                            direction="row"
                            alignItems="center"
                            gap={1}
                            error={false}
                            id="product-available-credits-message"
                          >
                            Total RCTs available: {numberFormat(maxAmount)}{" "}
                            <Tooltip title="Current Holding Qty - Pending Retirement Qty">
                              <InfoRounded sx={{ fontSize: 16 }} />
                            </Tooltip>
                          </FormHelperText>
                        )}
                        {errors.product?.message && (
                          <FormHelperText error id="product-error-message">
                            {errors.product?.message.toString()}
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  />

                  <Controller
                    name="amount"
                    control={control}
                    render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                      <>
                        <NumericFormat
                          customInput={TextField}
                          decimalScale={0}
                          fixedDecimalScale
                          label="Amount of credits"
                          margin="dense"
                          variant="outlined"
                          fullWidth
                          allowNegative={false}
                          error={!!errors.amount}
                          thousandSeparator
                          helperText={errors.amount?.message && <>{errors.amount?.message.toString()}</>}
                          InputProps={{ ref }}
                          value={value ?? ""}
                          {...otherProps}
                        />{" "}
                      </>
                    )}
                  />

                  <Controller
                    name="beneficiary"
                    control={control}
                    render={({ field }): JSX.Element => (
                      <TextField
                        {...field}
                        label="Beneficiary"
                        error={!!errors.beneficiary}
                        helperText={errors.beneficiary?.message}
                        value={field.value ?? ""}
                      />
                    )}
                  />

                  <MemoFormControl
                    control={control}
                    textFieldHelperText='Please indicate the retirement language you would like to include. For example: "Retired on behalf of Company ABC"'
                    fieldLabel={visibilitySelection === RetirementVisibility.PUBLIC ? "Memo" : "Memo (optional)"}
                  />
                </Grid>
              </fieldset>
            </form>
          </DialogContent>

          <DialogActions>
            <Button
              variant="text"
              onClick={(event: object): void => handleClose(event, "cancel")}
              disabled={submissionInFlight}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              form="retirement-request-form"
              sx={{ px: 3.5 }}
              disabled={submissionInFlight || !isValid}
            >
              Submit
            </Button>
          </DialogActions>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1, position: "absolute" }}
            open={submissionInFlight}
          >
            <Grid container alignContent="center" alignItems="center" direction="column" textAlign="center">
              <Grid item>
                <CircularProgress color="primary" />
              </Grid>
              <Grid item mt="1rem">
                <Typography>Submitting Request</Typography>
              </Grid>
            </Grid>
          </Backdrop>
        </>
      )}
      {submissionFailed && (
        <>
          <DialogContent>
            <IconButton
              aria-label="Close"
              onClick={(event: object): void => handleClose(event, "closed")}
              sx={{
                position: "absolute",
                top: "1.2rem",
                right: "1.2rem",
              }}
            >
              <CloseIcon />
            </IconButton>

            <Typography component="p" mb="0.8rem">
              Error ocurred while submitting retirement request.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button variant="text" onClick={(event: object): void => handleClose(event, "cancel")}>
              Cancel
            </Button>
            <Button variant="contained" onClick={onClearError} form="retirement-request-form" sx={{ px: 3.5 }}>
              Try Again
            </Button>
          </DialogActions>
        </>
      )}
    </>
  );
}
