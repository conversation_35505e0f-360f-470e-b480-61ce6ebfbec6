import {
  SUBMIT_LIVE_QUOTE_ERR_TITLE,
  SUBMIT_LIVE_QUOTE_SUCCESS_TITLE,
  SUBMIT_LIVE_QUOTE_SUCCESS,
  SUBMIT_LIVE_QUOTE_ERR,
} from "@app/constants/dialog-messages";
import ReactConfetti from "react-confetti";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import classes from "./submit-live-quote-dialog.module.scss";
import { useRouter } from "next/navigation";
import { SubmitData } from "@app/app/products/byo-rct/custom-rct/rct-request-live-quote";
import CloseIcon from "@mui/icons-material/Close";

export default function LiveQuoteSubmitDialog({
  submitData,
  onClose,
}: {
  submitData: SubmitData;
  onClose: () => void;
}): JSX.Element {
  const title = submitData.isSuccess ? SUBMIT_LIVE_QUOTE_SUCCESS_TITLE : SUBMIT_LIVE_QUOTE_ERR_TITLE;
  const msg = submitData.isSuccess ? SUBMIT_LIVE_QUOTE_SUCCESS : (submitData?.message ?? SUBMIT_LIVE_QUOTE_ERR);
  const [showConfetti, setShowConfetti] = useState<boolean>(true);
  const router = useRouter();

  useEffect(() => {
    if (submitData?.isSuccess) {
      setTimeout(() => {
        setShowConfetti(false);
      }, 5000);
    }
  }, [submitData]);

  const viewPortfolioHandler = (): void => {
    if (submitData.isSuccess && !!submitData.modelPortfolioId) {
      router.push(`/quotes/${submitData.modelPortfolioId}`);
    }
  };

  return (
    <Dialog open={submitData.isSubmit} onClose={onClose} maxWidth={"lg"}>
      <Maybe condition={showConfetti && submitData.isSuccess}>
        <ReactConfetti height={250} width={650} gravity={0.02} numberOfPieces={100} />
      </Maybe>
      <DialogTitle sx={{ backgroundColor: "white" }}>
        <IconButton
          aria-label="Close Modal"
          onClick={onClose}
          sx={{
            position: "absolute",
            top: "1.2rem",
            right: "1.2rem",
          }}
        >
          <CloseIcon />
        </IconButton>
        <Stack direction="column" sx={{ textAlign: "center" }}>
          <Maybe condition={submitData.isSuccess}>
            <Box mt={-1}>
              <CheckCircleIcon sx={{ color: "rgba(76, 175, 80, 1)", height: 37.5, width: 37.5 }} />
            </Box>
          </Maybe>
          <Typography variant="body1" component="p" className={classes.title}>
            {title}
          </Typography>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1" component="p" className={classes.msg}>
          {msg}
        </Typography>
      </DialogContent>
      <DialogActions sx={{ backgroundColor: "rgba(248, 248, 248, 1)" }}>
        <Box className={classes.btnContainer}>
          <Button className={classes.continueBtn} variant="contained" onClick={onClose}>
            Continue
          </Button>
          <Maybe condition={submitData.isSuccess}>
            <Button className={classes.viewBtn} variant="contained" onClick={viewPortfolioHandler}>
              View saved portfolio
            </Button>
          </Maybe>
        </Box>
      </DialogActions>
    </Dialog>
  );
}
