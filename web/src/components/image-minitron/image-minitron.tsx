"use client";

import { StaticComponentProps, Minitron } from "@app/types/static-content";
import Link from "next/link";
import { classcat, Maybe } from "@rubiconcarbon/frontend-shared";
import NoveltyChip from "../novelty-chip/novelty-chip";
import { MouseEvent, useState } from "react";
import { Box } from "@mui/material";

import classes from "./styles.module.scss";

const ImageMinitron = ({ data, classes: minitronClasses, style = {} }: StaticComponentProps<Minitron>): JSX.Element => {
  const { image, title, text, subText, width, height, link = "#", disabled = false, tag, bottomSlot: action } = data;
  const { root = "", link: linkClass = "", tag: tagClass = "" } = minitronClasses ?? {};

  const [hovered, setHovered] = useState<boolean>(false);

  const handleHovered = (event: MouseEvent<HTMLAnchorElement>): void => {
    event.preventDefault();
    setHovered(true);
  };

  const handleUnHovered = (event: MouseEvent<HTMLAnchorElement>): void => {
    event.preventDefault();
    setHovered(false);
  };

  return (
    <Box
      component={Link}
      href={!disabled ? (link as any) : "#"}
      className={classcat([
        classes.ImageMinitron,
        root,
        { [classes.ImageMinitronTag]: !!tag },
        { [classes.ImageMinitronDisabled]: !!disabled },
      ])}
      sx={{
        width,
        height,
        background: `linear-gradient(${
          hovered && !disabled
            ? "180deg, rgba(79, 165, 123, 0.5) 0%, rgba(154, 198, 106, 0.7) 100%"
            : "rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)"
        }), url(${image?.src ?? ""})`,
        backgroundSize: "280%",
        backgroundPosition: "center center",
        ...style,
      }}
      onMouseEnter={handleHovered}
      onMouseLeave={handleUnHovered}
    >
      <div className={classes.Content}>
        <div className={classes.Text}>{title ?? text}</div>
        <div className={classes.SubContent}>
          <div className={classes.SubText}>{subText}</div>
          <Maybe condition={!!action}>{action}</Maybe>
        </div>
      </div>
      <Maybe condition={!!tag}>
        <div className={linkClass}>
          <NoveltyChip className={tagClass} label={tag} />
        </div>
      </Maybe>
    </Box>
  );
};

export default ImageMinitron;
