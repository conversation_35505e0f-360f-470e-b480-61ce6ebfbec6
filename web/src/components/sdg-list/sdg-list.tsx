import { sdgIconMap } from "@app/components/sdg-icons";
import { <PERSON>, Grid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "@mui/material";
import { SdgTypeResponse } from "@rubiconcarbon/shared-types";
import { MouseEvent, useMemo, useState } from "react";
import Image from "next/image";
import { Maybe, MaybeNothing, px } from "@rubiconcarbon/frontend-shared";
import { MoreHorizRounded } from "@mui/icons-material";
import { useMeasure } from "react-use";

type SDGListProps = {
  sdgs: MaybeNothing<SdgTypeResponse[]>;
  component?: "grid" | "stack";
  orientation?: "horizontal" | "vertical";
  size?: number;
};

const Tootips = [
  "End poverty in all its forms everywhere",
  "End hunger, achieve food security and improved nutrition, and promote sustainable agriculture",
  "Ensure healthy lives and promote well-being for all at all ages",
  "Ensure inclusive and equitable quality education and promote lifelong learning opportunities for all",
  "Achieve gender equality and empower all women and girls",
  "Ensure availability and sustainable management of water and sanitation for all",
  "Ensure access to affordable, reliable, sustainable, and modern energy for all",
  "Promote sustained, inclusive, and sustainable economic growth, full and productive employment, and decent work for all",
  "Build resilient infrastructure, promote inclusive and sustainable industrialization, and foster innovation",
  "Reduce inequality within and among countries",
  "Make cities and human settlements inclusive, safe, resilient, and sustainable",
  "Ensure sustainable consumption and production patterns",
  "Take urgent action to combat climate change and its impacts",
  "Conserve and sustainably use the oceans, seas, and marine resources for sustainable development",
  "Protect, restore, and promote sustainable use of terrestrial ecosystems, sustainably manage forests, combat desertification, and halt and reverse land degradation and halt biodiversity loss",
  "Promote peaceful and inclusive societies for sustainable development, provide access to justice for all and build effective, accountable and inclusive institutions at all levels",
  "Strengthen the means of implementation and revitalize the Global Partnership for Sustainable Development",
];

const SDGList = ({ sdgs, component = "grid", orientation = "horizontal", size = 25 }: SDGListProps): JSX.Element => {
  const [ref, { width }] = useMeasure();
  const [el, setEl] = useState<HTMLElement>();

  const cutoff = useMemo(() => Math.ceil(width / (size * 2)), [size, width]);

  const sdgsData = useMemo(() => {
    return (sdgs ?? [])
      .map(({ id, title }) => ({
        id,
        title,
        tooltip: Tootips[id - 1],
      }))
      .sort((a, b) => a.id - b.id);
  }, [sdgs]);

  const togglePopper = (event: MouseEvent<HTMLButtonElement | HTMLDivElement>, open: boolean): void => {
    setEl(open ? event?.currentTarget : undefined);
  };

  return (
    <Box
      ref={ref}
      component={component === "grid" ? Grid : Stack}
      {...px(
        {
          item: component === "grid",
          container: component === "grid",
          direction: orientation === "horizontal" ? "row" : "column",
          alignItems: orientation === "horizontal" ? "center" : "flex-start",
        },
        [undefined, false],
      )}
      gap={1}
      width="100%"
    >
      {sdgsData.slice(0, cutoff).map(({ id, title, tooltip }) => (
        <Tooltip key={id} title={tooltip} placement="top">
          <Image key={id} alt={`icon for ${title}`} src={sdgIconMap.get(id) ?? ""} width={size} height={size} />
        </Tooltip>
      ))}
      <Maybe condition={!!cutoff && sdgsData?.length > cutoff}>
        <Box onMouseLeave={(event: MouseEvent<HTMLDivElement>) => togglePopper(event, false)}>
          <IconButton
            onMouseEnter={(event: MouseEvent<HTMLButtonElement>) => togglePopper(event, true)}
            onClick={(event: MouseEvent<HTMLButtonElement>) => togglePopper(event, true)}
          >
            <MoreHorizRounded />
          </IconButton>
          <Popper id="more-sdgs-popper" open={!!el} anchorEl={el}>
            <Grid container sx={{ backgroundColor: "#000000D9", padding: 1, borderRadius: 0.5 }} gap={1}>
              {sdgsData.slice(cutoff).map(({ id, title, tooltip }) => (
                <Tooltip key={id} title={tooltip} placement="top">
                  <Image key={id} alt={`icon for ${title}`} src={sdgIconMap.get(id) ?? ""} width={size} height={size} />
                </Tooltip>
              ))}
            </Grid>
          </Popper>
        </Box>
      </Maybe>
    </Box>
  );
};

export default SDGList;
