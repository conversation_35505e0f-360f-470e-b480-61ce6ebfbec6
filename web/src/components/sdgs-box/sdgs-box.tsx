"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Grid } from "@mui/material";
import { ProjectSdgResponse, Maybe as MaybeType } from "@rubiconcarbon/shared-types";
import Maybe from "@app/components/maybe/maybe";
import Image from "next/image";
import { sdgIconMap } from "@app/components/sdg-icons";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";

const showSDGs = (projectSDGs: MaybeType<ProjectSdgResponse[]>, height: number): JSX.Element => {
  return (
    <Grid container item spacing={1} sx={{ display: "inherit" }}>
      <Grid item xs={12} sx={{ minWidth: "100px", display: "inherit" }}>
        {!!projectSDGs &&
          projectSDGs.map((sdg) => (
            <Tooltip key={sdg.id} title={sdg?.sdgType?.title} placement="top">
              <Box key={sdg.id} sx={{ display: "inline-block", padding: "2px" }}>
                {
                  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                  <Image alt={`icon for ${sdg.sdgType.title}`} src={sdgIconMap.get(sdg.sdgTypeId)!} height={height} />
                }
              </Box>
            </Tooltip>
          ))}
      </Grid>
    </Grid>
  );
};

interface SDGsBoxProps {
  projectSDGs: MaybeType<ProjectSdgResponse[]>;
  max?: number;
  height?: number;
}

export default function SDGsBox(props: SDGsBoxProps): JSX.Element {
  const { projectSDGs, max = 6, height = 50 } = props;

  if (!projectSDGs || projectSDGs.length === 0) return <></>;

  let displaySDGs: ProjectSdgResponse[] = [];

  if (projectSDGs.length > max) {
    for (let i = 0; i < max; i++) {
      displaySDGs.push(projectSDGs[i]);
    }
  } else {
    displaySDGs = projectSDGs;
  }

  return (
    <Stack direction="row" alignItems="center" spacing={0}>
      <Box sx={{ display: "inline-flex" }}>{showSDGs(displaySDGs, height)}</Box>
      <Maybe condition={projectSDGs.length > max}>
        <Box>
          <Tooltip
            title={
              <Box width={218} sx={{ display: "inline-block" }}>
                {showSDGs(projectSDGs, height)}
              </Box>
            }
          >
            <MoreHorizIcon />
          </Tooltip>
        </Box>
      </Maybe>
    </Stack>
  );
}
