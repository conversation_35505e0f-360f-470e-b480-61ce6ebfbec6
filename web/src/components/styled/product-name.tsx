import { Maybe } from "@rubiconcarbon/frontend-shared";
import PortfolioName from "./portfolio-name";
import {
  AssetType,
  RctAssetResponse,
  TransactionResponse,
  TrimmedAssetResponse,
  uuid,
  VintageAssetResponse,
} from "@rubiconcarbon/shared-types";
import { Typography } from "@mui/material";
import { CSSProperties } from "react";
import ProjectWithVintageByAsset, { ProjectWithVintage } from "./project-with-vintage";
import { ProductMapping } from "@app/constants/products";

type SingleAssetProductNameProps = {
  asset: RctAssetResponse | VintageAssetResponse;
  style?: CSSProperties;
};

type MultiAssetProductNameProps = {
  assets: (RctAssetResponse | VintageAssetResponse)[];
  style?: CSSProperties;
};

type ProductNameProps = {
  transaction: TransactionResponse;
  style?: CSSProperties;
};

export const SingleAssetProductName = ({ asset, style = {} }: SingleAssetProductNameProps): JSX.Element => {
  const isPortfolio = Object.hasOwn(asset, "rct");

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  return (
    <>
      <Maybe condition={isPortfolio}>
        <PortfolioName
          id={(asset as RctAssetResponse)?.rct?.id}
          name={(asset as RctAssetResponse)?.rct?.name}
          includeColorChip
          color={ProductMapping?.[(asset as RctAssetResponse)?.rct?.id]?.color}
          sx={{ fontSize, fontWeight, ...rest }}
        />
      </Maybe>
      <Maybe condition={!isPortfolio}>
        <ProjectWithVintage
          vintage={(asset as VintageAssetResponse)?.projectVintage}
          sx={{ fontSize, fontWeight, ...rest }}
        />
      </Maybe>
    </>
  );
};

export const MultiAssetProductName = ({ assets, style = {} }: MultiAssetProductNameProps): JSX.Element => {
  const isMultiple = assets?.length > 1;
  const isPortfolio = assets?.some((asset) => Object.hasOwn(asset, "rct"));
  const firstAsset = assets?.at(0);

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <>
        <Maybe condition={isPortfolio}>
          <PortfolioName
            id={(firstAsset as RctAssetResponse)?.rct?.id}
            name={(firstAsset as RctAssetResponse)?.rct?.name}
            includeColorChip
            color={ProductMapping?.[(firstAsset as RctAssetResponse)?.rct?.id]?.color}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
        <Maybe condition={!isPortfolio}>
          <ProjectWithVintage
            vintage={(firstAsset as VintageAssetResponse)?.projectVintage}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
      </>
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

const ProductName = ({ transaction, style = {} }: ProductNameProps): JSX.Element => {
  const isMultiple = transaction?.assetFlows?.length > 1;
  const isPortfolio = transaction?.assetFlows?.some(({ asset }) => asset?.type === AssetType.RCT);
  const firstAsset = transaction?.assetFlows?.at(0)?.asset;

  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <>
        <Maybe condition={isPortfolio}>
          <PortfolioName
            id={firstAsset?.id as uuid}
            name={firstAsset?.name as string}
            includeColorChip
            color={ProductMapping?.[firstAsset?.id as uuid]?.color}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
        <Maybe condition={!isPortfolio}>
          <ProjectWithVintageByAsset
            asset={firstAsset as TrimmedAssetResponse}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
      </>
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

export default ProductName;
