import { Box, Stack, SxProps, Typography } from "@mui/material";
import { px } from "@rubiconcarbon/frontend-shared";
import { TrimmedAssetResponse, TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import Link from "next/link";

type ProjectWithVintageProps = {
  vintage: TrimmedProjectVintageResponse;
  linkable?: boolean;
  sx?: SxProps;
};

type ProjectWithVintageByAssetProps = {
  asset: TrimmedAssetResponse;
  linkable?: boolean;
  sx?: SxProps;
};

export const ProjectWithVintage = ({ vintage, linkable = true, sx = {} }: ProjectWithVintageProps): JSX.Element => {
  return (
    <Stack>
      <Box
        component={linkable ? Link : "span"}
        {...px({ href: linkable && `/projects/${vintage?.project?.id}`, target: linkable && "_blank" })}
        style={{ textUnderlineOffset: 3 }}
      >
        <Typography variant="body2" sx={{ ...sx, overflow: "hidden", textOverflow: "ellipsis", textWrap: "nowrap" }}>
          {vintage?.project?.name}
        </Typography>
      </Box>

      <Typography variant="body2" sx={sx} color="GrayText">
        {vintage?.project?.registryProjectId} - {vintage?.name}
      </Typography>
    </Stack>
  );
};

const ProjectWithVintageByAsset = ({
  asset,
  linkable = true,
  sx = {},
}: ProjectWithVintageByAssetProps): JSX.Element => {
  return (
    <Stack>
      <Box
        component={linkable ? Link : "span"}
        {...px({ href: linkable && `/projects/${asset?.id}`, target: linkable && "_blank" })}
        style={{ textUnderlineOffset: 3 }}
      >
        <Typography variant="body2" sx={{ ...sx, overflow: "hidden", textOverflow: "ellipsis", textWrap: "nowrap" }}>
          {asset?.name}
        </Typography>
      </Box>

      <Typography variant="body2" sx={sx} color="GrayText">
        {asset?.registryProjectId} - {asset?.label}
      </Typography>
    </Stack>
  );
};

export default ProjectWithVintageByAsset;
