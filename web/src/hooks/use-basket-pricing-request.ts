import { AxiosContext } from "@app/providers/axios-provider";
import { CustomPricingRequest, CustomPricingResponse } from "@rubiconcarbon/shared-types";
import { AxiosError } from "axios";
import { useCallback, useContext, useState } from "react";
import useS<PERSON> from "swr";
import { Bare<PERSON><PERSON>cher, KeyedMutator, PublicConfiguration } from "swr/_internal";

type UseBasketPricingRequestProps = {
  key: string;
  enable?: boolean;
  requestBody?: CustomPricingRequest;
  swrOptions?: Partial<PublicConfiguration<CustomPricingResponse, any, BareFetcher<CustomPricingResponse>>>;
};

type UseBasketPricingRequestReturn = {
  data: CustomPricingResponse | undefined;
  error: any;
  isLoading: boolean;
  isValidating: boolean;
  mutate: KeyedMutator<CustomPricingResponse>;
};

/**
 * useBasketPricingRequest
 * Project Pricing Reporting API is a POST API.
 * So to leverage SWR and all the benefits it brings, we will need to create a reusable hook extending the POST Request as a fetcher.
 * This can then under the hood act like a GET call becuase of the mutate props of SWR.
 * This hook does exactly that and we can use it anywhere like we do any other GET SWR.
 * @param | it takes
 *   key: cache key to make all request unique. this api is called at multiple places with different criteria and needs to be localized.
 *    enable: enables or disables the request
 *    requestBody: the data passed to the request
 *    swrOptions: this is basically the SWR Options we pass in a normal SWR function. it takes all the SWR options.
 * @returns | a normal SWR return object.
 */
const useBasketPricingRequest = ({
  key,
  enable = true,
  requestBody,
  swrOptions = {},
}: UseBasketPricingRequestProps): UseBasketPricingRequestReturn => {
  const { api } = useContext(AxiosContext);
  const [error, setError] = useState<any>();
  const request = useCallback(async (): Promise<CustomPricingResponse> => {
    try {
      const response = await api.post<CustomPricingResponse>(`baskets/pricing`, requestBody);
      const prices = response.data;

      setError(null);

      return prices;
    } catch (error: any) {
      const { response } = error as AxiosError;

      if (response?.status !== 401 && response?.status !== 403) {
        setError(response);
        return error;
      }

      throw error;
    }
  }, [api, requestBody]);

  const fetcher = useCallback(request, [request]);

  const { data, isLoading, isValidating, mutate } = useSWR<CustomPricingResponse>(
    enable ? key : null,
    fetcher,
    swrOptions,
  );

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  };
};

export default useBasketPricingRequest;
