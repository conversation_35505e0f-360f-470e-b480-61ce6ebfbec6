import mapboxgl, {
  <PERSON><PERSON><PERSON><PERSON>,
  LngLatBounds,
  LngLatLike,
  Marker,
  Map,
  NavigationControl,
  AnySourceData,
  FitBoundsOptions,
  MapboxEvent,
} from "mapbox-gl";
import { useMemo, useRef, useState } from "react";
import { Root, createRoot } from "react-dom/client";
import { Maybe } from "@rubiconcarbon/shared-types";
import MapFocusControl from "@app/models/map-focus-control";
import MapLocationMarker from "@app/components/map-location-marker/map-location-marker";
import { useUpdateEffect } from "react-use";
import type { FeatureCollection, Geometry, GeoJsonProperties } from "geojson";
import { bbox, multiPoint } from "@turf/turf";
import { Undefinable, isEmptyRecord, px } from "@rubiconcarbon/frontend-shared";

import "mapbox-gl/dist/mapbox-gl.css";

const DEFAULT_ZOOM = 0.7;
const MAX_PADDING = 100;
const DEFAULT_DURATION = 600;

export type MapEventMetadata = {
  width?: number;
  height?: number;
};

type Control =
  | {
      show?: boolean;
      position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
      padding?: number;
      zoom?: number;
    }
  | boolean;

type LayerStyle = {
  size?: {
    line?: number;
    circle?: number;
  };
  color?: {
    line?: string;
    circle?: string;
    fill?: string;
  };
  opacity?: {
    line?: number;
    circle?: number;
    fill?: number;
  };
};

export type BaseTileMapProps = {
  attach: boolean;
  container: string;
  accessToken?: string;
  style: string;
  projection?:
    | "albers"
    | "equalEarth"
    | "equirectangular"
    | "lambertConformalConic"
    | "mercator"
    | "naturalEarth"
    | "winkelTripel"
    | "globe";
  center?: LngLatLike;
  bounds?: LngLatBounds;
  zoom?: number;
  minZoom?: number;
  maxZoom?: number;
  boundedZoomFactor?: number;
  dragPan?: boolean;
  boundFocus?: boolean;
  initialBoundsOptions?: FitBoundsOptions;
  initialBoundFocus?: {
    padding?: number;
    zoom?: number;
    duration?: number;
  };
  control?: {
    zoom?: Control;
    compass?: Control;
    pitch?: Control;
    boundFocus?: Control;
    markerFocus?: Control;
  };
  antialias?: boolean;
  interactive?: boolean;
  multiple?: boolean;
  attribute?: boolean;
  markers?: [number, number][];
  sources?: Record<string, AnySourceData>;
  layerStyle?: LayerStyle;
  renderMarker?: (markerIndex?: number) => JSX.Element;
  onAddMarker?: (marker: [number, number]) => void;
  onLoad?: (event: MapboxEvent, metadata?: MapEventMetadata) => void;
  onResize?: (event: MapboxEvent, metadata?: MapEventMetadata) => void;
};

const MAPBOX_ACCESS_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

export const coordinatesToBounds = (coordinates: [number, number][]): Undefinable<LngLatBounds> =>
  !!coordinates.length
    ? ((): LngLatBounds => {
        const boxBound = bbox(multiPoint(coordinates));
        return new LngLatBounds([boxBound[0], boxBound[1], boxBound[2], boxBound[3]]);
      })()
    : undefined;

export const mapLayersFromGeoJSON = (
  geoJSON: FeatureCollection<Geometry, GeoJsonProperties>,
  source: string,
  style: LayerStyle,
): AnyLayer[] => {
  const layers: AnyLayer[] = [];

  const { size = {}, color = {}, opacity = {} } = style || {};
  const { circle: cSize = 15, line: lSize = 4 } = size;
  const { circle: cColor = "wheat", line: lColor = "wheat", fill: fColor = "wheat" } = color;
  const { circle: cOpacity = 1, line: lOpacity = 1, fill: fOpacity = 1 } = opacity;

  for (const [index, feature] of geoJSON.features.entries()) {
    const geometryType = feature.geometry.type;

    switch (geometryType) {
      case "Point":
        layers.push({
          id: `${geometryType}-${index}`,
          filter: ["==", "$type", geometryType],
          type: "circle",
          paint: {
            "circle-opacity": cOpacity,
            "circle-radius": cSize,
            "circle-color": cColor,
          },
          source,
        });
        break;
      case "LineString":
        layers.push({
          id: `${geometryType}-${index}`,
          filter: ["==", "$type", geometryType],
          type: "line",
          paint: {
            "line-opacity": lOpacity,
            "line-width": lSize,
            "line-color": lColor,
          },
          source,
        });
        break;
      default:
        layers.push({
          id: `${geometryType}-${index}`,
          type: "fill",
          paint: {
            "fill-opacity": fOpacity,
            "fill-color": fColor,
          },
          source,
        });
        break;
    }
  }

  return layers;
};

const useTileMap = (props: BaseTileMapProps): void => {
  const mapRef = useRef<Maybe<Map>>(null);

  const {
    attach,
    container,
    accessToken = MAPBOX_ACCESS_TOKEN,
    style,
    projection = "equirectangular",
    center,
    bounds,
    zoom = DEFAULT_ZOOM,
    minZoom: initialMinZoom = 0,
    maxZoom: initialMaxZoom = 22,
    boundedZoomFactor: zoomFactor = 0,
    dragPan = true,
    boundFocus = false,
    initialBoundsOptions = {},
    initialBoundFocus = {},
    control = {},
    antialias = true,
    interactive = false,
    multiple = true,
    attribute = false,
    markers = [],
    sources = {},
    layerStyle = {},
    renderMarker,
    onLoad,
    onResize,
  } = props;

  const mapElement = document.getElementById(container);

  const [loaded, setLoaded] = useState<boolean>(false);

  const boundedZoom = useMemo(() => {
    const _bounds = bounds || coordinatesToBounds(markers);

    if (loaded && mapRef.current && _bounds && !_bounds?.isEmpty()) {
      const map = mapRef.current;

      const mapWidth = map.getContainer().offsetWidth;
      const mapHeight = map.getContainer().offsetHeight;

      const WORLD_DIM = { height: 512, width: 512 };
      const ZOOM_MAX = 21;

      const latRad = (lat: number): number => {
        const sin = Math.sin((lat * Math.PI) / 180);
        const radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
        return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
      };

      const zoom = (mapPx: number, worldPx: number, fraction: number): number =>
        Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);

      const latFraction = (latRad(_bounds?._ne.lat) - latRad(_bounds?._sw.lat)) / Math.PI;

      const lngDiff = _bounds?._ne.lng - _bounds?._sw.lng;
      const lngFraction = ((lngDiff + 360) % 360) / 360;

      const latZoom = zoom(mapHeight, WORLD_DIM.height, latFraction);
      const lngZoom = zoom(mapWidth, WORLD_DIM.width, lngFraction);
      const maxLatLonZoom = Math.min(latZoom, lngZoom, ZOOM_MAX);

      const adjustedZoom = maxLatLonZoom - zoomFactor;

      return adjustedZoom;
    }

    return 0;
  }, [bounds, loaded, markers, zoomFactor]);

  const minZoom = useMemo(
    () => (boundFocus ? (boundedZoom - 1 < 0 ? 0 : boundedZoom - 2) : initialMinZoom),
    [boundFocus, boundedZoom, initialMinZoom],
  );

  const maxZoom = useMemo(
    () => (boundFocus ? boundedZoom + 2 : initialMaxZoom),
    [boundFocus, boundedZoom, initialMaxZoom],
  );

  const width = useMemo(() => mapElement?.offsetWidth, [mapElement?.offsetWidth]);
  const height = useMemo(() => mapElement?.offsetHeight, [mapElement?.offsetHeight]);

  const initializeMap = (): Map => {
    const map = new mapboxgl.Map({
      container,
      accessToken,
      style,
      center,
      zoom,
      antialias,
      interactive,
      dragPan,
      ...px(
        {
          minZoom: !boundFocus && minZoom,
          maxZoom: !boundFocus && maxZoom,
          bounds,
          fitBoundsOptions: !!bounds && {
            ...initialBoundsOptions,
            padding:
              typeof initialBoundsOptions.padding === "number" && initialBoundsOptions.padding > MAX_PADDING
                ? MAX_PADDING
                : (initialBoundsOptions?.padding ?? MAX_PADDING),
            zoom: initialBoundsOptions?.zoom ?? boundedZoom,
          },
        },
        [undefined, null, false],
      ),
      renderWorldCopies: multiple,
      attributionControl: attribute,
      projection: { name: projection },
    });

    return map;
  };

  const attachMakersToMap = (): (() => void) => {
    const hasMarkers = !!markers.length;
    const instances: Marker[] = [];
    const rootMarkers: Root[] = [];

    const mountTimeoutId = setTimeout(() => {
      if (mapRef.current && hasMarkers) {
        for (const marker of markers) {
          const position = document.createElement("div");
          const instance = new Marker(position).setLngLat(marker);
          instances.push(instance);
        }

        for (const [index, instance] of instances.entries()) {
          const root = createRoot(instance.getElement());
          instance.addTo(mapRef.current);
          root.render(renderMarker ? renderMarker(index) : <MapLocationMarker />);
          rootMarkers.push(root);
        }
      }
    });

    // unmount caller
    return (): void => {
      clearTimeout(mountTimeoutId);

      if (mapRef.current && hasMarkers) {
        setTimeout(() => {
          for (const marker of instances) marker.remove();
          for (const root of rootMarkers) root.unmount();
        });
      }
    };
  };

  const attachSourcesAndLayers = (): (() => void) => {
    const hasSources = !!Object.keys(sources).length;
    const sourceInstances: Record<string, AnySourceData> = {};
    const layerInstances: AnyLayer[] = [];

    const mountTimeoutId = setTimeout(() => {
      if (mapRef.current && hasSources) {
        for (const [id, source] of Object.entries(sources)) {
          sourceInstances[id] = source;
          mapRef.current.addSource(id, source);

          /**
           * only checking geojson for now.
           * we can expand it if we map other different types of features
           */
          if (source.type === "geojson") {
            const layers = mapLayersFromGeoJSON(source.data as FeatureCollection<Geometry>, id, layerStyle);

            for (const layer of layers) {
              layerInstances.push(layer);
              mapRef.current.addLayer(layer);
            }
          }
        }
      }
    });

    // unmount caller
    return (): void => {
      clearTimeout(mountTimeoutId);

      if (mapRef.current && hasSources) {
        setTimeout(() => {
          for (const instance of layerInstances) mapRef.current?.removeLayer(instance.id);
          for (const id of Object.keys(sourceInstances)) mapRef.current?.removeSource(id);
        });
      }
    };
  };

  const setBounds = (): void => {
    const { padding, zoom, duration = DEFAULT_DURATION } = initialBoundFocus;

    const { padding: boundPadding, zoom: boundZoom } = {
      padding: (padding ?? 0) > MAX_PADDING ? MAX_PADDING : padding,
      zoom: zoom || boundedZoom || DEFAULT_ZOOM,
    };

    if (mapRef.current) {
      if (!isEmptyRecord(initialBoundFocus) || boundFocus) {
        const isBoundable = !!bounds || !!markers.length;

        if (isBoundable) {
          mapRef.current.fitBounds(bounds ?? coordinatesToBounds(markers) ?? new LngLatBounds(), {
            ...(!!boundPadding ? { padding: boundPadding } : {}),
            ...(!!boundZoom ? { zoom: boundZoom } : {}),
            duration,
          });

          mapRef.current.setMinZoom(minZoom);
          mapRef.current.setMaxZoom(maxZoom);
        } else if (!!markers && !markers.length) {
          mapRef.current.easeTo({
            center: [0, 0],
            zoom: DEFAULT_ZOOM,
          });
        }
      }
    }
  };

  const attachControls = (): (() => void) => {
    const { zoom, compass, pitch, boundFocus = false, markerFocus } = control;
    const controls: (NavigationControl | MapFocusControl)[] = [];

    const mountTimeoutId = setTimeout(() => {
      const { zoom: showZoom, position: zoomPosition = "top-right" } = {
        zoom: !!zoom,
        position: typeof zoom === "boolean" ? "top-right" : zoom?.position,
      };
      const { compass: showCompass, position: compassPosition = "top-right" } = {
        compass: !!compass,
        position: typeof compass === "boolean" ? "top-right" : compass?.position,
      };
      const { pitch: visualizePitch, position: pitchPosition = "top-right" } = {
        pitch: !!pitch,
        position: typeof pitch === "boolean" ? "top-right" : pitch?.position,
      };
      const {
        boundFocus: showBoundFocus,
        position: boundPosition = "top-right",
        padding: boundPadding,
        zoom: boundZoom,
      } = {
        boundFocus: typeof boundFocus === "boolean" ? boundFocus === true : boundFocus?.show !== false,
        position: typeof boundFocus === "boolean" ? "top-right" : boundFocus?.position,
        padding:
          typeof boundFocus !== "boolean"
            ? (boundFocus?.padding ?? 0) > MAX_PADDING
              ? MAX_PADDING
              : boundFocus?.padding
            : MAX_PADDING,
        zoom:
          typeof boundFocus !== "boolean"
            ? boundFocus?.zoom || boundedZoom || DEFAULT_ZOOM
            : boundedZoom || DEFAULT_ZOOM,
      };
      const { markerFocus: showMarkerFocus, position: markerPosition } = {
        markerFocus: !!markerFocus,
        position: typeof markerFocus === "boolean" ? "top-right" : markerFocus?.position,
      };

      if (mapRef.current) {
        const map = mapRef.current;

        // set controls if any
        if (showZoom) {
          const navCtrl = new NavigationControl({ showZoom, showCompass: false, visualizePitch: false });
          controls.push(navCtrl);
          map.addControl(navCtrl, zoomPosition);
        }
        if (showCompass) {
          const navCtrl = new NavigationControl({ showZoom: false, showCompass, visualizePitch: false });
          controls.push(navCtrl);
          map.addControl(navCtrl, compassPosition);
        }
        if (visualizePitch) {
          const navCtrl = new NavigationControl({ showZoom: false, showCompass: false, visualizePitch });
          controls.push(navCtrl);
          map.addControl(navCtrl, pitchPosition);
        }
        if (showBoundFocus && (!!bounds || (!!markers && markers.length))) {
          const navCtrl = new MapFocusControl("bounds", () =>
            map.fitBounds(bounds ?? coordinatesToBounds(markers) ?? new LngLatBounds(), {
              ...(!!boundPadding ? { padding: boundPadding } : {}),
              ...(!!boundZoom ? { zoom: boundZoom } : {}),
            }),
          );
          controls.push(navCtrl);
          map.addControl(navCtrl as any, boundPosition);
        }
        if (showMarkerFocus && !!markers && markers.length === 1) {
          const navCtrl = new MapFocusControl("marker", () =>
            map.easeTo({
              duration: DEFAULT_DURATION,
              center: markers[0],
              zoom: 5,
            }),
          );
          controls.push(navCtrl);
          map.addControl(navCtrl as any, markerPosition);
        }
      }
    });

    return (): void => {
      clearTimeout(mountTimeoutId);
      setTimeout(() => {
        for (const ctrl of controls) {
          mapRef.current?.removeControl(ctrl as any);
        }
      });
    };
  };

  // [on mount]
  useUpdateEffect(() => {
    if (attach) {
      mapRef.current = initializeMap();
      mapRef.current.on("load", (event: any) => {
        setLoaded(true);

        if (!!onLoad) onLoad(event);
      });

      return (): void => {
        setLoaded(false);
        mapRef.current?.remove();
        mapRef.current = null;
      };
    }
  }, [attach]);

  // [on map load] - attaching markers
  useUpdateEffect(() => {
    if (loaded) {
      const unmount = attachMakersToMap();
      return (): void => unmount();
    }
  }, [loaded, markers]);

  // [on map load] - attaching layers
  useUpdateEffect(() => {
    if (loaded) {
      const unmount = attachSourcesAndLayers();
      return (): void => unmount();
    }
  }, [loaded, sources]);

  // [on map load] - setting bounds
  useUpdateEffect(() => {
    if (loaded) {
      setBounds();
    }
  }, [loaded, markers, bounds]);

  // [on map load] - attaching controls
  useUpdateEffect(() => {
    if (loaded) {
      const unmount = attachControls();
      return (): void => unmount();
    }
  }, [loaded, control]);

  useUpdateEffect(() => {
    if (onResize && !!mapRef?.current)
      mapRef.current.on("resize", (event) => {
        onResize(event as any, { width, height });
      });
  }, [width, height]);
};

export default useTileMap;
